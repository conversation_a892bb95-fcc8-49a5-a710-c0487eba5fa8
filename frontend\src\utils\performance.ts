/**
 * 性能监控工具
 */

// 性能指标类型
export interface PerformanceMetrics {
  // 页面加载时间
  pageLoadTime?: number
  // 首次内容绘制时间
  firstContentfulPaint?: number
  // 首次有意义绘制时间
  firstMeaningfulPaint?: number
  // 首次输入延迟
  firstInputDelay?: number
  // 累积布局偏移
  cumulativeLayoutShift?: number
  // 最大内容绘制时间
  largestContentfulPaint?: number
  // 首屏渲染时间
  firstScreenRender?: number
  // 资源加载时间
  resourceLoadTime?: Record<string, number>
  // 内存使用情况
  memoryUsage?: {
    jsHeapSizeLimit?: number
    totalJSHeapSize?: number
    usedJSHeapSize?: number
  }
  // 自定义指标
  custom?: Record<string, number>
}

// 性能监控配置
export interface PerformanceMonitorOptions {
  // 是否启用自动收集
  autoCollect?: boolean
  // 是否启用资源监控
  resourceMonitoring?: boolean
  // 是否启用内存监控
  memoryMonitoring?: boolean
  // 是否启用用户交互监控
  interactionMonitoring?: boolean
  // 是否启用错误监控
  errorMonitoring?: boolean
  // 是否启用控制台日志
  consoleLog?: boolean
  // 采样率 (0-1)
  samplingRate?: number
  // 自定义上报函数
  reportCallback?: (metrics: PerformanceMetrics) => void
}

// 默认配置
const defaultOptions: PerformanceMonitorOptions = {
  autoCollect: true,
  resourceMonitoring: true,
  memoryMonitoring: true,
  interactionMonitoring: true,
  errorMonitoring: true,
  consoleLog: false,
  samplingRate: 1.0
}

// 性能监控类
export class PerformanceMonitor {
  private options: PerformanceMonitorOptions
  private metrics: PerformanceMetrics = {}
  private resourceObserver: PerformanceObserver | null = null
  private paintObserver: PerformanceObserver | null = null
  private layoutShiftObserver: PerformanceObserver | null = null
  private largestContentfulPaintObserver: PerformanceObserver | null = null
  private firstInputObserver: PerformanceObserver | null = null
  private interactionObserver: PerformanceObserver | null = null
  private navigationObserver: PerformanceObserver | null = null
  private errorHandler: ((event: ErrorEvent) => void) | null = null
  private memoryInterval: number | null = null
  
  constructor(options: PerformanceMonitorOptions = {}) {
    this.options = { ...defaultOptions, ...options }
    
    // 初始化指标
    this.metrics = {
      custom: {}
    }
    
    // 如果启用自动收集，则开始监控
    if (this.options.autoCollect) {
      this.startMonitoring()
    }
  }
  
  /**
   * 开始性能监控
   */
  public startMonitoring(): void {
    // 检查浏览器支持
    if (!window.performance) {
      console.warn('Performance API is not supported in this browser')
      return
    }
    
    // 应用采样率
    if (Math.random() > (this.options.samplingRate || 1.0)) {
      return
    }
    
    // 监控页面加载性能
    this.monitorPageLoad()
    
    // 监控资源加载性能
    if (this.options.resourceMonitoring) {
      this.monitorResources()
    }
    
    // 监控内存使用情况
    if (this.options.memoryMonitoring) {
      this.monitorMemory()
    }
    
    // 监控用户交互
    if (this.options.interactionMonitoring) {
      this.monitorInteractions()
    }
    
    // 监控错误
    if (this.options.errorMonitoring) {
      this.monitorErrors()
    }
  }
  
  /**
   * 停止性能监控
   */
  public stopMonitoring(): void {
    // 断开所有观察器
    this.disconnectObservers()
    
    // 清除内存监控定时器
    if (this.memoryInterval !== null) {
      window.clearInterval(this.memoryInterval)
      this.memoryInterval = null
    }
    
    // 移除错误处理程序
    if (this.errorHandler) {
      window.removeEventListener('error', this.errorHandler)
      this.errorHandler = null
    }
  }
  
  /**
   * 获取性能指标
   */
  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }
  
  /**
   * 添加自定义指标
   */
  public addCustomMetric(name: string, value: number): void {
    if (!this.metrics.custom) {
      this.metrics.custom = {}
    }
    
    this.metrics.custom[name] = value
    
    // 如果启用了控制台日志，则输出日志
    if (this.options.consoleLog) {
      console.log(`Custom metric: ${name} = ${value}`)
    }
  }
  
  /**
   * 手动上报性能指标
   */
  public report(): void {
    if (this.options.reportCallback) {
      this.options.reportCallback(this.getMetrics())
    }
    
    // 如果启用了控制台日志，则输出日志
    if (this.options.consoleLog) {
      console.log('Performance metrics:', this.getMetrics())
    }
  }
  
  /**
   * 监控页面加载性能
   */
  private monitorPageLoad(): void {
    // 使用Performance API获取导航时间
    if (window.performance.timing) {
      const timing = window.performance.timing
      
      // 页面加载时间
      this.metrics.pageLoadTime = timing.loadEventEnd - timing.navigationStart
      
      // 首屏渲染时间
      this.metrics.firstScreenRender = timing.domContentLoadedEventEnd - timing.navigationStart
    }
    
    // 使用PerformanceObserver监控绘制事件
    if (window.PerformanceObserver) {
      try {
        // 监控绘制事件
        this.paintObserver = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries()
          
          entries.forEach((entry) => {
            const name = entry.name
            
            if (name === 'first-paint') {
              this.metrics.firstContentfulPaint = entry.startTime
            } else if (name === 'first-contentful-paint') {
              this.metrics.firstContentfulPaint = entry.startTime
            } else if (name === 'first-meaningful-paint') {
              this.metrics.firstMeaningfulPaint = entry.startTime
            }
          })
        })
        
        this.paintObserver.observe({ entryTypes: ['paint'] })
        
        // 监控最大内容绘制
        this.largestContentfulPaintObserver = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries()
          const lastEntry = entries[entries.length - 1]
          
          if (lastEntry) {
            this.metrics.largestContentfulPaint = lastEntry.startTime
          }
        })
        
        this.largestContentfulPaintObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        
        // 监控累积布局偏移
        this.layoutShiftObserver = new PerformanceObserver((entryList) => {
          let cumulativeLayoutShift = 0
          
          entryList.getEntries().forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              cumulativeLayoutShift += entry.value
            }
          })
          
          this.metrics.cumulativeLayoutShift = cumulativeLayoutShift
        })
        
        this.layoutShiftObserver.observe({ entryTypes: ['layout-shift'] })
        
        // 监控首次输入延迟
        this.firstInputObserver = new PerformanceObserver((entryList) => {
          const firstInput = entryList.getEntries()[0]
          
          if (firstInput) {
            this.metrics.firstInputDelay = firstInput.processingStart - firstInput.startTime
          }
        })
        
        this.firstInputObserver.observe({ entryTypes: ['first-input'] })
        
        // 监控导航
        this.navigationObserver = new PerformanceObserver((entryList) => {
          const navigationEntry = entryList.getEntries()[0] as PerformanceNavigationTiming
          
          if (navigationEntry) {
            this.metrics.pageLoadTime = navigationEntry.loadEventEnd - navigationEntry.startTime
            this.metrics.firstScreenRender = navigationEntry.domContentLoadedEventEnd - navigationEntry.startTime
          }
        })
        
        this.navigationObserver.observe({ entryTypes: ['navigation'] })
      } catch (e) {
        console.warn('PerformanceObserver is not fully supported in this browser', e)
      }
    }
  }
  
  /**
   * 监控资源加载性能
   */
  private monitorResources(): void {
    if (!window.PerformanceObserver) {
      return
    }
    
    try {
      this.resourceObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries() as PerformanceResourceTiming[]
        
        if (!this.metrics.resourceLoadTime) {
          this.metrics.resourceLoadTime = {}
        }
        
        entries.forEach((entry) => {
          const url = entry.name
          const duration = entry.duration
          
          // 只记录关键资源
          if (
            url.endsWith('.js') ||
            url.endsWith('.css') ||
            url.endsWith('.png') ||
            url.endsWith('.jpg') ||
            url.endsWith('.jpeg') ||
            url.endsWith('.gif') ||
            url.endsWith('.svg')
          ) {
            // 提取文件名
            const fileName = url.substring(url.lastIndexOf('/') + 1)
            this.metrics.resourceLoadTime![fileName] = duration
          }
        })
      })
      
      this.resourceObserver.observe({ entryTypes: ['resource'] })
    } catch (e) {
      console.warn('Resource monitoring is not supported in this browser', e)
    }
  }
  
  /**
   * 监控内存使用情况
   */
  private monitorMemory(): void {
    // 检查是否支持内存API
    if (!(performance as any).memory) {
      return
    }
    
    // 每10秒收集一次内存使用情况
    this.memoryInterval = window.setInterval(() => {
      const memory = (performance as any).memory
      
      this.metrics.memoryUsage = {
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        totalJSHeapSize: memory.totalJSHeapSize,
        usedJSHeapSize: memory.usedJSHeapSize
      }
    }, 10000)
  }
  
  /**
   * 监控用户交互
   */
  private monitorInteractions(): void {
    if (!window.PerformanceObserver) {
      return
    }
    
    try {
      this.interactionObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries()
        
        entries.forEach((entry: any) => {
          // 记录长任务
          if (entry.entryType === 'longtask' && entry.duration > 50) {
            this.addCustomMetric(`longtask_${Date.now()}`, entry.duration)
          }
        })
      })
      
      this.interactionObserver.observe({ entryTypes: ['longtask'] })
    } catch (e) {
      console.warn('Interaction monitoring is not fully supported in this browser', e)
    }
  }
  
  /**
   * 监控错误
   */
  private monitorErrors(): void {
    this.errorHandler = (event: ErrorEvent) => {
      this.addCustomMetric(`error_${Date.now()}`, 1)
    }
    
    window.addEventListener('error', this.errorHandler)
  }
  
  /**
   * 断开所有观察器
   */
  private disconnectObservers(): void {
    if (this.resourceObserver) {
      this.resourceObserver.disconnect()
      this.resourceObserver = null
    }
    
    if (this.paintObserver) {
      this.paintObserver.disconnect()
      this.paintObserver = null
    }
    
    if (this.layoutShiftObserver) {
      this.layoutShiftObserver.disconnect()
      this.layoutShiftObserver = null
    }
    
    if (this.largestContentfulPaintObserver) {
      this.largestContentfulPaintObserver.disconnect()
      this.largestContentfulPaintObserver = null
    }
    
    if (this.firstInputObserver) {
      this.firstInputObserver.disconnect()
      this.firstInputObserver = null
    }
    
    if (this.interactionObserver) {
      this.interactionObserver.disconnect()
      this.interactionObserver = null
    }
    
    if (this.navigationObserver) {
      this.navigationObserver.disconnect()
      this.navigationObserver = null
    }
  }
}

// 创建默认实例
export const performanceMonitor = new PerformanceMonitor({
  consoleLog: process.env.NODE_ENV === 'development'
})

// 导出默认实例的方法
export const startMonitoring = performanceMonitor.startMonitoring.bind(performanceMonitor)
export const stopMonitoring = performanceMonitor.stopMonitoring.bind(performanceMonitor)
export const getMetrics = performanceMonitor.getMetrics.bind(performanceMonitor)
export const addCustomMetric = performanceMonitor.addCustomMetric.bind(performanceMonitor)
export const report = performanceMonitor.report.bind(performanceMonitor)

// 在主应用中使用
export default performanceMonitor
