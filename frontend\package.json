{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:ui": "playwright test --ui", "test:e2e:report": "playwright show-report"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.7", "chart.js": "^4.4.8", "echarts": "^5.5.0", "element-plus": "^2.5.6", "file-saver": "^2.0.5", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "vite-plugin-svg-icons": "^0.1.0", "vue": "^3.4.21", "vue-router": "^4.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@playwright/test": "^1.42.1", "@testing-library/user-event": "^14.4.3", "@testing-library/vue": "^7.0.0", "@types/jsdom": "^21.1.1", "@vitejs/plugin-vue": "^5.0.4", "@vitest/coverage-v8": "^3.1.4", "@vue/test-utils": "^2.4.3", "@vue/tsconfig": "^0.4.0", "jsdom": "^22.1.0", "sass": "^1.86.3", "terser": "^5.39.0", "typescript": "^5.4.5", "unplugin-auto-import": "^19.2.0", "unplugin-vue-components": "^28.5.0", "vite": "^6.3.5", "vitest": "^3.1.4", "vue-tsc": "^2.0.7"}}