package com.simbest.cloud.pyjyj.uums;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.simbest.cloud.cores.config.AppConfig;
import com.simbest.cloud.cores.security.service.AbstractAuthService;
import com.simbest.cloud.cores.security.service.IAuthUserCacheService;
import com.simbest.cloud.cores.uums.api.user.UumsSysUserinfoApi;

/**
 * 用途：不得不实现的接口，认证已由UUMS统一管理
 * 时间: 2018/6/11 10:20
 */
@Service
public class MyAuthService extends AbstractAuthService {

    @Autowired
    public MyAuthService(IAuthUserCacheService authUserCacheService, AppConfig appConfig,
            @Qualifier("uumsSysUserinfoApi") UumsSysUserinfoApi userinfoApi) {
        super(authUserCacheService, appConfig, userinfoApi);
    }

}