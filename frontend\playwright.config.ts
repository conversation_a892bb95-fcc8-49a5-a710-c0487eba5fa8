import { defineConfig, devices } from '@playwright/test';
import path from 'path';

/**
 * 前端项目Playwright配置
 * 继承自全局配置，但可以覆盖特定设置
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  // 测试目录
  testDir: '../tests/e2e',
  
  // 测试文件匹配模式
  testMatch: '**/*.spec.ts',
  
  // 每个测试的超时时间
  timeout: 30000,
  
  // 测试失败时自动重试次数
  retries: process.env.CI ? 2 : 0,
  
  // 并行运行测试的工作进程数
  workers: process.env.CI ? 1 : undefined,
  
  // 测试报告器
  reporter: [
    ['html', { open: 'never' }],
    ['list']
  ],
  
  // 共享设置
  use: {
    // 基础URL
    baseURL: process.env.BASE_URL || 'http://localhost:3000',
    
    // 自动截图
    screenshot: 'only-on-failure',
    
    // 收集追踪信息
    trace: 'on-first-retry',
    
    // 视频录制
    video: 'on-first-retry',
  },
  
  // 项目配置
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'mobile-chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'mobile-safari',
      use: { ...devices['iPhone 12'] },
    },
  ],
  
  // 本地开发服务器配置
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
    timeout: 120000,
  },
});
