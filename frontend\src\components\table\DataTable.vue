<template>
  <div class="data-table-container">
    <el-card :body-style="{ padding: '20px' }">
      <!-- 工具栏 -->
      <div v-if="$slots.toolbar" class="table-toolbar">
        <slot name="toolbar"></slot>
      </div>

      <!-- 表格 -->
      <el-table
        ref="tableRef"
        v-bind="$attrs"
        :data="data"
        :border="border"
        :stripe="stripe"
        :row-key="rowKey"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <!-- 选择列 -->
        <el-table-column
          v-if="showSelection"
          type="selection"
          width="55"
          align="center"
          fixed="left"
        />

        <!-- 序号列 -->
        <el-table-column
          v-if="showIndex"
          type="index"
          :label="indexLabel"
          :width="indexWidth"
          :align="indexAlign"
          fixed="left"
        />

        <!-- 自定义列 -->
        <slot></slot>

        <!-- 操作列 -->
        <el-table-column
          v-if="$slots.action"
          :label="actionLabel"
          :width="actionWidth"
          :fixed="actionFixed"
          :align="actionAlign"
        >
          <template #default="scope">
            <slot name="action" :row="scope.row" :index="scope.$index"></slot>
          </template>
        </el-table-column>
        <template #empty>
          <el-empty description="暂无数据" />
        </template>
      </el-table>

      <!-- 分页器 -->
      <div v-if="showPagination" class="pagination-container">
        <el-pagination
          :current-page="currentPageValue"
          :page-size="pageSizeValue"
          :page-sizes="pageSizes"
          :layout="paginationLayout"
          :total="total"
          :background="background"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @update:current-page="handleCurrentChange"
          @update:page-size="handleSizeChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import type { TableInstance } from "element-plus";

const props = defineProps({
  /**
   * 表格数据
   */
  data: {
    type: Array,
    required: false,
    default: () => [],
  },
  /**
   * 是否显示边框
   */
  border: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否显示斑马纹
   */
  stripe: {
    type: Boolean,
    default: true,
  },
  /**
   * 行数据的键值
   */
  rowKey: {
    type: String,
    default: "id",
  },
  /**
   * 加载状态
   */
  loading: {
    type: Boolean,
    default: true,
  },
  /**
   * 是否显示选择列
   */
  showSelection: {
    type: Boolean,
    default: false,
  },
  /**
   * 是否显示序号列
   */
  showIndex: {
    type: Boolean,
    default: false,
  },
  /**
   * 序号列标签
   */
  indexLabel: {
    type: String,
    default: "序号",
  },
  /**
   * 序号列宽度
   */
  indexWidth: {
    type: [String, Number],
    default: 60,
  },
  /**
   * 序号列对齐方式
   */
  indexAlign: {
    type: String,
    default: "center",
  },
  /**
   * 操作列标签
   */
  actionLabel: {
    type: String,
    default: "操作",
  },
  /**
   * 操作列宽度
   */
  actionWidth: {
    type: [String, Number],
    default: 180,
  },
  /**
   * 操作列是否固定
   */
  actionFixed: {
    type: String,
    default: "right",
  },
  /**
   * 操作列对齐方式
   */
  actionAlign: {
    type: String,
    default: "center",
  },
  /**
   * 是否显示分页
   */
  showPagination: {
    type: Boolean,
    default: true,
  },
  /**
   * 当前页码
   */
  currentPage: {
    type: Number,
    default: 1,
  },
  /**
   * 每页条数
   */
  pageSize: {
    type: Number,
    default: 10,
  },
  /**
   * 每页条数选项
   */
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100],
  },
  /**
   * 分页布局
   */
  paginationLayout: {
    type: String,
    default: "total, sizes, prev, pager, next, jumper",
  },
  /**
   * 总条数
   */
  total: {
    type: Number,
    default: 0,
  },
  /**
   * 分页按钮是否带有背景色
   */
  background: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits([
  "update:currentPage",
  "update:pageSize",
  "size-change",
  "current-change",
  "selection-change",
]);

const tableRef = ref<TableInstance>();

// 计算属性处理分页
const currentPageValue = computed(() => props.currentPage);
const pageSizeValue = computed(() => props.pageSize);

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  emit("selection-change", selection);
};

// 每页条数变化
const handleSizeChange = (size: number) => {
  emit("update:pageSize", size);
  emit("size-change", size);
};

// 页码变化
const handleCurrentChange = (page: number) => {
  emit("update:currentPage", page);
  emit("current-change", page);
};

// 暴露方法给父组件
defineExpose({
  tableRef,
  // 清除选择
  clearSelection: () => {
    tableRef.value?.clearSelection();
  },
  // 切换行选中状态
  toggleRowSelection: (row: any, selected?: boolean) => {
    tableRef.value?.toggleRowSelection(row, selected);
  },
  // 切换全选状态
  toggleAllSelection: () => {
    tableRef.value?.toggleAllSelection();
  },
});
</script>

<style scoped>
.data-table-container {
  margin-bottom: 20px;
}

.table-toolbar {
  padding: 10px 15px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  padding: 15px;
  display: flex;
  justify-content: flex-end;
}
</style>
