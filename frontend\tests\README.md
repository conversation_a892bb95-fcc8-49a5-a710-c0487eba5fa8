# Salemanage 前端测试指南

本文档提供了 Salemanage 前端测试的相关指导和规范，包括单元测试、组件测试以及端到端测试。

## 测试框架与工具

- **Vitest**: 单元测试和组件测试的主要框架
- **Vue Test Utils**: Vue 组件测试的官方工具库
- **jsdom**: 浏览器环境模拟
- **@testing-library/vue**: 组件交互测试的辅助工具

## 项目测试结构

```
frontend/
├── tests/
│   ├── unit/            # 单元测试（工具函数、hooks等）
│   ├── components/      # 组件测试
│   ├── views/           # 页面视图测试
│   ├── store/           # 状态管理测试
│   └── setup.ts         # 测试环境配置文件
├── vitest.config.ts     # Vitest 配置文件
└── package.json         # 包含测试脚本
```

## 运行测试

```bash
# 运行所有测试
npm run test

# 以监视模式运行测试（开发时使用）
npm run test:watch

# 运行测试并生成覆盖率报告
npm run test:coverage
```

## 测试规范

### 单元测试

- 对独立的工具函数、hooks 进行测试
- 测试文件与被测试代码文件结构保持一致
- 测试文件名以 `.spec.ts` 或 `.test.ts` 结尾

### 组件测试

- 测试组件的渲染、交互行为和响应状态
- 遵循"黑盒测试"原则，专注于测试组件的输入和输出，而非内部实现
- 模拟依赖（如 API 调用、Vuex/Pinia 等）
- 组件测试文件置于 `tests/components` 目录下

### 覆盖率要求

- 整体覆盖率目标: 80%
- 关键业务组件覆盖率目标: 90%
- 工具函数覆盖率目标: 95%

## 最佳实践

1. **测试分离原则**: 每个测试只测试一个特定功能或行为
2. **AAA模式**: 
   - Arrange（准备）- 设置测试条件
   - Act（执行）- 执行要测试的代码
   - Assert（断言）- 验证结果
3. **命名规范**: 测试描述应清晰表达被测试的行为和预期结果
4. **避免测试实现细节**: 测试组件的行为，而非实现方式

## 模拟 (Mock) 策略

- 使用 `vi.mock()` 模拟外部依赖
- 为 API 调用创建 __mocks__ 目录
- 使用 `vi.spyOn()` 监视方法调用
- 对于 localStorage、fetch 等浏览器 API，使用测试环境中的模拟实现

## 常见问题与解决方案

### 异步测试

```ts
it('异步操作测试', async () => {
  // 使用 await 处理异步操作
  const result = await someAsyncFunction();
  expect(result).toBe(expectedValue);
  
  // 或使用 flushPromises 等待所有异步操作完成
  await flushPromises();
});
```

### 事件测试

```ts
it('测试按钮点击事件', async () => {
  const wrapper = mount(Component);
  await wrapper.find('button').trigger('click');
  // 验证结果
});
```

### 路由测试

```ts
// 创建模拟路由
const router = createRouter({
  history: createMemoryHistory(),
  routes: [
    { path: '/', component: Home },
    { path: '/about', component: About }
  ]
});

// 在测试中使用
const wrapper = mount(App, {
  global: {
    plugins: [router]
  }
});
```

## 参考资源

- [Vitest 文档](https://vitest.dev/)
- [Vue Test Utils 文档](https://test-utils.vuejs.org/)
- [Testing Library 文档](https://testing-library.com/docs/vue-testing-library/intro) 