import { describe, it, expect, vi, beforeEach } from 'vitest'
import { getSalesReport, getAuditReport, getFinanceReport } from '@/api/reports'
import axios from 'axios'

// Mock axios
vi.mock('axios')

describe('Reports API', () => {
  beforeEach(() => {
    // 清除所有模拟调用信息
    vi.clearAllMocks()
  })

  describe('getSalesReport', () => {
    it('should call the sales report endpoint with correct parameters', async () => {
      // 创建axios响应
      const mockResponse = {
        data: {
          data: {
            items: [],
            total: 0,
            summary: {},
            trend_data: {},
            channel_data: {},
            platform_data: {},
            person_data: {}
          }
        }
      }
      
      // 模拟axios.request返回成功响应
      axios.request = vi.fn().mockResolvedValue(mockResponse)
      
      // 调用函数
      const params = {
        start_date: '2023-01-01',
        end_date: '2023-01-31',
        channel: '渠道A',
        page: 1,
        page_size: 20
      }
      
      const result = await getSalesReport(params)
      
      // 验证axios.request被调用，并检查参数是否正确
      expect(axios.request).toHaveBeenCalledTimes(1)
      
      const callArgs = axios.request.mock.calls[0][0]
      expect(callArgs.url).toContain('/reports/sales')
      expect(callArgs.method).toBe('get')
      expect(callArgs.params).toEqual(params)
      
      // 验证返回数据
      expect(result).toEqual(mockResponse.data)
    })
    
    it('should handle errors', async () => {
      // 模拟axios.request抛出错误
      const error = new Error('Network Error')
      axios.request = vi.fn().mockRejectedValue(error)
      
      // 调用函数并捕获错误
      try {
        await getSalesReport({})
        // 如果没有抛出错误，测试应该失败
        expect(true).toBe(false)
      } catch (err) {
        expect(err).toBe(error)
      }
      
      expect(axios.request).toHaveBeenCalledTimes(1)
    })
  })
  
  describe('getAuditReport', () => {
    it('should call the audit report endpoint with correct parameters', async () => {
      // 创建axios响应
      const mockResponse = {
        data: {
          data: {
            items: [],
            total: 0,
            summary: {},
            overview_data: {},
            exception_data: [],
            trend_data: {}
          }
        }
      }
      
      // 模拟axios.request返回成功响应
      axios.request = vi.fn().mockResolvedValue(mockResponse)
      
      // 调用函数
      const params = {
        start_date: '2023-01-01',
        end_date: '2023-01-31',
        platform: '抖音',
        page: 1,
        page_size: 20
      }
      
      const result = await getAuditReport(params)
      
      // 验证axios.request被调用，并检查参数是否正确
      expect(axios.request).toHaveBeenCalledTimes(1)
      
      const callArgs = axios.request.mock.calls[0][0]
      expect(callArgs.url).toContain('/reports/audit')
      expect(callArgs.method).toBe('get')
      expect(callArgs.params).toEqual(params)
      
      // 验证返回数据
      expect(result).toEqual(mockResponse.data)
    })
    
    it('should handle errors', async () => {
      // 模拟axios.request抛出错误
      const error = new Error('Network Error')
      axios.request = vi.fn().mockRejectedValue(error)
      
      // 调用函数并捕获错误
      try {
        await getAuditReport({})
        // 如果没有抛出错误，测试应该失败
        expect(true).toBe(false)
      } catch (err) {
        expect(err).toBe(error)
      }
      
      expect(axios.request).toHaveBeenCalledTimes(1)
    })
  })
  
  describe('getFinanceReport', () => {
    it('should call the finance report endpoint with correct parameters', async () => {
      // 创建axios响应
      const mockResponse = {
        data: {
          data: {
            items: [],
            total: 0,
            summary: {},
            income_data: {},
            profit_data: {},
            debt_data: {}
          }
        }
      }
      
      // 模拟axios.request返回成功响应
      axios.request = vi.fn().mockResolvedValue(mockResponse)
      
      // 调用函数
      const params = {
        start_date: '2023-01-01',
        end_date: '2023-01-31',
        platform: '抖音',
        channel: '渠道A',
        page: 1,
        page_size: 20
      }
      
      const result = await getFinanceReport(params)
      
      // 验证axios.request被调用，并检查参数是否正确
      expect(axios.request).toHaveBeenCalledTimes(1)
      
      const callArgs = axios.request.mock.calls[0][0]
      expect(callArgs.url).toContain('/reports/finance')
      expect(callArgs.method).toBe('get')
      expect(callArgs.params).toEqual(params)
      
      // 验证返回数据
      expect(result).toEqual(mockResponse.data)
    })
    
    it('should handle errors', async () => {
      // 模拟axios.request抛出错误
      const error = new Error('Network Error')
      axios.request = vi.fn().mockRejectedValue(error)
      
      // 调用函数并捕获错误
      try {
        await getFinanceReport({})
        // 如果没有抛出错误，测试应该失败
        expect(true).toBe(false)
      } catch (err) {
        expect(err).toBe(error)
      }
      
      expect(axios.request).toHaveBeenCalledTimes(1)
    })
  })
})
