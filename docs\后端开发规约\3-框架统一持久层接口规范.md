# 3-框架统一持久层接口规范

本文档描述了SIMBEST框架中三个基础持久层接口的说明。

## 1. GenericRepository（基础实体通用数据持久层接口）

| 接口名称 | 接口类型 | 接口含义 | 继承关系 |
|---------|---------|---------|---------|
| GenericRepository | 基础持久层接口 | 提供通用实体的基础数据访问功能，包括：<br>- 扩展BaseRepository，继承其基础数据库操作功能<br>- 为GenericModel类及其子类提供数据访问支持<br>- 支持事务操作，默认所有方法都在事务中执行<br>- 作为其他更具体仓库接口的基础 | 继承自BaseRepository |

### 主要方法说明

| 方法名称 | 方法注释 | 输入参数 | 输出结果 |
|---------|---------|---------|---------|
| count() | 统计实体总数 | 无 | long：实体总数 |
| count(Specification<T> specification) | 根据条件统计实体数量 | specification：查询条件 | long：符合条件的实体数量 |
| existsById(PK id) | 检查指定ID的实体是否存在 | id：实体ID | boolean：是否存在 |
| getOne(PK id) | 根据ID查找实体（懒加载方式） | id：实体ID | T：实体对象（懒加载） |
| findOne(Specification<T> conditions) | 根据条件查找单个实体 | conditions：查询条件 | T：实体对象，不存在返回null |
| findById(PK id) | 根据ID查找实体（即时加载方式） | id：实体ID | T：实体对象，不存在返回null |
| findAll() | 查询所有实体并分页 | 无 | Page<T>：分页后的实体列表 |
| findAll(Pageable pageable) | 根据分页参数查询所有实体 | pageable：分页参数 | Page<T>：分页后的实体列表 |
| findAll(Sort sort) | 根据排序参数查询所有实体并分页 | sort：排序参数 | Page<T>：分页后的实体列表 |
| findAllNoPage() | 查询所有实体不分页 | 无 | Iterable<T>：所有实体列表 |
| findAllNoPage(Sort sort) | 根据排序参数查询所有实体不分页 | sort：排序参数 | Iterable<T>：排序后的所有实体列表 |
| findAllByIDs(Iterable<PK> ids) | 根据ID集合查询多个实体 | ids：ID集合 | Iterable<T>：实体列表 |
| findAll(Specification<T> conditions, Pageable pageable) | 根据条件查询实体并分页 | conditions：查询条件<br>pageable：分页参数 | Page<T>：分页后的实体列表 |
| findAllNoPage(Specification<T> conditions) | 根据条件查询实体不分页 | conditions：查询条件 | Iterable<T>：符合条件的所有实体 |
| findAllNoPage(Specification<T> conditions, Sort sort) | 根据条件和排序参数查询实体不分页 | conditions：查询条件<br>sort：排序参数 | Iterable<T>：排序后的符合条件的所有实体 |
| save(T entity) | 保存实体 | entity：待保存的实体对象 | T：保存后的实体对象 |
| saveAll(Iterable<T> entities) | 批量保存实体 | entities：待保存的实体集合 | List<T>：保存后的实体列表 |
| saveAndFlush(T entity) | 保存实体并立即刷新 | entity：待保存的实体对象 | T：保存后的实体对象 |
| deleteById(PK id) | 根据ID删除实体 | id：实体ID | void |
| delete(T entity) | 删除实体 | entity：待删除的实体对象 | void |
| deleteAll(Iterable<? extends T> entities) | 批量删除实体 | entities：待删除的实体集合 | void |
| deleteAll() | 删除所有实体 | 无 | void |
| deleteAllByIds(Iterable<? extends PK> ids) | 根据ID集合批量删除实体 | ids：ID集合 | void |

## 2. SystemRepository（系统实体通用数据持久层接口）

| 接口名称 | 接口类型 | 接口含义 | 继承关系 |
|---------|---------|---------|---------|
| SystemRepository | 系统持久层接口 | 提供系统实体的数据访问功能，包括：<br>- 扩展GenericRepository，继承其基础数据库操作功能<br>- 为SystemModel类及其子类提供数据访问支持<br>- 自动处理创建时间和修改时间字段<br>- 作为LogicRepository的基础接口 | 继承自GenericRepository |

### 主要方法说明
继承自GenericRepository的所有方法，并自动处理创建时间和修改时间字段。

## 3. LogicRepository（业务实体通用数据持久层接口）

| 接口名称 | 接口类型 | 接口含义 | 继承关系 |
|---------|---------|---------|---------|
| LogicRepository | 业务持久层接口 | 提供业务实体的数据访问功能，支持逻辑删除，包括：<br>- 扩展SystemRepository，继承其基础数据库操作功能<br>- 为LogicModel类及其子类提供数据访问支持<br>- 所有查询方法都只返回未被逻辑删除的数据（removedTime为空）<br>- 提供逻辑删除功能，通过设置removedTime实现<br>- 支持即时逻辑删除和计划逻辑删除 | 继承自SystemRepository |

### 主要方法说明

| 方法名称 | 方法注释 | 输入参数 | 输出结果 |
|---------|---------|---------|---------|
| countActive() | 统计所有未逻辑删除的记录数量 | 无 | long：活跃记录数量 |
| countActive(Specification<T> conditions) | 根据条件统计未逻辑删除的记录数量 | conditions：查询条件 | long：符合条件的活跃记录数量 |
| existsActive(PK id) | 判断指定ID的记录是否存在且未被逻辑删除 | id：记录ID | boolean：是否存在且活跃 |
| findAllActive() | 查询所有未逻辑删除的记录并分页 | 无 | Page<T>：分页后的活跃记录 |
| findAllActive(Sort sort) | 查询所有未逻辑删除的记录并排序分页 | sort：排序条件 | Page<T>：排序并分页后的活跃记录 |
| findAllActive(Pageable pageable) | 根据分页参数查询未逻辑删除的记录 | pageable：分页参数 | Page<T>：分页后的活跃记录 |
| findAllActiveNoPage() | 查询所有未逻辑删除的记录不分页 | 无 | List<T>：所有活跃记录列表 |
| findAllActiveNoPage(Sort sort) | 查询所有未逻辑删除的记录并排序不分页 | sort：排序条件 | List<T>：排序后的所有活跃记录列表 |
| findAllActive(Iterable<PK> ids) | 根据ID集合查询未逻辑删除的记录 | ids：ID集合 | List<T>：符合ID且活跃的记录列表 |
| findAllActive(Specification<T> conditions) | 根据条件查询未逻辑删除的记录 | conditions：查询条件 | List<T>：符合条件的活跃记录列表 |
| findAllActive(Specification<T> conditions, Pageable pageable) | 根据条件查询未逻辑删除的记录并分页 | conditions：查询条件<br>pageable：分页参数 | Page<T>：分页后的符合条件的活跃记录 |
| findAllActive(Specification<T> conditions, Sort sort) | 根据条件查询未逻辑删除的记录并排序 | conditions：查询条件<br>sort：排序条件 | List<T>：排序后的符合条件的活跃记录列表 |
| findByIdActive(PK id) | 根据ID查询未逻辑删除的记录 | id：记录ID | T：活跃的记录对象 |
| findOneActive(PK id) | 根据ID查询未逻辑删除的记录（与findByIdActive相同） | id：记录ID | T：活跃的记录对象 |
| findOneActive(Specification<T> conditions) | 根据条件查询单个未逻辑删除的记录 | conditions：查询条件 | T：符合条件的单个活跃记录 |
| deleteById(PK id) | 将指定ID的记录标记为逻辑删除 | id：记录ID | void |

## 继承关系

- LogicRepository 继承自 SystemRepository
- SystemRepository 继承自 GenericRepository
- GenericRepository 继承自 BaseRepository

## 说明

1. GenericRepository是所有持久层接口的基础，提供通用的数据访问功能
2. SystemRepository在GenericRepository基础上增加了系统实体的特定功能
3. LogicRepository在SystemRepository基础上增加了逻辑删除和业务实体的特定功能
4. 所有接口都支持事务操作，默认所有方法都在事务中执行
5. 所有接口都使用@NoRepositoryBean注解，表示这是一个基础接口，不能被直接实例化
6. LogicRepository的所有查询方法都会自动过滤掉已逻辑删除的记录（removedTime不为空的记录）
7. 所有方法都支持泛型参数T（实体类型）和PK（主键类型）
8. 分页查询默认使用系统配置的分页大小
9. 排序查询支持多字段排序
10. 条件查询支持动态构建查询条件 