import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import { performance } from 'perf_hooks'

/**
 * 组件渲染性能测试
 * 此测试旨在测量Vue组件的渲染性能，主要关注初始渲染和更新渲染的时间
 */
describe('组件渲染性能测试', () => {
  // 渲染性能基准（毫秒）
  const RENDER_BENCHMARKS = {
    INITIAL_RENDER: 50,   // 组件初始渲染时间
    UPDATE_RENDER: 30,    // 组件更新渲染时间
    LARGE_LIST_RENDER: 100 // 大列表渲染时间
  }
  
  // 测量组件渲染时间的辅助函数
  async function measureRenderPerformance(renderFn: () => Promise<any>): Promise<number> {
    const start = performance.now()
    await renderFn()
    // 确保DOM已更新
    await nextTick()
    const end = performance.now()
    return end - start
  }
  
  it('简单组件的初始渲染性能应在基准范围内', async () => {
    // 创建一个简单组件
    const SimpleComponent = {
      template: `
        <div class="simple-component">
          <h1>{{ title }}</h1>
          <p>{{ description }}</p>
          <button @click="count++">Count: {{ count }}</button>
        </div>
      `,
      data() {
        return {
          title: '简单组件',
          description: '这是一个用于性能测试的简单组件',
          count: 0
        }
      }
    }
    
    // 测量初始渲染时间
    const renderTime = await measureRenderPerformance(async () => {
      return mount(SimpleComponent)
    })
    
    console.log(`简单组件初始渲染时间: ${renderTime.toFixed(2)}ms, 基准: ${RENDER_BENCHMARKS.INITIAL_RENDER}ms`)
    expect(renderTime).toBeLessThanOrEqual(RENDER_BENCHMARKS.INITIAL_RENDER * 1.5)
  })
  
  it('组件更新渲染性能应在基准范围内', async () => {
    // 创建一个可更新的组件
    const UpdatableComponent = {
      template: `
        <div class="updatable-component">
          <h1>{{ title }}</h1>
          <p>{{ description }}</p>
          <div v-for="(item, index) in items" :key="index">
            <span>{{ item }}</span>
          </div>
          <button @click="addItem">添加项目</button>
        </div>
      `,
      data() {
        return {
          title: '可更新组件',
          description: '这是一个可以测试更新性能的组件',
          items: ['项目1', '项目2', '项目3']
        }
      },
      methods: {
        addItem() {
          this.items.push(`项目${this.items.length + 1}`)
        }
      }
    }
    
    // 挂载组件
    const wrapper = mount(UpdatableComponent)
    
    // 测量更新渲染时间
    const updateTime = await measureRenderPerformance(async () => {
      await wrapper.find('button').trigger('click')
    })
    
    console.log(`组件更新渲染时间: ${updateTime.toFixed(2)}ms, 基准: ${RENDER_BENCHMARKS.UPDATE_RENDER}ms`)
    expect(updateTime).toBeLessThanOrEqual(RENDER_BENCHMARKS.UPDATE_RENDER * 1.5)
  })
  
  it('大列表渲染性能应在基准范围内', async () => {
    // 生成大量测试数据
    const generateLargeDataset = (count: number) => {
      const items = []
      for (let i = 0; i < count; i++) {
        items.push({
          id: i,
          name: `项目 ${i}`,
          description: `这是项目 ${i} 的详细描述`,
          value: Math.random() * 1000
        })
      }
      return items
    }
    
    // 创建一个列表组件
    const LargeListComponent = {
      template: `
        <div class="large-list-component">
          <h1>大量数据列表</h1>
          <div class="list-container">
            <div v-for="item in items" :key="item.id" class="list-item">
              <h3>{{ item.name }}</h3>
              <p>{{ item.description }}</p>
              <span>{{ formatValue(item.value) }}</span>
            </div>
          </div>
        </div>
      `,
      props: {
        items: {
          type: Array,
          default: () => []
        }
      },
      methods: {
        formatValue(value: number): string {
          return value.toFixed(2)
        }
      }
    }
    
    // 100个列表项
    const largeDataset = generateLargeDataset(100)
    
    // 测量大列表渲染时间
    const largeListRenderTime = await measureRenderPerformance(async () => {
      return mount(LargeListComponent, {
        props: {
          items: largeDataset
        }
      })
    })
    
    console.log(`大列表渲染时间(100项): ${largeListRenderTime.toFixed(2)}ms, 基准: ${RENDER_BENCHMARKS.LARGE_LIST_RENDER}ms`)
    expect(largeListRenderTime).toBeLessThanOrEqual(RENDER_BENCHMARKS.LARGE_LIST_RENDER * 1.5)
  })
  
  it('组件反复渲染性能应保持稳定', async () => {
    // 创建一个简单的计数器组件
    const CounterComponent = {
      template: `
        <div class="counter-component">
          <h2>{{ title }}</h2>
          <div class="counter">{{ count }}</div>
          <button @click="increment">增加</button>
        </div>
      `,
      data() {
        return {
          title: '计数器组件',
          count: 0
        }
      },
      methods: {
        increment() {
          this.count++
        }
      }
    }
    
    // 挂载组件
    const wrapper = mount(CounterComponent)
    
    // 进行10次连续更新，并记录每次的时间
    const updateTimes = []
    
    for (let i = 0; i < 10; i++) {
      const updateTime = await measureRenderPerformance(async () => {
        await wrapper.find('button').trigger('click')
      })
      
      updateTimes.push(updateTime)
      console.log(`第 ${i+1} 次更新时间: ${updateTime.toFixed(2)}ms`)
    }
    
    // 计算平均更新时间
    const avgUpdateTime = updateTimes.reduce((a, b) => a + b, 0) / updateTimes.length
    console.log(`平均更新时间: ${avgUpdateTime.toFixed(2)}ms`)
    
    // 计算时间变化率 (最大时间 / 最小时间)
    const maxTime = Math.max(...updateTimes)
    const minTime = Math.min(...updateTimes)
    const timeVariation = maxTime / minTime
    
    console.log(`时间变化率: ${timeVariation.toFixed(2)}`)
    
    // 验证时间变化率不应超过3倍，表明性能相对稳定
    // 注意：这个测试可能有些波动，因为测试环境的不稳定因素
    expect(timeVariation).toBeLessThanOrEqual(3)
  })
}) 