package com.simbest.cloud;

import java.net.InetAddress;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.web.context.WebServerApplicationContext;
import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.boot.web.server.WebServer;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.util.StringUtils;

import com.simbest.cloud.cores.base.repository.CustomJpaRepositoryFactoryBean;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@SpringBootApplication
@EnableRetry
@EnableFeignClients(basePackages = "com.simbest.**.feigns")
@EntityScan(basePackages = { "com.simbest.boot", "com.simbest.cloud" }) // 实体所在的包
@ComponentScan(basePackages = { "com.simbest.boot", "com.simbest.cloud" })
@ServletComponentScan(basePackages = { "com.simbest.boot", "com.simbest.cloud" })
@EnableJpaRepositories(basePackages = { "com.simbest.boot",
        "com.simbest.cloud" }, repositoryFactoryBeanClass = CustomJpaRepositoryFactoryBean.class) // repository 所在的包
@EnableMethodSecurity(securedEnabled = true, jsr250Enabled = true)
@EnableAsync
public class SimbestCloudApplication implements ApplicationListener<WebServerInitializedEvent> {

    @Autowired
    private ApplicationContext appContext;

    /**
     * @param args 默认参数
     */
    public static void main(String[] args) {
        SpringApplication.run(SimbestCloudApplication.class, args);
    }

    @SneakyThrows
    @Override
    public void onApplicationEvent(WebServerInitializedEvent event) {
        String[] activeProfiles = appContext.getEnvironment().getActiveProfiles();
        for (String profile : activeProfiles) {
            log.warn("加载环境信息为: 【{}】", profile);
            log.warn("Application started successfully, lets go and have fun......");
        }

        WebServer server = event.getWebServer();
        WebServerApplicationContext context = event.getApplicationContext();
        Environment env = context.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        int port = server.getPort();
        String contextPath = env.getProperty("server.servlet.context-path");
        contextPath = StringUtils.isEmpty(contextPath) ? "" : contextPath;
        log.warn("\n---------------------------------------------------------\n" +
                "\t应用已成功启动，运行地址如下：:\n" +
                "\tLocal:\t\thttp://localhost:{}{}" +
                "\n\tExternal:\thttp://{}:{}{}" +
                "\nAplication started successfully, lets go and have fun......" +
                "\n---------------------------------------------------------\n", port, contextPath, ip, port,
                contextPath);
    }

}
