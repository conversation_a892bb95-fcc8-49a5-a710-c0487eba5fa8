import { defineStore } from "pinia";
import axios from "axios";
import { ElMessage } from "element-plus";
import router from "../router";
import { UserInfo, Permission } from "./user";
import { API_BASE_URL } from "@/api";

// 用户状态接口
interface UserState {
  token: string;
  userId: number | null;
  username: string;
  name: string;
  role: string;
  permissions: string[];
  userInfo: UserInfo | null;
  loading: boolean;
  error: string | null;
}

// 使用选项式API风格定义存储
export const useUserStore = defineStore("user", {
  // 状态
  state: (): UserState => ({
    token: localStorage.getItem("token") || "",
    userId: localStorage.getItem("userId")
      ? Number(localStorage.getItem("userId"))
      : null,
    username: localStorage.getItem("username") || "",
    name: localStorage.getItem("userName") || "",
    role: localStorage.getItem("userRole") || "",
    permissions: [],
    userInfo: null,
    loading: false,
    error: null,
  }),

  // 计算属性
  getters: {
    isLoggedIn: (state) => !!state.token,
    isAdmin: (state) => state.role === "admin" || state.role === "superadmin",
    hasPermission: (state) => (permissionCode: string) =>
      state.permissions.includes(permissionCode),
  },

  // 方法
  actions: {
    // 保存用户信息到本地存储
    saveUserToLocalStorage(userToken: string, user: UserInfo) {
      localStorage.setItem("token", userToken);
      localStorage.setItem("userId", String(user.id));
      localStorage.setItem("username", user.username);
      localStorage.setItem("userName", user.name);
      if (user.role) {
        localStorage.setItem("userRole", user.role);
      }
    },

    // 从本地存储中清除用户信息
    clearUserFromLocalStorage() {
      localStorage.removeItem("token");
      localStorage.removeItem("userId");
      localStorage.removeItem("username");
      localStorage.removeItem("userName");
      localStorage.removeItem("userRole");
    },

    // 登录
    async login(loginUsername: string, password: string) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.post(`${API_BASE_URL}/auth/login/`, {
          username: loginUsername,
          password,
        });
        const { access_token, user } = response.data;

        // 更新状态
        this.token = access_token;
        this.userId = user.id;
        this.username = user.username;
        this.name = user.name;
        if (user.role) {
          this.role = user.role;
        }
        this.userInfo = user;

        // 保存到本地存储
        this.saveUserToLocalStorage(access_token, user);

        // 获取用户权限
        await this.getUserInfo();

        this.loading = false;
        return true;
      } catch (err: any) {
        this.loading = false;
        this.error =
          err.response?.data?.detail || "登录失败，请检查用户名和密码";
        ElMessage.error(this.error);
        return false;
      }
    },

    // 获取用户信息
    async getUserInfo() {
      if (!this.token) return;

      this.loading = true;
      this.error = null;

      try {
        // 获取用户信息
        const response = await axios.get(`${API_BASE_URL}/users/me/`, {
          headers: { Authorization: `Bearer ${this.token}` },
        });

        this.userInfo = response.data;

        // 如果响应中包含角色和权限信息
        if (response.data.role) {
          this.role = response.data.role;
        }

        if (response.data.permissions) {
          this.permissions = response.data.permissions.map(
            (p: Permission) => p.code
          );
        }

        this.loading = false;
      } catch (err: any) {
        this.loading = false;
        this.error = "获取用户信息失败";
        console.error("获取用户信息失败", err);

        // 如果是401错误，可能是token过期，执行登出操作
        if (err.response?.status === 401) {
          this.logout();
        }
      }
    },

    // 退出登录
    async logout() {
      this.loading = true;

      try {
        if (this.token) {
          await axios.post(
            "/api/auth/logout",
            {},
            {
              headers: { Authorization: `Bearer ${this.token}` },
            }
          );
        }
      } catch (err) {
        console.error("退出登录请求失败", err);
      } finally {
        // 重置状态
        this.resetState();

        // 跳转到登录页
        router.push("/login");
        ElMessage.success("退出登录成功");
        this.loading = false;
      }
    },

    // 重置状态
    resetState() {
      this.token = "";
      this.userId = null;
      this.username = "";
      this.name = "";
      this.role = "";
      this.permissions = [];
      this.userInfo = null;
      this.error = null;

      // 清除本地存储
      this.clearUserFromLocalStorage();
    },

    // 检查并刷新token
    async checkAndRefreshToken() {
      // 如果没有token，不需要刷新
      if (!this.token) return;

      try {
        // 这里可以添加token刷新逻辑
        // 例如，检查token是否快过期，如果是，则调用刷新token的API
        // const response = await axios.post('/api/auth/refresh-token', { refresh_token: refreshToken })
        // this.token = response.data.access_token
        // localStorage.setItem('token', this.token)
      } catch (err) {
        console.error("刷新token失败", err);
        // 如果刷新失败，可能需要重新登录
        this.logout();
      }
    },
  },

  // 持久化配置
  persist: {
    key: "user-store",
    storage: localStorage,
    paths: ["token", "userId", "username", "name", "role", "permissions"],
  },
});
