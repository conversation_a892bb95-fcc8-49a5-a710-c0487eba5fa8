import dayjs from "dayjs";

/**
 * 格式化日期
 * @param date 日期字符串或Date对象
 * @param format 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期字符串
 */
export function formatDate(
  date: string | Date | undefined,
  format = "YYYY-MM-DD HH:mm:ss"
): string {
  if (!date) return "";
  return dayjs(date).format(format);
}

/**
 * 格式化金额
 * @param amount 金额
 * @param decimals 小数位数，默认为2
 * @returns 格式化后的金额字符串
 */
export function formatPrice(amount: number | undefined, decimals = 2): string {
  if (amount === undefined) return "";
  return amount.toFixed(decimals);
}
