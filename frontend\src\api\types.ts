/**
 * API类型定义
 * 提供API相关的类型定义
 */

import type { InternalAxiosRequestConfig } from "axios";

// ==================== 请求类型 ====================

/**
 * 分页请求参数
 */
export interface PaginationParams {
  page?: number;
  size?: number;
  [key: string]: any;
}

/**
 * 排序参数
 */
export interface SortParams {
  field: string;
  order: "asc" | "desc";
}

/**
 * 过滤参数
 */
export interface FilterParams {
  field: string;
  value: any;
  operator?:
    | "eq"
    | "neq"
    | "gt"
    | "gte"
    | "lt"
    | "lte"
    | "like"
    | "in"
    | "between";
}

/**
 * 查询参数
 */
export interface QueryParams {
  pagination?: PaginationParams;
  sort?: SortParams[];
  filters?: FilterParams[];
  search?: string;
  [key: string]: any;
}

/**
 * API请求选项
 */
export interface ApiRequestOptions extends Partial<InternalAxiosRequestConfig> {
  // 是否显示加载状态
  showLoading?: boolean;
  // 是否显示错误消息
  showError?: boolean;
  // 是否显示成功消息
  showSuccess?: boolean;
  // 是否使用缓存
  useCache?: boolean;
  // 是否启用重试
  enableRetry?: boolean;
  // 自定义错误处理
  errorHandler?: (error: any) => void;
}

// ==================== 响应类型 ====================

/**
 * 标准API响应
 */
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  code?: number;
  error_code?: string;
  details?: any;
}

/**
 * 分页响应
 */
export interface PaginatedResponse<T = any> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages?: number;
}

/**
 * 错误响应
 */
export interface ErrorResponse {
  detail?: string;
  message?: string;
  error?: string;
  code?: number;
  status?: number;
}

// ==================== 认证类型 ====================

/**
 * 登录请求参数
 */
export interface LoginParams {
  username: string;
  password: string;
  remember?: boolean;
}

/**
 * 刷新令牌请求参数
 */
export interface RefreshTokenParams {
  refresh_token: string;
}

/**
 * 登录响应
 */
export interface LoginResponse {
  access_token: string;
  refresh_token?: string;
  token_type: string;
  expires_in?: number;
  user: UserInfo;
}

/**
 * 刷新令牌响应
 */
export interface RefreshTokenResponse {
  access_token: string;
  refresh_token?: string;
  token_type: string;
  expires_in?: number;
}

// ==================== 用户类型 ====================

/**
 * 用户信息
 */
export interface UserInfo {
  id: number;
  username: string;
  name: string;
  role_id: number;
  is_active: boolean;
  email?: string;
  phone?: string;
  avatar?: string;
  permissions?: string[];
}

// ==================== 销售类型 ====================

/**
 * 销售信息
 */
export interface SalesInfo {
  id: number;
  transfer_time: string;
  recipient_account_name: string;
  channel_sales_name: string;
  person_in_charge: string;
  business_platform: string;
  currency: number;
  profit: number;
  audit_status: string;
  remark?: string;
}

/**
 * 销售查询参数
 */
export interface SalesQueryParams extends PaginationParams {
  start_date?: string;
  end_date?: string;
  sales_person?: string;
  customer_name?: string;
  product_name?: string;
  audit_status?: string;
}
