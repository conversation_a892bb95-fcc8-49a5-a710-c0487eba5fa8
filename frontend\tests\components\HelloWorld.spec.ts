import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import HelloWorld from '@/components/HelloWorld.vue'

describe('HelloWorld组件测试', () => {
  it('应该正确渲染传入的prop', () => {
    const msg = '欢迎使用Vue 3'
    const wrapper = mount(HelloWorld, {
      props: { msg }
    })
    
    // 验证h1标签包含传入的消息
    expect(wrapper.find('h1').text()).toBe(msg)
  })
  
  it('按钮点击时应该增加计数', async () => {
    const wrapper = mount(HelloWorld, {
      props: { msg: 'Hello' }
    })
    
    // 获取按钮元素
    const button = wrapper.find('button')
    
    // 验证初始计数为0
    expect(button.text()).toContain('count is 0')
    
    // 点击按钮
    await button.trigger('click')
    
    // 验证计数增加到1
    expect(button.text()).toContain('count is 1')
    
    // 再次点击
    await button.trigger('click')
    
    // 验证计数增加到2
    expect(button.text()).toContain('count is 2')
  })
  
  it('组件应该包含必要的链接', () => {
    const wrapper = mount(HelloWorld, {
      props: { msg: 'Test' }
    })
    
    // 验证组件包含链接
    const links = wrapper.findAll('a')
    expect(links.length).toBeGreaterThan(0)
    
    // 验证链接内容
    const linkTexts = links.map(link => link.text())
    expect(linkTexts.some(text => text.includes('create-vue'))).toBe(true)
    
    // 验证链接属性
    const vueDocsLink = wrapper.find('a[href*="vuejs.org"]')
    expect(vueDocsLink.exists()).toBe(true)
    expect(vueDocsLink.attributes('target')).toBe('_blank')
  })
  
  it('渲染样式正确', () => {
    const wrapper = mount(HelloWorld, {
      props: { msg: 'Styled Component' }
    })
    
    // 验证组件包含特定的样式类
    const styledElement = wrapper.find('.read-the-docs')
    expect(styledElement.exists()).toBe(true)
  })
}) 