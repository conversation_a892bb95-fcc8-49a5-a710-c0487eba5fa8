<template>
  <div class="user-detail-container">
    <PageHeader title="用户详情">
      <template #actions>
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>返回列表
        </el-button>
        <el-button type="primary" @click="handleEdit">
          <el-icon><Edit /></el-icon>编辑
        </el-button>
      </template>
    </PageHeader>

    <el-card class="detail-card" v-loading="loading">
      <div class="user-info">
        <div class="avatar-container">
          <el-avatar 
            :size="100" 
            :src="userInfo.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" 
          />
        </div>
        
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="用户ID">{{ userInfo.id }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ userInfo.username }}</el-descriptions-item>
          <el-descriptions-item label="姓名">{{ userInfo.name }}</el-descriptions-item>
          <el-descriptions-item label="角色">
            <el-tag :type="getRoleType(userInfo.role)">{{ getRoleName(userInfo.role) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ userInfo.email }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ userInfo.phone || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="userInfo.status === 'active' ? 'success' : 'danger'">
              {{ userInfo.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(userInfo.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDate(userInfo.updated_at) }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="permissions-section" v-if="userInfo.permissions && userInfo.permissions.length">
        <h3>权限列表</h3>
        <el-tag 
          v-for="permission in userInfo.permissions" 
          :key="permission" 
          class="permission-tag"
        >
          {{ permission }}
        </el-tag>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Back, Edit } from '@element-plus/icons-vue';
import { PageHeader } from '@/components';
import { getUserDetail } from '@/api/modules/user';
import type { UserInfo } from '@/api/modules/user';

const router = useRouter();
const route = useRoute();
const loading = ref(false);

// 用户信息
const userInfo = ref<UserInfo>({
  id: 0,
  username: '',
  name: '',
  email: '',
  phone: '',
  role: '',
  status: '',
  avatar: '',
  permissions: [],
  created_at: '',
  updated_at: ''
});

// 获取用户详情
const getUserData = async () => {
  const id = Number(route.params.id);
  if (!id) {
    ElMessage.error('用户ID无效');
    goBack();
    return;
  }

  loading.value = true;
  try {
    const data = await getUserDetail(id);
    userInfo.value = data;
  } catch (error) {
    console.error('获取用户详情失败', error);
    ElMessage.error('获取用户详情失败');
  } finally {
    loading.value = false;
  }
};

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 获取角色名称
const getRoleName = (role: string) => {
  const roleMap: Record<string, string> = {
    admin: '管理员',
    user: '普通用户',
    guest: '访客'
  };
  return roleMap[role] || role;
};

// 获取角色标签类型
const getRoleType = (role: string) => {
  const typeMap: Record<string, string> = {
    admin: 'danger',
    user: 'primary',
    guest: 'info'
  };
  return typeMap[role] || 'info';
};

// 编辑用户
const handleEdit = () => {
  router.push(`/user/edit/${userInfo.value.id}`);
};

// 返回列表
const goBack = () => {
  router.push('/user/list');
};

// 页面加载时获取数据
onMounted(() => {
  getUserData();
});
</script>

<style scoped>
.user-detail-container {
  padding: 20px;
}

.detail-card {
  margin-top: 20px;
}

.user-info {
  margin-bottom: 30px;
}

.avatar-container {
  text-align: center;
  margin-bottom: 20px;
}

.permissions-section {
  margin-top: 30px;
}

.permission-tag {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
