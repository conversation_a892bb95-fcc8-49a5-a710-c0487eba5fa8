/**
 * 响应式布局工具
 */
import { ref, onMounted, onUnmounted, computed } from 'vue'

// 断点定义
export const breakpoints = {
  xs: 0,    // 超小屏幕 - 手机竖屏
  sm: 576,  // 小屏幕 - 手机横屏
  md: 768,  // 中等屏幕 - 平板
  lg: 992,  // 大屏幕 - 桌面
  xl: 1200, // 超大屏幕 - 宽屏桌面
  xxl: 1600 // 巨大屏幕 - 超宽屏
}

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop'

/**
 * 使用响应式布局
 */
export function useResponsive() {
  // 当前窗口宽度
  const windowWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 0)
  
  // 更新窗口宽度
  const updateWidth = () => {
    windowWidth.value = window.innerWidth
  }
  
  // 计算当前断点
  const currentBreakpoint = computed(() => {
    const width = windowWidth.value
    
    if (width < breakpoints.sm) return 'xs'
    if (width < breakpoints.md) return 'sm'
    if (width < breakpoints.lg) return 'md'
    if (width < breakpoints.xl) return 'lg'
    if (width < breakpoints.xxl) return 'xl'
    return 'xxl'
  })
  
  // 计算设备类型
  const deviceType = computed((): DeviceType => {
    const width = windowWidth.value
    
    if (width < breakpoints.md) return 'mobile'
    if (width < breakpoints.lg) return 'tablet'
    return 'desktop'
  })
  
  // 是否为移动设备
  const isMobile = computed(() => deviceType.value === 'mobile')
  
  // 是否为平板设备
  const isTablet = computed(() => deviceType.value === 'tablet')
  
  // 是否为桌面设备
  const isDesktop = computed(() => deviceType.value === 'desktop')
  
  // 是否为小屏幕
  const isSmallScreen = computed(() => ['xs', 'sm'].includes(currentBreakpoint.value))
  
  // 是否为中等屏幕
  const isMediumScreen = computed(() => ['md', 'lg'].includes(currentBreakpoint.value))
  
  // 是否为大屏幕
  const isLargeScreen = computed(() => ['xl', 'xxl'].includes(currentBreakpoint.value))
  
  // 根据断点获取值
  const getValueByBreakpoint = <T>(values: Record<string, T>, defaultValue?: T): T => {
    const bp = currentBreakpoint.value
    
    // 尝试获取当前断点的值
    if (values[bp] !== undefined) {
      return values[bp]
    }
    
    // 尝试获取较小断点的值
    const breakpointKeys = Object.keys(breakpoints)
    const currentIndex = breakpointKeys.indexOf(bp)
    
    for (let i = currentIndex - 1; i >= 0; i--) {
      const key = breakpointKeys[i]
      if (values[key] !== undefined) {
        return values[key]
      }
    }
    
    // 尝试获取较大断点的值
    for (let i = currentIndex + 1; i < breakpointKeys.length; i++) {
      const key = breakpointKeys[i]
      if (values[key] !== undefined) {
        return values[key]
      }
    }
    
    // 返回默认值
    return defaultValue as T
  }
  
  // 生命周期钩子
  onMounted(() => {
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', updateWidth)
      updateWidth()
    }
  })
  
  onUnmounted(() => {
    if (typeof window !== 'undefined') {
      window.removeEventListener('resize', updateWidth)
    }
  })
  
  return {
    windowWidth,
    currentBreakpoint,
    deviceType,
    isMobile,
    isTablet,
    isDesktop,
    isSmallScreen,
    isMediumScreen,
    isLargeScreen,
    getValueByBreakpoint
  }
}

/**
 * 创建响应式样式
 */
export function createResponsiveStyles(styles: Record<string, Record<string, string>>) {
  const { currentBreakpoint, getValueByBreakpoint } = useResponsive()
  
  // 计算当前样式
  const computedStyles = computed(() => {
    const result: Record<string, string> = {}
    
    // 遍历样式对象
    Object.entries(styles).forEach(([property, values]) => {
      const value = getValueByBreakpoint(values)
      if (value !== undefined) {
        result[property] = value
      }
    })
    
    return result
  })
  
  return {
    currentBreakpoint,
    styles: computedStyles
  }
}

/**
 * 创建响应式类名
 */
export function createResponsiveClasses(classMap: Record<string, Record<string, boolean>>) {
  const { currentBreakpoint, getValueByBreakpoint } = useResponsive()
  
  // 计算当前类名
  const computedClasses = computed(() => {
    const result: Record<string, boolean> = {}
    
    // 遍历类名映射
    Object.entries(classMap).forEach(([className, values]) => {
      const value = getValueByBreakpoint(values, false)
      result[className] = value
    })
    
    return result
  })
  
  return {
    currentBreakpoint,
    classes: computedClasses
  }
}

export default useResponsive
