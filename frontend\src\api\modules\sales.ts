/**
 * 销售API模块
 * 提供销售相关的API请求功能
 */

import { get, post, put, del } from "../core/http";
import type { PaginationParams, PaginatedResponse } from "../types";

// 销售查询参数接口
export interface SalesQueryParams extends PaginationParams {
  customer_name?: string;
  product_name?: string;
  start_date?: string;
  end_date?: string;
  audit_status?: string;
  sales_person?: string;
}

// 销售创建接口
export interface SalesCreateData {
  customer_name: string;
  product_name: string;
  quantity: number;
  unit_price: number;
  currency: string;
  exchange_rate: number;
  sales_date: string;
  port_policy: number;
  customer_policy: number;
  sales_person: string;
  notes?: string;
}

// 销售更新接口
export interface SalesUpdateData {
  customer_name?: string;
  product_name?: string;
  quantity?: number;
  unit_price?: number;
  currency?: string;
  exchange_rate?: number;
  sales_date?: string;
  port_policy?: number;
  customer_policy?: number;
  sales_person?: string;
  notes?: string;
}

// 销售信息接口
export interface SalesInfo {
  id: number;
  customer_name: string;
  product_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  currency: string;
  exchange_rate: number;
  sales_date: string;
  port_policy: number;
  customer_policy: number;
  port_recharge: number;
  receivables: number;
  reserves: number;
  advances: number;
  profit: number;
  audit_status: string;
  sales_person: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

// 销售列表响应接口
export interface SalesListResponse extends PaginatedResponse<SalesInfo> {}

/**
 * 获取销售列表
 * @param params 查询参数
 * @returns Promise<SalesListResponse>
 */
export async function getSalesList(
  params: SalesQueryParams
): Promise<SalesListResponse> {
  return get<SalesListResponse>("/sales/", params);
}

/**
 * 获取销售详情
 * @param salesId 销售ID
 * @returns Promise<SalesInfo>
 */
export async function getSalesDetail(salesId: number): Promise<SalesInfo> {
  return get<SalesInfo>(`/sales/${salesId}/`);
}

/**
 * 创建销售记录
 * @param data 销售数据
 * @returns Promise<SalesInfo>
 */
export async function createSales(data: SalesCreateData): Promise<SalesInfo> {
  return post<SalesInfo>("/sales/", data);
}

/**
 * 更新销售记录
 * @param salesId 销售ID
 * @param data 更新数据
 * @returns Promise<SalesInfo>
 */
export async function updateSales(
  salesId: number,
  data: SalesUpdateData
): Promise<SalesInfo> {
  return put<SalesInfo>(`/sales/${salesId}/`, data);
}

/**
 * 删除销售记录
 * @param salesId 销售ID
 * @returns Promise<void>
 */
export async function deleteSales(salesId: number): Promise<void> {
  return del<void>(`/sales/${salesId}/`);
}

/**
 * 审核销售记录
 * @param salesId 销售ID
 * @param auditStatus 审核状态
 * @param auditNotes 审核备注
 * @returns Promise<SalesInfo>
 */
export async function auditSales(
  salesId: number,
  auditStatus: string,
  auditNotes?: string
): Promise<SalesInfo> {
  return post<SalesInfo>(`/sales/${salesId}/audit/`, {
    audit_status: auditStatus,
    audit_notes: auditNotes,
  });
}

/**
 * 批量导出销售记录
 * @param params 查询参数
 * @returns Promise<Blob>
 */
export async function exportSales(params: SalesQueryParams): Promise<Blob> {
  return get<Blob>("/sales/export/", params, {
    responseType: "blob",
  });
}

/**
 * 获取销售统计数据
 * @param params 查询参数
 * @returns Promise<any>
 */
export async function getSalesStats(params?: SalesQueryParams): Promise<any> {
  return get<any>("/sales/stats/", params);
}

export default {
  getSalesList,
  getSalesDetail,
  createSales,
  updateSales,
  deleteSales,
  auditSales,
  exportSales,
  getSalesStats,
};
