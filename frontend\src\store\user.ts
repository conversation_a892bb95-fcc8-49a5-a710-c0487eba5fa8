import { defineStore } from "pinia";
import axios from "axios";
import { ElMessage } from "element-plus";
import router from "../router";
import { ref, computed } from "vue";
import { API_BASE_URL } from "@/api";

// 用户信息类型定义
export interface UserInfo {
  id: number;
  username: string;
  name: string;
  email?: string;
  avatar?: string;
  role?: string;
  roleId?: number;
  isActive?: boolean;
  lastLogin?: string;
  createdAt?: string;
  updatedAt?: string;
}

// 权限类型定义
export interface Permission {
  id: number;
  name: string;
  code: string;
  description?: string;
}

// 使用组合式API风格定义存储
export const useUserStore = defineStore("user", () => {
  // 状态
  const token = ref<string>(localStorage.getItem("token") || "");
  const userId = ref<number | null>(
    localStorage.getItem("userId")
      ? Number(localStorage.getItem("userId"))
      : null
  );
  const username = ref<string>(localStorage.getItem("username") || "");
  const name = ref<string>(localStorage.getItem("userName") || "");
  const role = ref<string>(localStorage.getItem("userRole") || "");
  const permissions = ref<string[]>([]);
  const userInfo = ref<UserInfo | null>(null);
  const loading = ref<boolean>(false);
  const error = ref<string | null>(null);

  // 计算属性
  const isLoggedIn = computed(() => !!token.value);
  const hasPermission = (permissionCode: string) =>
    permissions.value.includes(permissionCode);
  const isAdmin = computed(
    () => role.value === "admin" || role.value === "superadmin"
  );

  // 保存用户信息到本地存储
  function saveUserToLocalStorage(userToken: string, user: UserInfo) {
    localStorage.setItem("token", userToken);
    localStorage.setItem("userId", String(user.id));
    localStorage.setItem("username", user.username);
    localStorage.setItem("userName", user.name);
    if (user.role) {
      localStorage.setItem("userRole", user.role);
    }
  }

  // 从本地存储中清除用户信息
  function clearUserFromLocalStorage() {
    localStorage.removeItem("token");
    localStorage.removeItem("userId");
    localStorage.removeItem("username");
    localStorage.removeItem("userName");
    localStorage.removeItem("userRole");
  }

  // 登录
  async function login(loginUsername: string, password: string) {
    loading.value = true;
    error.value = null;

    try {
      const response = await axios.post(`${API_BASE_URL}/auth/login/`, {
        username: loginUsername,
        password,
      });
      const { access_token, user } = response.data;

      // 更新状态
      token.value = access_token;
      userId.value = user.id;
      username.value = user.username;
      name.value = user.name;
      if (user.role) {
        role.value = user.role;
      }
      userInfo.value = user;

      // 保存到本地存储
      saveUserToLocalStorage(access_token, user);

      // 获取用户权限
      await getUserInfo();

      loading.value = false;
      return true;
    } catch (err: any) {
      loading.value = false;
      error.value =
        err.response?.data?.detail || "登录失败，请检查用户名和密码";
      ElMessage.error(error.value);
      return false;
    }
  }

  // 获取用户信息
  async function getUserInfo() {
    if (!token.value) return;

    loading.value = true;
    error.value = null;

    try {
      // 获取用户信息
      const response = await axios.get(`${API_BASE_URL}/users/me/`, {
        headers: { Authorization: `Bearer ${token.value}` },
      });

      userInfo.value = response.data;

      // 如果响应中包含角色和权限信息
      if (response.data.role) {
        role.value = response.data.role;
      }

      if (response.data.permissions) {
        permissions.value = response.data.permissions.map(
          (p: Permission) => p.code
        );
      }

      loading.value = false;
    } catch (err: any) {
      loading.value = false;
      error.value = "获取用户信息失败";
      console.error("获取用户信息失败", err);

      // 如果是401错误，可能是token过期，执行登出操作
      if (err.response?.status === 401) {
        logout();
      }
    }
  }

  // 退出登录
  async function logout() {
    loading.value = true;

    try {
      if (token.value) {
        await axios.post(
          "/api/auth/logout",
          {},
          {
            headers: { Authorization: `Bearer ${token.value}` },
          }
        );
      }
    } catch (err) {
      console.error("退出登录请求失败", err);
    } finally {
      // 重置状态
      resetState();

      // 跳转到登录页
      router.push("/login");
      ElMessage.success("退出登录成功");
      loading.value = false;
    }
  }

  // 重置状态
  function resetState() {
    token.value = "";
    userId.value = null;
    username.value = "";
    name.value = "";
    role.value = "";
    permissions.value = [];
    userInfo.value = null;
    error.value = null;

    // 清除本地存储
    clearUserFromLocalStorage();
  }

  // 检查并刷新token
  async function checkAndRefreshToken() {
    // 如果没有token，不需要刷新
    if (!token.value) return;

    try {
      // 这里可以添加token刷新逻辑
      // 例如，检查token是否快过期，如果是，则调用刷新token的API
      // const response = await axios.post('/api/auth/refresh-token', { refresh_token: refreshToken })
      // token.value = response.data.access_token
      // localStorage.setItem('token', token.value)
    } catch (err) {
      console.error("刷新token失败", err);
      // 如果刷新失败，可能需要重新登录
      logout();
    }
  }

  // 返回状态和方法
  return {
    // 状态
    token,
    userId,
    username,
    name,
    role,
    permissions,
    userInfo,
    loading,
    error,

    // 计算属性
    isLoggedIn,
    isAdmin,

    // 方法
    login,
    logout,
    getUserInfo,
    resetState,
    hasPermission,
    checkAndRefreshToken,
  };
});
