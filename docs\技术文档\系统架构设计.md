# 日常审批系统架构设计

## 系统概述

日常审批系统是为教育局设计的一个集成化审批管理平台，包含五个主要审批流程：出差申请、请假申请、车辆申请、会议申请和办公用品申请。系统采用微服务架构，后端使用Java技术栈，前端使用Vue3框架开发，并集成Camunda流程引擎实现标准化的工作流管理。

## 系统目标

1. 提供统一的审批平台，规范审批流程
2. 支持不同类型审批的个性化配置
3. 实现审批过程的全程可追溯
4. 提供友好的用户界面和便捷的操作体验
5. 确保系统具备可扩展性，便于后续增加新的审批类型
6. 实现业务与流程的解耦，提高系统维护性
7. 支持流程的可视化设计和监控

## 技术架构

### 后端技术栈
- 开发语言：Java 11+
- 框架：Spring Boot 2.7.x
- 微服务框架：Spring Cloud
- 流程引擎：Camunda BPM Platform 7.18+
- ORM框架：MyBatis-Plus
- 数据库：MySQL 8.0
- 缓存：Redis
- 消息队列：RabbitMQ
- 认证授权：Spring Security + JWT
- API文档：Swagger/Knife4j
- 服务注册与发现：Eureka/Nacos
- 服务网关：Spring Cloud Gateway

### 前端技术栈
- 框架：Vue 3
- 构建工具：Vite
- UI组件库：Element Plus
- 状态管理：Pinia
- 路由：Vue Router
- HTTP客户端：Axios
- 表单验证：VeeValidate
- 流程图展示：bpmn.js

## 系统架构图

```
┌───────────────────────────────────────────────────────────────────────────┐
│                                前端应用层                                   │
│                                                                           │
│  ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────┐ ┌─────────┐ │
│  │  用户管理  │ │  审批管理 │ │ 流程配置  │ │ 统计分析  │ │ 系统 │ │流程监控  │ │
│  └──────────┘ └──────────┘ └──────────┘ └──────────┘ └──────┘ └─────────┘ │
└─────────────────────────────────┬─────────────────────────────────────────┘
                                  │
                                  │ HTTP/JSON
                                  ▼
┌───────────────────────────────────────────────────────────────────────────┐
│                                API网关层                                   │
│                                                                           │
│                       Spring Cloud Gateway / Nginx                         │
└─────────────────────────────────┬─────────────────────────────────────────┘
                                  │
                                  ▼
┌───────────────────────────────────────────────────────────────────────────┐
│                               服务注册中心                                  │
│                                                                           │
│                              Eureka / Nacos                               │
└─────────────────────────────────┬─────────────────────────────────────────┘
                                  │
                                  ▼
┌───────────────────────────────────────────────────────────────────────────┐
│                               微服务集群                                    │
│                                                                           │
│  ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐  ┌───────────────┐   │
│  │          │ │          │ │          │ │          │  │               │   │
│  │ 用户服务  │ │ 审批服务  │ │ 通知服务  │ │ 系统服务  │  │  配置中心服务  │   │
│  │          │ │          │ │          │ │          │  │               │   │
│  └──────────┘ └─────┬────┘ └──────────┘ └──────────┘  └───────────────┘   │
│                     │                                                      │
│                     ▼                                                      │
│  ┌────────────────────────────────────┐                                   │
│  │                                    │                                   │
│  │       Camunda工作流引擎服务          │                                   │
│  │                                    │                                   │
│  │  ┌──────────┐ ┌──────────────┐    │                                   │
│  │  │ Modeler  │ │   Cockpit    │    │                                   │
│  │  └──────────┘ └──────────────┘    │                                   │
│  │                                    │                                   │
│  │  ┌──────────┐ ┌──────────────┐    │                                   │
│  │  │ Tasklist │ │    Admin     │    │                                   │
│  │  └──────────┘ └──────────────┘    │                                   │
│  └────────────────────────────────────┘                                   │
│                                                                           │
└─────────────────────────────────┬─────────────────────────────────────────┘
                                  │
                                  ▼
┌───────────────────────────────────────────────────────────────────────────┐
│                               数据持久层                                    │
│                                                                           │
│  ┌──────────┐ ┌──────────┐ ┌──────────┐ ┌──────────┐  ┌────────────┐      │
│  │          │ │          │ │          │ │          │  │            │      │
│  │  MySQL   │ │  Redis   │ │ 文件存储  │ │ RabbitMQ │  │  Camunda DB │      │
│  │          │ │          │ │          │ │          │  │            │      │
│  └──────────┘ └──────────┘ └──────────┘ └──────────┘  └────────────┘      │
│                                                                           │
└───────────────────────────────────────────────────────────────────────────┘
```

## 系统模块划分

### 核心模块

1. **用户权限模块**
   - 用户管理
   - 角色管理
   - 部门管理
   - 权限控制

2. **流程管理模块**
   - 流程定义与部署
   - BPMN流程设计
   - 流程版本管理
   - 审批规则配置

3. **审批处理模块**
   - 审批任务管理
   - 审批流转控制
   - 审批历史查询
   - 审批统计分析

4. **消息通知模块**
   - 站内消息
   - 邮件通知
   - 提醒设置

5. **系统管理模块**
   - 系统配置
   - 日志管理
   - 数据字典

6. **Camunda集成模块**
   - Camunda引擎配置
   - 流程定义管理
   - 任务监听器
   - 流程服务委托

### 业务模块

1. **出差申请模块**
   - 表单管理
   - 流程配置
   - 数据统计

2. **请假申请模块**
   - 表单管理
   - 流程配置
   - 数据统计

3. **车辆申请模块**
   - 表单管理
   - 流程配置
   - 数据统计

4. **会议申请模块**
   - 表单管理
   - 流程配置
   - 数据统计

5. **办公用品申请模块**
   - 表单管理
   - 流程配置
   - 数据统计

## 微服务划分

1. **用户服务(user-service)**
   - 用户账号管理
   - 部门组织管理
   - 角色权限管理

2. **审批服务(approval-service)**
   - 业务表单处理
   - 业务数据管理
   - 与工作流引擎集成

3. **工作流服务(workflow-service)**
   - 封装Camunda API
   - 流程定义管理
   - 流程实例控制
   - 任务处理接口

4. **通知服务(notification-service)**
   - 消息模板管理
   - 消息发送队列
   - 多渠道通知

5. **系统服务(system-service)**
   - 系统参数配置
   - 日志审计
   - 数据字典维护

6. **网关服务(gateway-service)**
   - 请求路由
   - 统一认证
   - 限流控制

## 部署架构

系统采用基于Docker的微服务部署架构：

1. **容器编排**：使用Docker Compose/Kubernetes进行容器编排
2. **服务发现**：通过服务注册中心实现服务自动发现
3. **负载均衡**：网关层和服务间通信实现负载均衡
4. **配置中心**：统一管理各服务配置信息
5. **监控系统**：集成Prometheus + Grafana进行系统监控

### 部署图

```
┌─────────────────────────────────────────────────────────────┐
│                        Kubernetes集群                        │
│                                                             │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌───────┐  │
│  │ Gateway │ │ Frontend│ │ Config  │ │ Monitor │ │ Log   │  │
│  │  Pod    │ │  Pod    │ │  Pod    │ │  Pod    │ │ Pod   │  │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘ └───────┘  │
│                                                             │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌───────┐  │
│  │  User   │ │ Approval│ │ Workflow│ │ Notify  │ │ System│  │
│  │ Service │ │ Service │ │ Service │ │ Service │ │Service│  │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘ └───────┘  │
│                                                             │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐            │
│  │  MySQL  │ │  Redis  │ │ RabbitMQ│ │ Camunda │            │
│  │         │ │         │ │         │ │         │            │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘            │
└─────────────────────────────────────────────────────────────┘
```

## 扩展性设计

1. **流程扩展**：基于Camunda的BPMN标准，可以通过可视化设计器快速添加和修改审批流程
2. **表单扩展**：采用动态表单设计，支持自定义表单字段和验证规则
3. **角色扩展**：支持灵活的角色定义和权限分配
4. **业务扩展**：基于微服务架构，可以方便地添加新的业务服务
5. **服务可伸缩**：各微服务支持水平扩展，按需增减实例数

## 安全性设计

1. **认证授权**：基于JWT的身份认证和基于角色的权限控制
2. **数据安全**：敏感数据加密存储，防止信息泄露
3. **操作审计**：系统操作日志记录，确保所有操作可追溯
4. **接口安全**：API接口访问控制和防攻击措施
5. **服务间安全**：服务间通信采用HTTPS和JWT验证
6. **容器安全**：限制容器资源和权限，防止容器逃逸

## 性能考虑

1. **服务拆分**：将高频服务和低频服务分离，避免互相影响
2. **数据库优化**：合理的数据库索引设计和SQL优化
3. **缓存策略**：使用Redis缓存热点数据，减轻数据库压力
4. **异步处理**：通过消息队列实现异步通知和非核心流程异步处理
5. **前端优化**：组件懒加载，资源压缩等措施提升前端性能
6. **Camunda优化**：配置合适的历史级别，避免过多历史数据

## Camunda集成架构

### 集成方式

1. **REST API集成**
   - 业务服务通过REST API与Camunda服务交互
   - 使用统一的API网关进行路由和安全控制

2. **Spring Boot集成**
   - 工作流服务基于Spring Boot Starter for Camunda
   - 提供流程管理和任务处理的统一接口

3. **事件驱动集成**
   - 流程事件通过消息队列传递给相关业务服务
   - 支持异步任务处理和流程状态更新

### Camunda组件使用

1. **Camunda Engine**：核心流程执行引擎，处理流程定义和实例
2. **Camunda Modeler**：流程设计工具，支持BPMN 2.0标准
3. **Camunda Cockpit**：流程监控工具，实时查看流程执行状态
4. **Camunda Tasklist**：任务管理工具，处理用户任务
5. **Camunda Admin**：管理工具，配置用户和权限 