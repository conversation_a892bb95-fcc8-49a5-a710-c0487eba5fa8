<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_0" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="meeting-reservation-process" name="教育局会议预定流程" isExecutable="true">
    <bpmn:startEvent id="start_event" name="开始">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>

    <bpmn:sequenceFlow id="Flow_1" sourceRef="start_event" targetRef="dept_leader_approve" />

    <!-- 部门负责人审批 -->
    <bpmn:userTask id="dept_leader_approve" name="部门负责人审批">
      <bpmn:extensionElements>
        <zeebe:assignmentDefinition assignee="#{assignee}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>

    <bpmn:sequenceFlow id="Flow_2" sourceRef="dept_leader_approve" targetRef="gateway_1" />

    <bpmn:exclusiveGateway id="gateway_1" name="部门审批结果">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
      <bpmn:outgoing>Flow_reject_1</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <bpmn:sequenceFlow id="Flow_3" name="通过" sourceRef="gateway_1" targetRef="check_meeting_type">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= deptApproved = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="Flow_reject_1" name="不通过" sourceRef="gateway_1" targetRef="end_event_fail">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= deptApproved = false</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <!-- 检查会议类型网关 -->
    <bpmn:exclusiveGateway id="check_meeting_type" name="检查会议类型">
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <bpmn:sequenceFlow id="Flow_4" name="重要会议" sourceRef="check_meeting_type" targetRef="bureau_leader_approve">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= meetingType = "important"</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="Flow_5" name="普通会议" sourceRef="check_meeting_type" targetRef="admin_office_approve">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= meetingType = "normal"</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <!-- 分管局长审批 -->
    <bpmn:userTask id="bureau_leader_approve" name="分管局长审批">
      <bpmn:extensionElements>
        <zeebe:assignmentDefinition assignee="#{assignee}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>

    <bpmn:sequenceFlow id="Flow_6" sourceRef="bureau_leader_approve" targetRef="gateway_2" />

    <bpmn:exclusiveGateway id="gateway_2" name="局长审批结果">
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_7</bpmn:outgoing>
      <bpmn:outgoing>Flow_reject_2</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <bpmn:sequenceFlow id="Flow_7" name="通过" sourceRef="gateway_2" targetRef="admin_office_approve">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= bureauApproved = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="Flow_reject_2" name="不通过" sourceRef="gateway_2" targetRef="end_event_fail">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= bureauApproved = false</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <!-- 行政办公室审批 -->
    <bpmn:userTask id="admin_office_approve" name="行政办公室审批">
      <bpmn:extensionElements>
        <zeebe:assignmentDefinition assignee="#{assignee}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:incoming>Flow_7</bpmn:incoming>
      <bpmn:outgoing>Flow_8</bpmn:outgoing>
    </bpmn:userTask>

    <bpmn:sequenceFlow id="Flow_8" sourceRef="admin_office_approve" targetRef="gateway_3" />

    <bpmn:exclusiveGateway id="gateway_3" name="行政办公室审批结果">
      <bpmn:incoming>Flow_8</bpmn:incoming>
      <bpmn:outgoing>Flow_9</bpmn:outgoing>
      <bpmn:outgoing>Flow_reject_3</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <bpmn:sequenceFlow id="Flow_9" name="通过" sourceRef="gateway_3" targetRef="end_event_success">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= adminOfficeApproved = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="Flow_reject_3" name="不通过" sourceRef="gateway_3" targetRef="end_event_fail">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= adminOfficeApproved = false</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:endEvent id="end_event_success" name="申请通过">
      <bpmn:incoming>Flow_9</bpmn:incoming>
    </bpmn:endEvent>

    <bpmn:endEvent id="end_event_fail" name="申请未通过">
      <bpmn:incoming>Flow_reject_1</bpmn:incoming>
      <bpmn:incoming>Flow_reject_2</bpmn:incoming>
      <bpmn:incoming>Flow_reject_3</bpmn:incoming>
    </bpmn:endEvent>
  </bpmn:process>

  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="meeting-reservation-process">
      <!-- Start Event -->
      <bpmndi:BPMNShape id="StartEvent_1" bpmnElement="start_event">
        <dc:Bounds x="152" y="102" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="145" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>

      <!-- Department Leader Approval -->
      <bpmndi:BPMNShape id="Activity_1" bpmnElement="dept_leader_approve">
        <dc:Bounds x="240" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>

      <!-- Gateway 1 -->
      <bpmndi:BPMNShape id="Gateway_1" bpmnElement="gateway_1" isMarkerVisible="true">
        <dc:Bounds x="395" y="95" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="385" y="65" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>

      <!-- Meeting Type Gateway -->
      <bpmndi:BPMNShape id="Gateway_2" bpmnElement="check_meeting_type" isMarkerVisible="true">
        <dc:Bounds x="505" y="95" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="495" y="65" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>

      <!-- Bureau Leader Approval -->
      <bpmndi:BPMNShape id="Activity_2" bpmnElement="bureau_leader_approve">
        <dc:Bounds x="610" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>

      <!-- Gateway 2 -->
      <bpmndi:BPMNShape id="Gateway_3" bpmnElement="gateway_2" isMarkerVisible="true">
        <dc:Bounds x="765" y="95" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="755" y="65" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>

      <!-- Admin Office Approval -->
      <bpmndi:BPMNShape id="Activity_3" bpmnElement="admin_office_approve">
        <dc:Bounds x="610" y="190" width="100" height="80" />
      </bpmndi:BPMNShape>

      <!-- Gateway 3 -->
      <bpmndi:BPMNShape id="Gateway_4" bpmnElement="gateway_3" isMarkerVisible="true">
        <dc:Bounds x="765" y="205" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="755" y="175" width="70" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>

      <!-- Success End Event -->
      <bpmndi:BPMNShape id="Event_Success" bpmnElement="end_event_success">
        <dc:Bounds x="872" y="212" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="868" y="255" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>

      <!-- Fail End Event -->
      <bpmndi:BPMNShape id="Event_Fail" bpmnElement="end_event_fail">
        <dc:Bounds x="872" y="302" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="863" y="345" width="55" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>

      <!-- Sequence Flows -->
      <bpmndi:BPMNEdge id="Flow_1_di" bpmnElement="Flow_1">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="240" y="120" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="Flow_2_di" bpmnElement="Flow_2">
        <di:waypoint x="340" y="120" />
        <di:waypoint x="395" y="120" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="Flow_3_di" bpmnElement="Flow_3">
        <di:waypoint x="445" y="120" />
        <di:waypoint x="505" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="464" y="102" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="Flow_reject_1_di" bpmnElement="Flow_reject_1">
        <di:waypoint x="420" y="145" />
        <di:waypoint x="420" y="320" />
        <di:waypoint x="872" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="415" y="230" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="Flow_4_di" bpmnElement="Flow_4">
        <di:waypoint x="555" y="120" />
        <di:waypoint x="610" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="563" y="102" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="Flow_5_di" bpmnElement="Flow_5">
        <di:waypoint x="530" y="145" />
        <di:waypoint x="530" y="230" />
        <di:waypoint x="610" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="525" y="185" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="Flow_6_di" bpmnElement="Flow_6">
        <di:waypoint x="710" y="120" />
        <di:waypoint x="765" y="120" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="Flow_7_di" bpmnElement="Flow_7">
        <di:waypoint x="790" y="145" />
        <di:waypoint x="790" y="230" />
        <di:waypoint x="710" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="785" y="185" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="Flow_reject_2_di" bpmnElement="Flow_reject_2">
        <di:waypoint x="790" y="145" />
        <di:waypoint x="790" y="320" />
        <di:waypoint x="872" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="785" y="230" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="Flow_8_di" bpmnElement="Flow_8">
        <di:waypoint x="710" y="230" />
        <di:waypoint x="765" y="230" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="Flow_9_di" bpmnElement="Flow_9">
        <di:waypoint x="815" y="230" />
        <di:waypoint x="872" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="834" y="212" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="Flow_reject_3_di" bpmnElement="Flow_reject_3">
        <di:waypoint x="790" y="255" />
        <di:waypoint x="790" y="320" />
        <di:waypoint x="872" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="785" y="285" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions> 