import { describe, it, expect } from 'vitest'
import axios from 'axios'
import { performance } from 'perf_hooks'

/**
 * 数据库性能测试
 * 此测试主要关注数据库相关操作的性能，如大数据集查询、批量操作等
 */
describe('数据库性能测试', () => {
  // 测试配置
  const BASE_URL = process.env.API_BASE_URL || 'http://localhost:8000'
  let token: string | null = null
  
  // 性能基准（毫秒）
  const DB_PERFORMANCE_BENCHMARKS = {
    LARGE_QUERY: 3000,    // 大数据集查询
    BATCH_INSERT: 5000,   // 批量插入操作
    COMPLEX_JOIN: 2000,   // 复杂连接查询
    AGGREGATE: 4000       // 聚合统计操作
  }
  
  // 跳过标志，如果无法连接到API，将跳过测试
  let skipTests = false
  
  // 测试前获取令牌
  beforeEach(async () => {
    if (skipTests) return
    
    if (!token) {
      try {
        const response = await axios.post(`${BASE_URL}/api/auth/login`, {
          username: 'admin',
          password: 'admin123'
        })
        token = response.data.access_token
      } catch (error) {
        console.warn('无法登录API，数据库性能测试将被跳过')
        skipTests = true
      }
    }
  })
  
  // 由于这些测试需要实际的API服务器和数据库，默认跳过
  // 可以通过设置环境变量 RUN_DB_TESTS=true 来运行
  const itConditional = process.env.RUN_DB_TESTS === 'true' ? it : it.skip
  
  /**
   * 测量API操作的性能
   */
  async function measureDbOperation(
    operation: () => Promise<any>,
    retries = 3
  ): Promise<number> {
    let totalTime = 0
    let successfulCalls = 0
    
    for (let i = 0; i < retries; i++) {
      try {
        const start = performance.now()
        await operation()
        const end = performance.now()
        
        const callTime = end - start
        totalTime += callTime
        successfulCalls++
        
        console.log(`操作 #${i+1}: ${callTime.toFixed(2)}ms`)
      } catch (error) {
        console.error(`操作失败 #${i+1}:`, error)
      }
      
      // 延迟一下再进行下一次操作
      if (i < retries - 1) {
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    }
    
    if (successfulCalls === 0) {
      throw new Error('所有操作都失败了')
    }
    
    // 计算平均响应时间
    return totalTime / successfulCalls
  }
  
  itConditional('大数据集查询性能应在基准范围内', async () => {
    if (skipTests) {
      console.warn('跳过数据库性能测试: 无法连接到API')
      return
    }
    
    // 查询大量订单记录
    const largeQueryTime = await measureDbOperation(async () => {
      return axios.get(`${BASE_URL}/api/v1/orders`, {
        params: { limit: 500, offset: 0 },
        headers: { Authorization: `Bearer ${token}` }
      })
    })
    
    console.log(`大数据集查询平均响应时间: ${largeQueryTime.toFixed(2)}ms, 基准: ${DB_PERFORMANCE_BENCHMARKS.LARGE_QUERY}ms`)
    expect(largeQueryTime).toBeLessThanOrEqual(DB_PERFORMANCE_BENCHMARKS.LARGE_QUERY * 1.2)
  })
  
  itConditional('批量插入操作性能应在基准范围内', async () => {
    if (skipTests) {
      console.warn('跳过数据库性能测试: 无法连接到API')
      return
    }
    
    // 生成批量测试数据
    const generateBatchData = (count: number) => {
      const items = []
      for (let i = 0; i < count; i++) {
        items.push({
          name: `Test Product ${i}`,
          description: `Description for test product ${i}`,
          price: Math.round(Math.random() * 10000) / 100,
          category_id: Math.floor(Math.random() * 5) + 1,
          status: 'active'
        })
      }
      return items
    }
    
    // 批量创建50个产品记录
    const batchSize = 50
    const batchData = generateBatchData(batchSize)
    
    const batchInsertTime = await measureDbOperation(async () => {
      return axios.post(`${BASE_URL}/api/v1/products/batch`, {
        products: batchData
      }, {
        headers: { Authorization: `Bearer ${token}` }
      })
    })
    
    console.log(`批量插入(${batchSize}条)平均响应时间: ${batchInsertTime.toFixed(2)}ms, 基准: ${DB_PERFORMANCE_BENCHMARKS.BATCH_INSERT}ms`)
    expect(batchInsertTime).toBeLessThanOrEqual(DB_PERFORMANCE_BENCHMARKS.BATCH_INSERT * 1.2)
    
    // 清理创建的测试数据
    // 假设API返回了创建的产品IDs
    // 这里可能需要根据实际API实现调整
    try {
      // 获取最近创建的产品
      const response = await axios.get(`${BASE_URL}/api/v1/products`, {
        params: { limit: batchSize, sort: 'created_at:desc' },
        headers: { Authorization: `Bearer ${token}` }
      })
      
      // 删除这些测试产品
      if (response.data && response.data.items && response.data.items.length > 0) {
        const testProductIds = response.data.items
          .filter((p: any) => p.name.startsWith('Test Product'))
          .map((p: any) => p.id)
        
        if (testProductIds.length > 0) {
          await axios.post(`${BASE_URL}/api/v1/products/batch-delete`, {
            ids: testProductIds
          }, {
            headers: { Authorization: `Bearer ${token}` }
          })
          console.log(`已清理${testProductIds.length}个测试产品`)
        }
      }
    } catch (error) {
      console.warn('清理测试数据失败:', error)
    }
  })
  
  itConditional('复杂连接查询性能应在基准范围内', async () => {
    if (skipTests) {
      console.warn('跳过数据库性能测试: 无法连接到API')
      return
    }
    
    // 复杂连接查询 - 获取带有用户、产品和状态信息的订单列表
    const complexQueryTime = await measureDbOperation(async () => {
      return axios.get(`${BASE_URL}/api/v1/orders/detailed`, {
        params: { limit: 50, include: 'user,products,status_history' },
        headers: { Authorization: `Bearer ${token}` }
      })
    })
    
    console.log(`复杂连接查询平均响应时间: ${complexQueryTime.toFixed(2)}ms, 基准: ${DB_PERFORMANCE_BENCHMARKS.COMPLEX_JOIN}ms`)
    expect(complexQueryTime).toBeLessThanOrEqual(DB_PERFORMANCE_BENCHMARKS.COMPLEX_JOIN * 1.2)
  })
  
  itConditional('聚合统计操作性能应在基准范围内', async () => {
    if (skipTests) {
      console.warn('跳过数据库性能测试: 无法连接到API')
      return
    }
    
    // 聚合统计 - 获取按类别、时间段、用户等维度的销售统计
    const aggregationTime = await measureDbOperation(async () => {
      return axios.get(`${BASE_URL}/api/v1/reports/sales/analysis`, {
        params: { 
          group_by: 'category,month,user',
          start_date: '2023-01-01',
          end_date: '2023-12-31'
        },
        headers: { Authorization: `Bearer ${token}` }
      })
    })
    
    console.log(`聚合统计平均响应时间: ${aggregationTime.toFixed(2)}ms, 基准: ${DB_PERFORMANCE_BENCHMARKS.AGGREGATE}ms`)
    expect(aggregationTime).toBeLessThanOrEqual(DB_PERFORMANCE_BENCHMARKS.AGGREGATE * 1.2)
  })
  
  itConditional('数据库事务性能应保持稳定', async () => {
    if (skipTests) {
      console.warn('跳过数据库性能测试: 无法连接到API')
      return
    }
    
    // 需要事务处理的复杂操作 - 创建订单及订单项
    const createOrderData = () => {
      return {
        user_id: 1, // 假设ID为1的用户存在
        items: [
          { product_id: 1, quantity: 2, price: 99.99 },
          { product_id: 2, quantity: 1, price: 149.99 }
        ],
        shipping_address: '测试地址',
        payment_method: 'credit_card',
        notes: '这是一个测试订单'
      }
    }
    
    // 测量创建订单（事务操作）的性能
    const transactionTimes = []
    
    // 执行3次，检查事务性能稳定性
    for (let i = 0; i < 3; i++) {
      const transactionTime = await measureDbOperation(async () => {
        return axios.post(`${BASE_URL}/api/v1/orders`, createOrderData(), {
          headers: { Authorization: `Bearer ${token}` }
        })
      }, 1) // 每次只执行一次操作
      
      transactionTimes.push(transactionTime)
    }
    
    // 计算平均事务时间和变化率
    const avgTransactionTime = transactionTimes.reduce((a, b) => a + b, 0) / transactionTimes.length
    const maxTime = Math.max(...transactionTimes)
    const minTime = Math.min(...transactionTimes)
    const timeVariation = maxTime / minTime
    
    console.log(`平均事务操作时间: ${avgTransactionTime.toFixed(2)}ms`)
    console.log(`时间变化率: ${timeVariation.toFixed(2)}`)
    
    // 验证事务性能的稳定性
    expect(avgTransactionTime).toBeLessThanOrEqual(2000) // 平均响应时间<=2s
    expect(timeVariation).toBeLessThanOrEqual(3) // 时间变化率不超过3倍
  })
}) 