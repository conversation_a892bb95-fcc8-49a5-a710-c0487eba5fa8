/**
 * 负载测试生成器
 * 提供用于生成测试负载的工具函数和类
 */

import axios, { AxiosRequestConfig } from 'axios'
import { performance } from 'perf_hooks'

// 请求结果接口
interface RequestResult {
  url: string
  method: string
  status: number
  duration: number
  timestamp: number
  error?: any
}

// 负载测试配置接口
interface LoadTestConfig {
  baseURL: string
  concurrentUsers: number
  requestsPerUser: number
  delayBetweenRequests?: number
  headers?: Record<string, string>
  timeout?: number
}

// 负载测试结果接口
interface LoadTestResult {
  testName: string
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  totalDuration: number
  avgResponseTime: number
  minResponseTime: number
  maxResponseTime: number
  p50ResponseTime: number
  p90ResponseTime: number
  p95ResponseTime: number
  p99ResponseTime: number
  requestsPerSecond: number
  results: RequestResult[]
}

/**
 * 负载测试生成器类
 */
export class LoadGenerator {
  private config: LoadTestConfig
  private results: RequestResult[] = []
  
  /**
   * 创建负载测试生成器
   * @param config 负载测试配置
   */
  constructor(config: LoadTestConfig) {
    this.config = {
      delayBetweenRequests: 100, // 默认请求间隔100ms
      timeout: 30000, // 默认超时30秒
      ...config
    }
  }
  
  /**
   * 发送单个HTTP请求并记录结果
   * @param url 请求URL
   * @param method 请求方法
   * @param data 请求数据
   */
  private async sendRequest(
    url: string, 
    method: string = 'GET', 
    data?: any
  ): Promise<RequestResult> {
    const fullUrl = `${this.config.baseURL}${url}`
    const requestConfig: AxiosRequestConfig = {
      method,
      url: fullUrl,
      data,
      headers: this.config.headers,
      timeout: this.config.timeout
    }
    
    const startTime = performance.now()
    const timestamp = Date.now()
    let status = 0
    let error = undefined
    
    try {
      const response = await axios(requestConfig)
      status = response.status
    } catch (err) {
      error = err
      if (err.response) {
        status = err.response.status
      }
    }
    
    const endTime = performance.now()
    const duration = endTime - startTime
    
    return {
      url,
      method,
      status,
      duration,
      timestamp,
      error
    }
  }
  
  /**
   * 运行用户模拟测试，每个用户发送多个请求
   * @param testName 测试名称
   * @param requestFn 请求生成函数，返回一个包含URL、方法和数据的对象
   */
  async runUserSimulation(
    testName: string,
    requestFn: (userIndex: number, requestIndex: number) => { url: string, method?: string, data?: any }
  ): Promise<LoadTestResult> {
    console.log(`开始负载测试: ${testName}`)
    console.log(`并发用户数: ${this.config.concurrentUsers}, 每用户请求数: ${this.config.requestsPerUser}`)
    
    const startTime = performance.now()
    this.results = []
    
    // 创建所有用户的所有请求的Promise
    const userPromises = []
    
    for (let userIndex = 0; userIndex < this.config.concurrentUsers; userIndex++) {
      userPromises.push(this.simulateSingleUser(userIndex, requestFn))
    }
    
    // 等待所有用户完成
    await Promise.all(userPromises)
    
    const endTime = performance.now()
    const totalDuration = endTime - startTime
    
    // 计算测试结果
    return this.calculateResults(testName, totalDuration)
  }
  
  /**
   * 模拟单个用户的行为
   */
  private async simulateSingleUser(
    userIndex: number,
    requestFn: (userIndex: number, requestIndex: number) => { url: string, method?: string, data?: any }
  ): Promise<void> {
    for (let requestIndex = 0; requestIndex < this.config.requestsPerUser; requestIndex++) {
      // 获取请求配置
      const requestConfig = requestFn(userIndex, requestIndex)
      
      // 发送请求
      const result = await this.sendRequest(
        requestConfig.url,
        requestConfig.method || 'GET',
        requestConfig.data
      )
      
      // 添加到结果
      this.results.push(result)
      
      // 延迟一段时间再发送下一个请求
      if (requestIndex < this.config.requestsPerUser - 1 && this.config.delayBetweenRequests > 0) {
        await new Promise(resolve => setTimeout(resolve, this.config.delayBetweenRequests))
      }
    }
  }
  
  /**
   * 计算测试结果统计信息
   */
  private calculateResults(testName: string, totalDuration: number): LoadTestResult {
    const totalRequests = this.results.length
    const successfulRequests = this.results.filter(r => r.status >= 200 && r.status < 300).length
    const failedRequests = totalRequests - successfulRequests
    
    // 提取响应时间并排序
    const responseTimes = this.results.map(r => r.duration).sort((a, b) => a - b)
    
    const minResponseTime = responseTimes[0] || 0
    const maxResponseTime = responseTimes[responseTimes.length - 1] || 0
    const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / totalRequests
    
    // 计算分位数
    const p50Index = Math.floor(responseTimes.length * 0.5)
    const p90Index = Math.floor(responseTimes.length * 0.9)
    const p95Index = Math.floor(responseTimes.length * 0.95)
    const p99Index = Math.floor(responseTimes.length * 0.99)
    
    const p50ResponseTime = responseTimes[p50Index] || 0
    const p90ResponseTime = responseTimes[p90Index] || 0
    const p95ResponseTime = responseTimes[p95Index] || 0
    const p99ResponseTime = responseTimes[p99Index] || 0
    
    // 每秒请求数
    const requestsPerSecond = (totalRequests / totalDuration) * 1000
    
    return {
      testName,
      totalRequests,
      successfulRequests,
      failedRequests,
      totalDuration,
      avgResponseTime,
      minResponseTime,
      maxResponseTime,
      p50ResponseTime,
      p90ResponseTime,
      p95ResponseTime,
      p99ResponseTime,
      requestsPerSecond,
      results: [...this.results]
    }
  }
  
  /**
   * 打印测试结果摘要
   */
  static printResultSummary(result: LoadTestResult): void {
    console.log('\n========== 负载测试结果摘要 ==========')
    console.log(`测试名称: ${result.testName}`)
    console.log(`总请求数: ${result.totalRequests}`)
    console.log(`成功请求: ${result.successfulRequests} (${(result.successfulRequests / result.totalRequests * 100).toFixed(2)}%)`)
    console.log(`失败请求: ${result.failedRequests} (${(result.failedRequests / result.totalRequests * 100).toFixed(2)}%)`)
    console.log(`总执行时间: ${(result.totalDuration / 1000).toFixed(2)}秒`)
    console.log(`每秒请求数: ${result.requestsPerSecond.toFixed(2)}`)
    console.log('\n响应时间统计:')
    console.log(`平均响应时间: ${result.avgResponseTime.toFixed(2)}ms`)
    console.log(`最小响应时间: ${result.minResponseTime.toFixed(2)}ms`)
    console.log(`50%响应时间: ${result.p50ResponseTime.toFixed(2)}ms`)
    console.log(`90%响应时间: ${result.p90ResponseTime.toFixed(2)}ms`)
    console.log(`95%响应时间: ${result.p95ResponseTime.toFixed(2)}ms`)
    console.log(`99%响应时间: ${result.p99ResponseTime.toFixed(2)}ms`)
    console.log(`最大响应时间: ${result.maxResponseTime.toFixed(2)}ms`)
    console.log('=======================================\n')
  }
  
  /**
   * 将测试结果导出为CSV格式
   */
  static exportResultsToCSV(result: LoadTestResult): string {
    // 创建CSV表头
    let csv = 'URL,Method,Status,Duration(ms),Timestamp\n'
    
    // 添加每个请求的详细结果
    result.results.forEach(req => {
      csv += `${req.url},${req.method},${req.status},${req.duration},${req.timestamp}\n`
    })
    
    return csv
  }
} 