/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ActionButtons: typeof import('./../components/common/ActionButtons.vue')['default']
    CardSkeleton: typeof import('./../components/common/CardSkeleton.vue')['default']
    DataTable: typeof import('./../components/table/DataTable.vue')['default']
    EnhancedForm: typeof import('./../components/form/EnhancedForm.vue')['default']
    EnhancedFormItem: typeof import('./../components/form/EnhancedFormItem.vue')['default']
    OptimizedChart: typeof import('./../components/chart/OptimizedChart.vue')['default']
    PageHeader: typeof import('./../components/layout/PageHeader.vue')['default']
    ResponsiveContainer: typeof import('./../components/layout/ResponsiveContainer.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchForm: typeof import('./../components/form/SearchForm.vue')['default']
    SkeletonLoader: typeof import('./../components/common/SkeletonLoader.vue')['default']
    StatusTag: typeof import('./../components/common/StatusTag.vue')['default']
    TableSkeleton: typeof import('./../components/common/TableSkeleton.vue')['default']
    VirtualTable: typeof import('./../components/table/VirtualTable.vue')['default']
  }
}
