// 导出所有存储模块
export * from "./user";
export * from "./settings";

// 定义持久化配置类型
interface PersistOptions {
  key: string;
  paths?: string[];
  storage: Storage;
}

interface PersistedStateOptions {
  user: PersistOptions;
  settings: PersistOptions;
}

// 导出持久化配置
export const persistedStateOptions: PersistedStateOptions = {
  // 用户存储持久化配置
  user: {
    key: "user-store",
    paths: ["token", "userId", "username", "name", "role", "permissions"],
    storage: localStorage,
  },

  // 设置存储持久化配置
  settings: {
    key: "settings-store",
    storage: localStorage,
  },
};
