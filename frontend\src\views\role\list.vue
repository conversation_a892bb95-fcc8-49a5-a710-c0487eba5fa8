<template>
  <div class="role-list-container">
    <PageHeader title="角色管理">
      <template #actions>
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>新增
        </el-button>
      </template>
    </PageHeader>

    <SearchForm
      v-model="queryParams"
      :loading="loading"
      @search="handleSearch"
      @reset="resetQuery"
    >
      <!-- 搜索表单字段 -->
      <el-form-item label="角色名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入角色名称"
          clearable
        />
      </el-form-item>

      <el-form-item label="角色编码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入角色编码"
          clearable
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
        >
          <el-option label="启用" value="active" />
          <el-option label="禁用" value="inactive" />
        </el-select>
      </el-form-item>
    </SearchForm>

    <DataTable
      :data="tableData"
      :loading="loading"
      :total="total"
      v-model:current-page="queryParams.page"
      v-model:page-size="queryParams.size"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @selection-change="handleSelectionChange"
      show-selection
    >
      <!-- 表格列 -->
      <el-table-column prop="id" label="ID" width="80" />

      <el-table-column
        prop="name"
        label="角色名称"
        min-width="120"
        show-overflow-tooltip
      />

      <el-table-column
        prop="code"
        label="角色编码"
        min-width="120"
        show-overflow-tooltip
      />

      <el-table-column
        prop="description"
        label="描述"
        min-width="180"
        show-overflow-tooltip
      />

      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
            {{ row.status === "active" ? "启用" : "禁用" }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="创建时间" min-width="160">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
        <el-button type="primary" link @click="handleView(row)">查看</el-button>
        <el-button type="danger" link @click="handleDelete(row)"
          >删除</el-button
        >
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { PageHeader, SearchForm, DataTable } from "@/components";
import { getRoleList, deleteRole } from "@/api/modules/role";
import type { RoleQueryParams, RoleInfo } from "@/api/modules/role";

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return "";
  const d = new Date(date);
  return d.toLocaleString();
};

const router = useRouter();

// 查询参数
const queryParams = reactive<RoleQueryParams>({
  page: 1,
  size: 10,
  name: undefined,
  code: undefined,
  status: undefined,
});

// 表格数据
const tableData = ref<RoleInfo[]>([]);
// 加载状态
const loading = ref(false);
// 总记录数
const total = ref(0);
// 选中的行
const selectedRows = ref<RoleInfo[]>([]);

// 模拟数据
const mockRoleData = [
  {
    id: 1,
    name: "超级管理员",
    code: "SUPER_ADMIN",
    description: "系统超级管理员，拥有所有权限",
    status: "active",
    created_at: "2023-01-01T00:00:00Z",
    updated_at: "2023-01-01T00:00:00Z",
  },
  {
    id: 2,
    name: "普通管理员",
    code: "ADMIN",
    description: "系统管理员，拥有大部分管理权限",
    status: "active",
    created_at: "2023-01-02T00:00:00Z",
    updated_at: "2023-01-02T00:00:00Z",
  },
  {
    id: 3,
    name: "运营人员",
    code: "OPERATOR",
    description: "运营人员，负责日常运营工作",
    status: "active",
    created_at: "2023-01-03T00:00:00Z",
    updated_at: "2023-01-03T00:00:00Z",
  },
  {
    id: 4,
    name: "财务人员",
    code: "FINANCE",
    description: "财务人员，负责财务相关工作",
    status: "active",
    created_at: "2023-01-04T00:00:00Z",
    updated_at: "2023-01-04T00:00:00Z",
  },
  {
    id: 5,
    name: "普通用户",
    code: "USER",
    description: "普通用户，只有基本权限",
    status: "active",
    created_at: "2023-01-05T00:00:00Z",
    updated_at: "2023-01-05T00:00:00Z",
  },
  {
    id: 6,
    name: "访客",
    code: "GUEST",
    description: "访客用户，只有查看权限",
    status: "inactive",
    created_at: "2023-01-06T00:00:00Z",
    updated_at: "2023-01-06T00:00:00Z",
  },
];

// 获取数据列表
const getList = async () => {
  loading.value = true;
  try {
    const result = await getRoleList(queryParams);
    if (result && result.items) {
      tableData.value = result.items;
      total.value = result.total;
    } else if (Array.isArray(result)) {
      tableData.value = result;
      total.value = result.length;
    } else {
      console.log("接口未返回有效数据，使用模拟数据");
      // 使用模拟数据
      const filteredMockData = filterMockData(mockRoleData);
      tableData.value = filteredMockData;
      total.value = filteredMockData.length;
    }
  } catch (error) {
    console.error("获取角色数据失败", error);
    ElMessage.warning("获取接口数据失败，使用模拟数据");

    // 使用模拟数据
    const filteredMockData = filterMockData(mockRoleData);
    tableData.value = filteredMockData;
    total.value = filteredMockData.length;
  } finally {
    loading.value = false;
  }
};

// 过滤模拟数据
const filterMockData = (data: RoleInfo[]) => {
  // 根据查询参数过滤模拟数据
  return data.filter((item) => {
    // 名称过滤
    if (queryParams.name && !item.name.includes(queryParams.name)) {
      return false;
    }
    // 编码过滤
    if (queryParams.code && !item.code.includes(queryParams.code)) {
      return false;
    }
    // 状态过滤
    if (queryParams.status && item.status !== queryParams.status) {
      return false;
    }
    return true;
  });
};

// 搜索
const handleSearch = () => {
  queryParams.page = 1;
  getList();
};

// 重置
const resetQuery = () => {
  // 重置查询参数
  Object.keys(queryParams).forEach((key) => {
    if (key !== "page" && key !== "size") {
      (queryParams as any)[key] = undefined;
    }
  });
  queryParams.page = 1;
  getList();
};

// 选择变化
const handleSelectionChange = (selection: RoleInfo[]) => {
  selectedRows.value = selection;
};

// 每页条数变化
const handleSizeChange = (size: number) => {
  queryParams.size = size;
  getList();
};

// 页码变化
const handleCurrentChange = (page: number) => {
  queryParams.page = page;
  getList();
};

// 新增
const handleCreate = () => {
  router.push("/role/create");
};

// 编辑
const handleEdit = (row: RoleInfo) => {
  router.push(`/role/edit/${row.id}`);
};

// 查看
const handleView = (row: RoleInfo) => {
  router.push(`/role/detail/${row.id}`);
};

// 删除
const handleDelete = (row: RoleInfo) => {
  ElMessageBox.confirm("确认删除该角色吗？", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      try {
        await deleteRole(row.id);
        ElMessage.success("删除成功");
        getList();
      } catch (error) {
        console.error("删除失败", error);

        // 如果接口调用失败，使用模拟数据进行删除
        if (
          tableData.value === mockRoleData ||
          tableData.value.some((item) =>
            mockRoleData.find((mock) => mock.id === item.id)
          )
        ) {
          ElMessage.warning("删除接口调用失败，使用模拟数据进行删除");
          // 从模拟数据中删除
          const index = mockRoleData.findIndex((item) => item.id === row.id);
          if (index !== -1) {
            mockRoleData.splice(index, 1);
            // 重新过滤并显示数据
            const filteredMockData = filterMockData(mockRoleData);
            tableData.value = filteredMockData;
            total.value = filteredMockData.length;
            ElMessage.success("模拟删除成功");
          }
        } else {
          ElMessage.error("删除失败");
        }
      }
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
};

// 页面加载时获取数据
onMounted(() => {
  getList();
});
</script>

<style scoped>
.role-list-container {
  padding: 20px;
}
</style>
