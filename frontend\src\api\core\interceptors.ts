/**
 * 拦截器
 * 提供请求和响应拦截器
 */

import type {
  AxiosInstance,
  AxiosError,
  InternalAxiosRequestConfig,
} from "axios";
import { getToken, refreshToken, isTokenExpiringSoon } from "../helpers/token";
import { handleApiError } from "../helpers/error";
import { getFromCache, saveToCache } from "../helpers/cache";
import { API_CONFIG } from "../config";

/**
 * 拦截器配置
 */
export interface InterceptorsConfig {
  // 是否自动添加认证令牌
  autoAddToken: boolean;
  // 是否自动刷新令牌
  autoRefreshToken: boolean;
  // 是否启用缓存
  enableCache: boolean;
  // 缓存最大有效期（毫秒）
  cacheMaxAge: number;
}

// 默认拦截器配置
export const defaultInterceptorsConfig: InterceptorsConfig = {
  autoAddToken: true,
  autoRefreshToken: true,
  enableCache: API_CONFIG.enableCache,
  cacheMaxAge: API_CONFIG.cacheMaxAge,
};

/**
 * 设置请求拦截器
 * @param instance Axios实例
 * @param config 拦截器配置
 */
export const setupRequestInterceptor = (
  instance: AxiosInstance,
  config: InterceptorsConfig = defaultInterceptorsConfig
): void => {
  // 刷新令牌状态
  let isRefreshing = false;
  // 等待令牌刷新的请求队列
  let refreshSubscribers: Array<(token: string) => void> = [];

  // 添加请求拦截器
  instance.interceptors.request.use(
    async (requestConfig: InternalAxiosRequestConfig) => {
      // 确保headers对象存在
      if (!requestConfig.headers) {
        requestConfig.headers = {};
      }

      // 从缓存获取响应
      if (config.enableCache) {
        const cachedResponse = getFromCache(requestConfig, config.cacheMaxAge);
        if (cachedResponse) {
          // 使用特殊标记表示这是缓存的响应
          requestConfig.headers["X-From-Cache"] = "true";
          // 返回缓存的响应
          return Promise.reject({
            config: requestConfig,
            response: { data: cachedResponse },
            isAxiosError: true,
            isCachedResponse: true,
          });
        }
      }

      // 自动添加认证令牌
      if (config.autoAddToken) {
        const token = getToken();
        if (token) {
          requestConfig.headers.Authorization = `Bearer ${token}`;
          console.debug(
            `添加认证令牌: ${requestConfig.method?.toUpperCase()} ${
              requestConfig.url
            }`
          );
        }
      }

      // 自动刷新令牌
      if (config.autoRefreshToken && isTokenExpiringSoon() && !isRefreshing) {
        try {
          console.info("令牌即将过期，尝试刷新");
          isRefreshing = true;
          const success = await refreshToken();
          isRefreshing = false;

          if (success) {
            // 更新当前请求的令牌
            const newToken = getToken();
            if (newToken) {
              requestConfig.headers.Authorization = `Bearer ${newToken}`;
            }

            // 通知等待的请求
            refreshSubscribers.forEach((callback) => callback(newToken || ""));
            refreshSubscribers = [];
          }
        } catch (error) {
          isRefreshing = false;
          console.error("刷新令牌失败", error);
        }
      }

      return requestConfig;
    },
    (error) => {
      console.error("请求拦截器错误", error);
      return Promise.reject(error);
    }
  );
};

/**
 * 设置响应拦截器
 * @param instance Axios实例
 * @param config 拦截器配置
 */
export const setupResponseInterceptor = (
  instance: AxiosInstance,
  config: InterceptorsConfig = defaultInterceptorsConfig
): void => {
  // 刷新令牌状态
  let isRefreshing = false;
  // 等待令牌刷新的请求队列
  let refreshSubscribers: Array<(token: string) => void> = [];

  // 添加响应拦截器
  instance.interceptors.response.use(
    (response) => {
      // 如果响应头中包含缓存标记，则直接返回
      if (response.config.headers?.["X-From-Cache"] === "true") {
        console.debug(
          `从缓存返回: ${response.config.method?.toUpperCase()} ${
            response.config.url
          }`
        );
        return response;
      }

      // 保存响应到缓存（如果启用缓存）
      if (
        config.enableCache &&
        response.config.method?.toLowerCase() === "get"
      ) {
        saveToCache(
          response.config as InternalAxiosRequestConfig,
          response.data,
          config.cacheMaxAge
        );
      }

      return response;
    },
    async (error: AxiosError) => {
      // 处理缓存响应
      if ((error as any).isCachedResponse) {
        return (error as any).response;
      }

      // 处理令牌刷新
      if (
        error.response?.status === 401 &&
        config.autoRefreshToken &&
        !isRefreshing
      ) {
        try {
          isRefreshing = true;
          const success = await refreshToken();
          isRefreshing = false;

          if (success && error.config) {
            // 更新请求的令牌
            const newToken = getToken();
            if (newToken && error.config.headers) {
              error.config.headers.Authorization = `Bearer ${newToken}`;
            }

            // 通知等待的请求
            refreshSubscribers.forEach((callback) => callback(newToken || ""));
            refreshSubscribers = [];

            // 重试请求
            return instance(error.config);
          }
        } catch (refreshError) {
          isRefreshing = false;
          console.error("刷新令牌失败", refreshError);
        }
      }

      // 简化版本不包含重试逻辑

      // 处理API错误
      handleApiError(error);

      return Promise.reject(error);
    }
  );

  // 简化版本不需要单独的订阅者函数
};

/**
 * 设置拦截器
 * @param instance Axios实例
 * @param config 拦截器配置
 */
export const setupInterceptors = (
  instance: AxiosInstance,
  config: InterceptorsConfig = defaultInterceptorsConfig
): void => {
  setupRequestInterceptor(instance, config);
  setupResponseInterceptor(instance, config);
};

export default {
  defaultInterceptorsConfig,
  setupRequestInterceptor,
  setupResponseInterceptor,
  setupInterceptors,
};
