package com.simbest.cloud.pyjyj.web;

import static java.lang.System.out;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class MessagesController {

    /**
     * http://localhost:9003/pyjyj/admin
     * 
     * @return
     */
    @PreAuthorize("hasAuthority('admin')")
    @GetMapping("/admin")
    public String admin() {
        return "admin";
    }

    /**
     * http://localhost:9003/pyjyj/anonymous/messages1
     * 可访问
     * 
     * @return
     */
    @RequestMapping(value = "/anonymous/messages1", method = { RequestMethod.GET, RequestMethod.POST })
    public String getMessages1() {
        return " hello Message 1";
    }

    /**
     * http://localhost:9003/pyjyj/messages2/anonymous
     * 可访问
     * 
     * @return
     */
    @GetMapping("/messages2/anonymous")
    public String getMessages2() {
        return " hello Message 2";
    }

    /**
     * http://localhost:9003/pyjyj/getCurrentUser
     * 
     * @return
     */
    @GetMapping("/getCurrentUser")
    @ResponseBody
    public Authentication getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return authentication;
    }

    /**
     * http://localhost:9003/pyjyj/messages3
     * 
     * @return
     */
    @PreAuthorize("hasAuthority('system:page:write')")
    @GetMapping("/messages3")
    public String getMessages3() {
        out.println(SecurityContextHolder.getContext().getAuthentication().isAuthenticated());
        SecurityContextHolder.getContext().getAuthentication().getAuthorities()
                .stream()
                .map(GrantedAuthority::getAuthority)
                .forEach(System.out::println);
        return " hello Message 3";
    }

    /**
     * http://localhost:9003/pyjyj/messages4
     * 
     * @return
     */
    @PreAuthorize("hasRole('USER')")
    @GetMapping("/messages4")
    public String getMessages4() {
        return " hello Message 4";
    }

    /**
     * http://localhost:9003/pyjyj/messages5
     * 
     * @return
     */
    // @PreAuthorize("#oauth2.hasScope('profile')") //报错：oauth2.hasScope EL1011E:
    // Method call
    // org.springframework.security.oauth2.client.oidc.userinfo.OidcUserService
    // 会将认证信息的权限authorities制作为OIDC_USER+用户的SCOPE
    @PreAuthorize("hasAuthority('SCOPE_profile')")
    @GetMapping("/messages5")
    public String getMessages5() {
        return " hello Message 5";
    }

    /**
     * http://localhost:9003/pyjyj/messages6
     * 
     * @return
     */
    // org.springframework.security.oauth2.client.oidc.userinfo.OidcUserService
    // 会将认证信息的权限authorities制作为OIDC_USER+用户的SCOPE
    @PreAuthorize("hasAuthority('SCOPE_openid')")
    @GetMapping("/messages6")
    public String getMessages6() {
        out.println(SecurityContextHolder.getContext().getAuthentication().isAuthenticated());
        SecurityContextHolder.getContext().getAuthentication().getAuthorities()
                .stream()
                .map(GrantedAuthority::getAuthority)
                .forEach(System.out::println);
        return " hello Message 6";
    }

    /**
     * http://localhost:9003/pyjyj/messages7
     * 
     * @return
     */
    // org.springframework.security.oauth2.client.oidc.userinfo.OidcUserService
    // 会将认证信息的权限authorities制作为OIDC_USER+用户的SCOPE
    @PreAuthorize("hasAuthority('OIDC_USER')")
    @GetMapping("/messages7")
    public String getMessages7() {
        return " hello Message 7";
    }

}
