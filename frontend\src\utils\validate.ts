/**
 * 判断是否是外部链接
 * @param path - 要检查的路径
 * @returns 如果是外部链接返回 true，否则返回 false
 */
export function isExternal(path: string): boolean {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * 验证用户名是否有效
 * @param str - 要验证的用户名
 * @returns 如果用户名有效返回 true，否则返回 false
 */
export function validUsername(str: string): boolean {
  const valid_map = ['admin', 'editor']
  return valid_map.indexOf(str.trim()) >= 0
}

/**
 * 验证URL是否有效
 * @param url - 要验证的URL
 * @returns 如果URL有效返回 true，否则返回 false
 */
export function validURL(url: string): boolean {
  const reg = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return reg.test(url)
}

/**
 * 验证是否为小写字母
 * @param str - 要验证的字符串
 * @returns 如果全部是小写字母返回 true，否则返回 false
 */
export function validLowerCase(str: string): boolean {
  const reg = /^[a-z]+$/
  return reg.test(str)
}

/**
 * 验证是否为大写字母
 * @param str - 要验证的字符串
 * @returns 如果全部是大写字母返回 true，否则返回 false
 */
export function validUpperCase(str: string): boolean {
  const reg = /^[A-Z]+$/
  return reg.test(str)
}

/**
 * 验证是否为字母
 * @param str - 要验证的字符串
 * @returns 如果全部是字母返回 true，否则返回 false
 */
export function validAlphabets(str: string): boolean {
  const reg = /^[A-Za-z]+$/
  return reg.test(str)
}

/**
 * 验证是否为有效的电子邮件
 * @param email - 要验证的电子邮件地址
 * @returns 如果是有效的电子邮件返回 true，否则返回 false
 */
export function validEmail(email: string): boolean {
  const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return reg.test(email)
}

/**
 * 验证是否为字符串
 * @param str - 要验证的值
 * @returns 如果是字符串返回 true，否则返回 false
 */
export function isString(str: unknown): boolean {
  return typeof str === 'string' || str instanceof String
}

/**
 * 验证是否为数组
 * @param arg - 要验证的值
 * @returns 如果是数组返回 true，否则返回 false
 */
export function isArray(arg: unknown): boolean {
  if (typeof Array.isArray === 'undefined') {
    return Object.prototype.toString.call(arg) === '[object Array]'
  }
  return Array.isArray(arg)
}

/**
 * 验证是否为有效的手机号
 * @param phone - 要验证的手机号
 * @returns 如果是有效的手机号返回 true，否则返回 false
 */
export function validPhone(phone: string): boolean {
  const reg = /^1[3-9]\d{9}$/
  return reg.test(phone)
}