# API设计

## API设计概述

本文档定义了日常审批系统的后端API接口设计，采用RESTful风格，包含认证、用户管理、流程管理和审批处理等模块。所有接口均返回统一格式的JSON数据，支持分页、排序和条件查询。

## 接口通用规范

### 基础路径

所有API的基础路径为 `/api/v1`

### 返回格式

```json
{
  "code": 200,           // 状态码，200成功，非200失败
  "message": "success",  // 提示信息
  "data": {}             // 返回数据，可能是对象、数组或空
}
```

### 分页参数

分页查询统一使用以下参数：

- `pageNum`: 页码，从1开始
- `pageSize`: 每页大小
- `orderBy`: 排序字段
- `orderType`: 排序方式，asc或desc

分页查询返回格式：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [],        // 数据记录
    "total": 100,         // 总记录数
    "pages": 10,          // 总页数
    "pageNum": 1,         // 当前页码
    "pageSize": 10        // 每页大小
  }
}
```

### 认证方式

使用JWT认证，请求需在Header中携带Token：

```
Authorization: Bearer {token}
```

## API接口定义

### 1. 认证与授权接口

#### 1.1 用户登录

- **URL**: `/auth/login`
- **Method**: POST
- **请求参数**:

```json
{
  "username": "admin",
  "password": "123456"
}
```

- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "userInfo": {
      "id": 1,
      "username": "admin",
      "realName": "管理员",
      "deptId": 1,
      "position": "管理员",
      "permissions": ["sys:user:list", "sys:role:list"]
    }
  }
}
```

#### 1.2 刷新令牌

- **URL**: `/auth/refresh-token`
- **Method**: POST
- **请求参数**: 无（从现有Token中提取信息）
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9..."
  }
}
```

#### 1.3 退出登录

- **URL**: `/auth/logout`
- **Method**: POST
- **请求参数**: 无
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 2. 用户管理接口

#### 2.1 获取用户列表

- **URL**: `/sys/users`
- **Method**: GET
- **请求参数**:
  - `pageNum`: 页码
  - `pageSize`: 每页大小
  - `username`: 用户名（可选）
  - `realName`: 真实姓名（可选）
  - `deptId`: 部门ID（可选）
  - `status`: 状态（可选）
- **返回结果**: 分页数据

#### 2.2 获取用户详情

- **URL**: `/sys/users/{id}`
- **Method**: GET
- **请求参数**: 无
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "username": "admin",
    "realName": "管理员",
    "employeeNo": "001",
    "deptId": 1,
    "deptName": "局领导",
    "position": "管理员",
    "email": "<EMAIL>",
    "mobile": "13800138000",
    "gender": 1,
    "status": 1,
    "roles": [
      {
        "id": 1,
        "roleName": "管理员"
      }
    ]
  }
}
```

#### 2.3 创建用户

- **URL**: `/sys/users`
- **Method**: POST
- **请求参数**:

```json
{
  "username": "zhangsan",
  "password": "123456",
  "realName": "张三",
  "employeeNo": "1001",
  "deptId": 5,
  "position": "工作人员",
  "email": "<EMAIL>",
  "mobile": "13812345678",
  "gender": 1,
  "status": 1,
  "roleIds": [2, 3]
}
```

- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 100
  }
}
```

#### 2.4 更新用户

- **URL**: `/sys/users/{id}`
- **Method**: PUT
- **请求参数**: 同创建用户，不含password
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

#### 2.5 删除用户

- **URL**: `/sys/users/{id}`
- **Method**: DELETE
- **请求参数**: 无
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

#### 2.6 重置密码

- **URL**: `/sys/users/{id}/reset-password`
- **Method**: POST
- **请求参数**:

```json
{
  "newPassword": "123456"
}
```

- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3. 部门管理接口

#### 3.1 获取部门树

- **URL**: `/sys/depts/tree`
- **Method**: GET
- **请求参数**:
  - `deptName`: 部门名称（可选）
  - `status`: 状态（可选）
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "parentId": 0,
      "deptName": "局领导",
      "deptCode": "001",
      "orderNum": 1,
      "leaderId": 1,
      "leaderName": "葛磊",
      "status": 1,
      "children": [
        {
          "id": 2,
          "parentId": 1,
          "deptName": "办公室",
          "deptCode": "002",
          "orderNum": 1,
          "leaderId": 13,
          "leaderName": "于党伟",
          "status": 1,
          "children": []
        }
      ]
    }
  ]
}
```

#### 3.2 获取部门列表

- **URL**: `/sys/depts`
- **Method**: GET
- **请求参数**:
  - `deptName`: 部门名称（可选）
  - `status`: 状态（可选）
- **返回结果**: 同部门树，但不包含children字段

#### 3.3 获取部门详情

- **URL**: `/sys/depts/{id}`
- **Method**: GET
- **请求参数**: 无
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 2,
    "parentId": 1,
    "deptName": "办公室",
    "deptCode": "002",
    "orderNum": 1,
    "leaderId": 13,
    "leaderName": "于党伟",
    "status": 1
  }
}
```

#### 3.4 创建部门

- **URL**: `/sys/depts`
- **Method**: POST
- **请求参数**:

```json
{
  "parentId": 1,
  "deptName": "新部门",
  "deptCode": "010",
  "orderNum": 10,
  "leaderId": 15,
  "status": 1
}
```

- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 20
  }
}
```

#### 3.5 更新部门

- **URL**: `/sys/depts/{id}`
- **Method**: PUT
- **请求参数**: 同创建部门
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

#### 3.6 删除部门

- **URL**: `/sys/depts/{id}`
- **Method**: DELETE
- **请求参数**: 无
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 4. 流程管理接口

#### 4.1 获取流程列表

- **URL**: `/workflow/processes`
- **Method**: GET
- **请求参数**:
  - `pageNum`: 页码
  - `pageSize`: 每页大小
  - `processName`: 流程名称（可选）
  - `processType`: 流程类型（可选）
  - `status`: 状态（可选）
- **返回结果**: 分页数据

#### 4.2 获取流程详情

- **URL**: `/workflow/processes/{id}`
- **Method**: GET
- **请求参数**: 无
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "processCode": "TRAVEL_PROCESS",
    "processName": "出差申请流程",
    "processType": "travel",
    "description": "出差申请审批流程",
    "formId": 1,
    "status": 1,
    "version": 1,
    "nodes": [
      {
        "id": 1,
        "nodeCode": "start_node",
        "nodeName": "发起申请",
        "nodeType": "start",
        "approverType": null,
        "approverIds": null,
        "nextNode": "dept_leader_node",
        "orderNum": 1
      },
      // 更多节点...
    ]
  }
}
```

#### 4.3 创建流程

- **URL**: `/workflow/processes`
- **Method**: POST
- **请求参数**:

```json
{
  "processCode": "TRAVEL_PROCESS",
  "processName": "出差申请流程",
  "processType": "travel",
  "description": "出差申请审批流程",
  "formId": 1,
  "status": 1,
  "nodes": [
    {
      "nodeCode": "start_node",
      "nodeName": "发起申请",
      "nodeType": "start",
      "nextNode": "dept_leader_node",
      "orderNum": 1
    },
    // 更多节点...
  ]
}
```

- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 10
  }
}
```

#### 4.4 更新流程

- **URL**: `/workflow/processes/{id}`
- **Method**: PUT
- **请求参数**: 同创建流程
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

#### 4.5 删除流程

- **URL**: `/workflow/processes/{id}`
- **Method**: DELETE
- **请求参数**: 无
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

#### 4.6 发布/停用流程

- **URL**: `/workflow/processes/{id}/status`
- **Method**: PUT
- **请求参数**:

```json
{
  "status": 1  // 1-启用，0-停用
}
```

- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 5. 表单管理接口

#### 5.1 获取表单列表

- **URL**: `/workflow/forms`
- **Method**: GET
- **请求参数**:
  - `pageNum`: 页码
  - `pageSize`: 每页大小
  - `formName`: 表单名称（可选）
  - `formType`: 表单类型（可选）
  - `status`: 状态（可选）
- **返回结果**: 分页数据

#### 5.2 获取表单详情

- **URL**: `/workflow/forms/{id}`
- **Method**: GET
- **请求参数**: 无
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "formCode": "TRAVEL_FORM",
    "formName": "出差申请表单",
    "formType": "travel",
    "formContent": {
      "fields": [
        {
          "name": "title",
          "label": "标题",
          "type": "input",
          "required": true
        },
        // 更多字段...
      ]
    },
    "status": 1,
    "version": 1
  }
}
```

#### 5.3 创建表单

- **URL**: `/workflow/forms`
- **Method**: POST
- **请求参数**:

```json
{
  "formCode": "TRAVEL_FORM",
  "formName": "出差申请表单",
  "formType": "travel",
  "formContent": {
    "fields": [
      {
        "name": "title",
        "label": "标题",
        "type": "input",
        "required": true
      },
      // 更多字段...
    ]
  },
  "status": 1
}
```

- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 10
  }
}
```

#### 5.4 更新表单

- **URL**: `/workflow/forms/{id}`
- **Method**: PUT
- **请求参数**: 同创建表单
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

#### 5.5 删除表单

- **URL**: `/workflow/forms/{id}`
- **Method**: DELETE
- **请求参数**: 无
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 6. 审批实例接口

#### 6.1 创建审批实例

- **URL**: `/workflow/instances`
- **Method**: POST
- **请求参数**:

```json
{
  "processId": 1,
  "formData": {
    "title": "张三出差申请",
    "travelType": "business",
    "startTime": "2023-09-01 09:00:00",
    "endTime": "2023-09-03 18:00:00",
    "days": 3,
    "destination": "北京",
    "travelReason": "参加会议",
    "travelMembers": "李四",
    "transportType": "train",
    "estimateCost": 2000,
    "remarks": "需要提前订票"
  }
}
```

- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "instanceId": 100
  }
}
```

#### 6.2 提交审批

- **URL**: `/workflow/instances/{id}/submit`
- **Method**: POST
- **请求参数**: 无
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

#### 6.3 撤回审批

- **URL**: `/workflow/instances/{id}/cancel`
- **Method**: POST
- **请求参数**: 无
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

#### 6.4 获取审批实例详情

- **URL**: `/workflow/instances/{id}`
- **Method**: GET
- **请求参数**: 无
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 100,
    "instanceCode": "TRAVEL-20230901-001",
    "processId": 1,
    "processName": "出差申请流程",
    "formId": 1,
    "title": "张三出差申请",
    "applicantId": 5,
    "applicantName": "张三",
    "deptId": 5,
    "deptName": "技术部",
    "currentNode": "dept_leader_node",
    "currentNodeName": "部门负责人审批",
    "status": "running",
    "createTime": "2023-09-01 10:30:00",
    "formData": {
      // 表单数据
    },
    "tasks": [
      // 任务列表
    ],
    "history": [
      // 审批历史
    ]
  }
}
```

#### 6.5 获取我的申请列表

- **URL**: `/workflow/instances/my-applications`
- **Method**: GET
- **请求参数**:
  - `pageNum`: 页码
  - `pageSize`: 每页大小
  - `status`: 状态（可选）
  - `processType`: 流程类型（可选）
  - `startTime`: 开始时间（可选）
  - `endTime`: 结束时间（可选）
- **返回结果**: 分页数据

#### 6.6 获取待办任务列表

- **URL**: `/workflow/tasks/my-tasks`
- **Method**: GET
- **请求参数**:
  - `pageNum`: 页码
  - `pageSize`: 每页大小
  - `processType`: 流程类型（可选）
  - `startTime`: 开始时间（可选）
  - `endTime`: 结束时间（可选）
- **返回结果**: 分页数据

#### 6.7 获取已办任务列表

- **URL**: `/workflow/tasks/my-completed-tasks`
- **Method**: GET
- **请求参数**:
  - `pageNum`: 页码
  - `pageSize`: 每页大小
  - `processType`: 流程类型（可选）
  - `startTime`: 开始时间（可选）
  - `endTime`: 结束时间（可选）
- **返回结果**: 分页数据

#### 6.8 审批任务

- **URL**: `/workflow/tasks/{id}/approve`
- **Method**: POST
- **请求参数**:

```json
{
  "action": "approve",  // approve-同意，reject-拒绝，return-退回
  "comment": "同意，金额合理",
  "returnNodeCode": "start_node"  // 当action为return时必填
}
```

- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

#### 6.9 转交任务

- **URL**: `/workflow/tasks/{id}/transfer`
- **Method**: POST
- **请求参数**:

```json
{
  "targetUserId": 10,
  "comment": "请代为审批"
}
```

- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 7. 业务表单接口

以出差申请为例，其他表单类似

#### 7.1 保存出差申请表单

- **URL**: `/business/travel`
- **Method**: POST
- **请求参数**:

```json
{
  "instanceId": 100,  // 关联的流程实例ID
  "title": "张三出差申请",
  "travelType": "business",
  "startTime": "2023-09-01 09:00:00",
  "endTime": "2023-09-03 18:00:00",
  "days": 3,
  "destination": "北京",
  "travelReason": "参加会议",
  "travelMembers": "李四",
  "transportType": "train",
  "estimateCost": 2000,
  "attachments": ["file1.pdf", "file2.jpg"],
  "remarks": "需要提前订票"
}
```

- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 50
  }
}
```

#### 7.2 获取出差申请详情

- **URL**: `/business/travel/{id}`
- **Method**: GET
- **请求参数**: 无
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 50,
    "instanceId": 100,
    "title": "张三出差申请",
    "travelType": "business",
    "startTime": "2023-09-01 09:00:00",
    "endTime": "2023-09-03 18:00:00",
    "days": 3,
    "destination": "北京",
    "travelReason": "参加会议",
    "travelMembers": "李四",
    "transportType": "train",
    "estimateCost": 2000,
    "attachments": ["file1.pdf", "file2.jpg"],
    "remarks": "需要提前订票",
    "creator": 5,
    "creatorName": "张三",
    "createTime": "2023-09-01 10:30:00"
  }
}
```

#### 7.3 更新出差申请表单

- **URL**: `/business/travel/{id}`
- **Method**: PUT
- **请求参数**: 同保存表单
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 8. 文件上传接口

#### 8.1 上传文件

- **URL**: `/common/upload`
- **Method**: POST
- **请求参数**: multipart/form-data 类型
  - `file`: 文件对象
  - `type`: 文件类型（avatar-头像，attachment-附件）
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "fileName": "file1.pdf",
    "fileUrl": "/uploads/2023/09/01/file1.pdf",
    "fileSize": 1024,
    "fileType": "application/pdf"
  }
}
```

### 9. 数据字典接口

#### 9.1 获取字典数据

- **URL**: `/system/dict/data/{dictType}`
- **Method**: GET
- **请求参数**:
  - `dictType`: 字典类型编码
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "dictValue": "business",
      "dictLabel": "公务出差",
      "orderNum": 1
    },
    {
      "dictValue": "training",
      "dictLabel": "培训学习",
      "orderNum": 2
    }
  ]
}
```

### 10. 统计分析接口

#### 10.1 获取流程统计数据

- **URL**: `/statistics/workflow`
- **Method**: GET
- **请求参数**:
  - `startTime`: 开始时间（可选）
  - `endTime`: 结束时间（可选）
  - `processType`: 流程类型（可选）
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalCount": 100,
    "approvedCount": 80,
    "rejectedCount": 15,
    "pendingCount": 5,
    "processTypeStats": [
      {
        "processType": "travel",
        "processName": "出差申请",
        "count": 30
      },
      {
        "processType": "leave",
        "processName": "请假申请",
        "count": 40
      },
      // 其他类型
    ],
    "monthlyStats": [
      {
        "month": "2023-01",
        "count": 10
      },
      {
        "month": "2023-02",
        "count": 15
      },
      // 其他月份
    ]
  }
}
```

#### 10.2 获取个人统计数据

- **URL**: `/statistics/personal`
- **Method**: GET
- **请求参数**:
  - `startTime`: 开始时间（可选）
  - `endTime`: 结束时间（可选）
- **返回结果**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "applicationStats": {
      "totalCount": 20,
      "approvedCount": 15,
      "rejectedCount": 3,
      "pendingCount": 2,
      "processTypeStats": [
        // 按流程类型统计
      ]
    },
    "approvalStats": {
      "totalCount": 30,
      "approvedCount": 25,
      "rejectedCount": 5,
      "averageHandleTime": 2.5,  // 小时
      "processTypeStats": [
        // 按流程类型统计
      ]
    }
  }
}
``` 