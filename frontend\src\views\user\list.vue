<template>
  <div class="user-list-container">
    <PageHeader title="用户管理">
      <template #actions>
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>新增用户
        </el-button>
      </template>
    </PageHeader>

    <SearchForm
      v-model="queryParams"
      :loading="loading"
      @search="handleSearch"
      @reset="resetQuery"
    >
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入用户名"
          clearable
        />
      </el-form-item>

      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
        />
      </el-form-item>

      <el-form-item label="角色" prop="role">
        <el-select
          v-model="queryParams.role"
          placeholder="请选择角色"
          clearable
        >
          <el-option label="管理员" value="admin" />
          <el-option label="普通用户" value="user" />
          <el-option label="访客" value="guest" />
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
        >
          <el-option label="启用" value="active" />
          <el-option label="禁用" value="inactive" />
        </el-select>
      </el-form-item>

      <el-form-item label="创建时间" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
    </SearchForm>

    <DataTable
      :data="tableData"
      :loading="loading"
      :total="total"
      v-model:current-page="queryParams.page"
      v-model:page-size="queryParams.size"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @selection-change="handleSelectionChange"
      showSelection
      showIndex
    >
      <el-table-column prop="username" label="用户名" min-width="120" />

      <el-table-column prop="name" label="姓名" min-width="120" />

      <el-table-column prop="role" label="角色" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getRoleType(row.role)">{{
            getRoleName(row.role)
          }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="email"
        label="邮箱"
        min-width="180"
        show-overflow-tooltip
      />

      <el-table-column prop="phone" label="手机号" min-width="120" />

      <el-table-column prop="status" label="状态" min-width="80">
        <template #default="{ row }">
          <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
            {{ row.status === "active" ? "启用" : "禁用" }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="创建时间" min-width="150">
        <template #default="{ row }">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>

      <template #action="{ row }">
        <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
        <el-button type="primary" link @click="handleView(row)">查看</el-button>
        <el-button
          type="danger"
          link
          @click="handleDelete(row)"
          :disabled="row.role === 'admin'"
          >删除</el-button
        >
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { PageHeader, SearchForm, DataTable } from "@/components";
import { getUserList, deleteUser } from "@/api/modules/user";
import type { UserQueryParams, UserInfo } from "@/api/modules/user";

const router = useRouter();

// 查询参数
const queryParams = reactive<UserQueryParams & { dateRange: string[] }>({
  page: 1,
  size: 10,
  username: "",
  name: "",
  role: "",
  status: "",
  dateRange: [],
});

// 表格数据
const tableData = ref<UserInfo[]>([]);
// 加载状态
const loading = ref(false);
// 总记录数
const total = ref(0);
// 选中的行
const selectedRows = ref<UserInfo[]>([]);

// 获取数据列表
const getList = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const params: UserQueryParams = {
      page: queryParams.page,
      size: queryParams.size,
      username: queryParams.username,
      name: queryParams.name,
      role: queryParams.role,
      status: queryParams.status,
    };

    // 添加日期范围
    if (queryParams.dateRange && queryParams.dateRange.length === 2) {
      params.start_date = queryParams.dateRange[0];
      params.end_date = queryParams.dateRange[1];
    }

    const result = await getUserList(params);
    if (result && result.items) {
      tableData.value = result.items;
      total.value = result.total;
    } else if (Array.isArray(result)) {
      tableData.value = result;
      total.value = result.length;
    } else {
      tableData.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error("获取用户数据失败", error);
    ElMessage.error("获取用户数据失败");
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  queryParams.page = 1;
  getList();
};

// 重置
const resetQuery = () => {
  // 重置查询参数
  queryParams.username = "";
  queryParams.name = "";
  queryParams.role = "";
  queryParams.status = "";
  queryParams.dateRange = [];
  queryParams.page = 1;
  getList();
};

// 选择变化
const handleSelectionChange = (selection: UserInfo[]) => {
  selectedRows.value = selection;
};

// 每页条数变化
const handleSizeChange = (size: number) => {
  queryParams.size = size;
  getList();
};

// 页码变化
const handleCurrentChange = (page: number) => {
  queryParams.page = page;
  getList();
};

// 新增
const handleCreate = () => {
  router.push("/user/create");
};

// 编辑
const handleEdit = (row: UserInfo) => {
  router.push(`/user/edit/${row.id}`);
};

// 查看
const handleView = (row: UserInfo) => {
  router.push(`/user/detail/${row.id}`);
};

// 删除
const handleDelete = (row: UserInfo) => {
  // 管理员不能删除
  if (row.role === "admin") {
    ElMessage.warning("管理员账号不能删除");
    return;
  }

  ElMessageBox.confirm("确认删除该用户吗？此操作不可恢复！", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      try {
        await deleteUser(row.id);
        ElMessage.success("删除成功");
        getList();
      } catch (error) {
        console.error("删除用户失败", error);
        ElMessage.error("删除失败");
      }
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
};

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 获取角色名称
const getRoleName = (role: string) => {
  const roleMap: Record<string, string> = {
    admin: "管理员",
    user: "普通用户",
    guest: "访客",
  };
  return roleMap[role] || role;
};

// 获取角色标签类型
const getRoleType = (role: string) => {
  const typeMap: Record<string, string> = {
    admin: "danger",
    user: "primary",
    guest: "info",
  };
  return typeMap[role] || "info";
};

// 页面加载时获取数据
onMounted(() => {
  getList();
});
</script>

<style scoped>
.user-list-container {
  padding: 20px;
}
</style>
