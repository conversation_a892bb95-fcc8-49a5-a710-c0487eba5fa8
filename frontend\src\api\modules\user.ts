/**
 * 用户API模块
 * 提供用户相关的API请求功能
 */

import { get, post, put, del } from "../core/http";
import type { PaginationParams, PaginatedResponse } from "../types";

// 用户查询参数接口
export interface UserQueryParams extends PaginationParams {
  username?: string;
  name?: string;
  role?: string;
  status?: string;
  start_date?: string;
  end_date?: string;
}

// 用户信息接口
export interface UserInfo {
  id: number;
  username: string;
  name: string;
  email: string;
  phone?: string;
  role: string;
  status: string;
  avatar?: string;
  permissions?: string[];
  created_at: string;
  updated_at: string;
}

// 用户创建数据接口
export interface UserCreateData {
  username: string;
  password: string;
  name: string;
  email: string;
  phone?: string;
  role: string;
  status: string;
  avatar?: string;
  permissions?: string[];
}

// 用户更新数据接口
export interface UserUpdateData {
  username?: string;
  password?: string;
  name?: string;
  email?: string;
  phone?: string;
  role?: string;
  status?: string;
  avatar?: string;
  permissions?: string[];
}

// 用户列表响应接口
export interface UserListResponse extends PaginatedResponse<UserInfo> {}

/**
 * 获取用户列表
 * @param params 查询参数
 * @returns 用户列表数据
 */
export const getUserList = async (params?: UserQueryParams): Promise<UserListResponse> => {
  return await get<UserListResponse>('/users', params);
};

/**
 * 获取用户详情
 * @param id 用户ID
 * @returns 用户详情数据
 */
export const getUserDetail = async (id: number): Promise<UserInfo> => {
  return await get<UserInfo>(`/users/${id}`);
};

/**
 * 创建用户
 * @param data 用户数据
 * @returns 创建的用户数据
 */
export const createUser = async (data: UserCreateData): Promise<UserInfo> => {
  return await post<UserInfo>('/users', data);
};

/**
 * 更新用户
 * @param id 用户ID
 * @param data 更新数据
 * @returns 更新后的用户数据
 */
export const updateUser = async (id: number, data: UserUpdateData): Promise<UserInfo> => {
  return await put<UserInfo>(`/users/${id}`, data);
};

/**
 * 删除用户
 * @param id 用户ID
 * @returns 操作结果
 */
export const deleteUser = async (id: number): Promise<void> => {
  return await del<void>(`/users/${id}`);
};

/**
 * 批量删除用户
 * @param ids 用户ID数组
 * @returns 操作结果
 */
export const batchDeleteUsers = async (ids: number[]): Promise<void> => {
  return await post<void>('/users/batch-delete', { ids });
};

/**
 * 修改用户状态
 * @param id 用户ID
 * @param status 状态值
 * @returns 更新后的用户数据
 */
export const changeUserStatus = async (id: number, status: string): Promise<UserInfo> => {
  return await put<UserInfo>(`/users/${id}/status`, { status });
};

/**
 * 重置用户密码
 * @param id 用户ID
 * @param password 新密码
 * @returns 操作结果
 */
export const resetUserPassword = async (id: number, password: string): Promise<void> => {
  return await put<void>(`/users/${id}/reset-password`, { password });
};
