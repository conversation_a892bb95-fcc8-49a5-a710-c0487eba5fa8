import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { getSalesReport, getAuditReport, getFinanceReport } from '../src/api/reports';
import axios from 'axios';

// 模拟axios
vi.mock('axios');

describe('报表API测试', () => {
  beforeEach(() => {
    // 清理所有模拟
    vi.clearAllMocks();
  });

  describe('getSalesReport', () => {
    it('应正确调用销售报表API', async () => {
      // 设置模拟响应
      const mockResponse = {
        data: {
          data: [
            {
              group_key: '2023-01-01',
              total_amount: 1000,
              total_cost: 700,
              profit: 300,
              order_count: 10,
              product_count: 15
            }
          ],
          report_date: '2023-01-02T00:00:00Z'
        }
      };
      (axios.request as any).mockResolvedValue(mockResponse);

      // 调用API函数
      const params = {
        start_date: '2023-01-01',
        end_date: '2023-01-31',
        business_platform: '巨量千川',
        grouping: 'day'
      };
      const result = await getSalesReport(params);

      // 验证axios是否被正确调用
      expect(axios.request).toHaveBeenCalledTimes(1);
      const requestConfig = (axios.request as any).mock.calls[0][0];
      expect(requestConfig.url).toBe('/api/reports/sales');
      expect(requestConfig.method).toBe('GET');
      
      // 验证请求参数
      expect(requestConfig.params).toEqual(params);
      
      // 验证返回值
      expect(result).toEqual(mockResponse.data);
    });

    it('处理请求错误', async () => {
      // 设置模拟错误
      const mockError = new Error('网络错误');
      (axios.request as any).mockRejectedValue(mockError);

      // 调用API函数并期望抛出错误
      await expect(getSalesReport({
        start_date: '2023-01-01',
        end_date: '2023-01-31'
      })).rejects.toThrow('网络错误');
      
      // 验证axios是否被正确调用
      expect(axios.request).toHaveBeenCalledTimes(1);
    });
  });

  describe('getAuditReport', () => {
    it('应正确调用审计报表API', async () => {
      // 设置模拟响应
      const mockResponse = {
        data: {
          data: [
            {
              group_key: '2023-01-01',
              matched_count: 100,
              unmatched_count: 5,
              match_rate: 95.24
            }
          ],
          report_date: '2023-01-02T00:00:00Z'
        }
      };
      (axios.request as any).mockResolvedValue(mockResponse);

      // 调用API函数
      const params = {
        start_date: '2023-01-01',
        end_date: '2023-01-31',
        business_platform: '巨量千川',
        grouping: 'week'
      };
      const result = await getAuditReport(params);

      // 验证axios是否被正确调用
      expect(axios.request).toHaveBeenCalledTimes(1);
      const requestConfig = (axios.request as any).mock.calls[0][0];
      expect(requestConfig.url).toBe('/api/reports/audit');
      expect(requestConfig.method).toBe('GET');
      
      // 验证请求参数
      expect(requestConfig.params).toEqual(params);
      
      // 验证返回值
      expect(result).toEqual(mockResponse.data);
    });
  });

  describe('getFinanceReport', () => {
    it('应正确调用财务报表API', async () => {
      // 设置模拟响应
      const mockResponse = {
        data: {
          data: [
            {
              group_key: '2023-01',
              total_sales: 10000,
              total_cost: 7000,
              gross_profit: 3000,
              profit_margin: 30
            }
          ],
          report_date: '2023-01-02T00:00:00Z'
        }
      };
      (axios.request as any).mockResolvedValue(mockResponse);

      // 调用API函数
      const params = {
        start_date: '2023-01-01',
        end_date: '2023-01-31',
        business_platform: '巨量千川',
        grouping: 'month'
      };
      const result = await getFinanceReport(params);

      // 验证axios是否被正确调用
      expect(axios.request).toHaveBeenCalledTimes(1);
      const requestConfig = (axios.request as any).mock.calls[0][0];
      expect(requestConfig.url).toBe('/api/reports/finance');
      expect(requestConfig.method).toBe('GET');
      
      // 验证请求参数
      expect(requestConfig.params).toEqual(params);
      
      // 验证返回值
      expect(result).toEqual(mockResponse.data);
    });
  });
}); 