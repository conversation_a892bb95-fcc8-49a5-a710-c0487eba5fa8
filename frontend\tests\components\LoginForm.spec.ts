import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount, flushPromises } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import axios from 'axios'
import LoginView from '@/views/login/index.vue'
import { ElMessage } from 'element-plus'

// 模拟axios
vi.mock('axios')
// 模拟ElementPlus消息
vi.mock('element-plus', async () => {
  const actual = await vi.importActual('element-plus')
  return {
    ...actual,
    ElMessage: {
      success: vi.fn(),
      error: vi.fn()
    }
  }
})

describe('登录表单组件测试', () => {
  // 创建路由实例
  const router = createRouter({
    history: createWebHistory(),
    routes: [{ path: '/', component: { template: '<div>首页</div>' } }]
  })

  // 创建一个测试包装器
  let wrapper

  beforeEach(() => {
    // 重置localStorage模拟
    localStorage.clear()
    
    // 重置axios模拟
    vi.mocked(axios.post).mockReset()
    
    // 挂载组件
    wrapper = mount(LoginView, {
      global: {
        plugins: [router],
        stubs: {
          ElIcon: true,
          'el-button': true
        }
      }
    })
  })

  it('正确渲染登录表单', () => {
    // 验证页面结构
    expect(wrapper.find('.login-container').exists()).toBe(true)
    expect(wrapper.find('.login-card').exists()).toBe(true)
    expect(wrapper.find('.login-header').exists()).toBe(true)
    
    // 验证表单输入元素
    expect(wrapper.find('input[placeholder="用户名"]').exists()).toBe(true)
    expect(wrapper.find('input[placeholder="密码"]').exists()).toBe(true)
    expect(wrapper.find('input[type="checkbox"]').exists()).toBe(true)
    expect(wrapper.find('button.login-button').exists()).toBe(true)
  })

  it('表单验证 - 空用户名和密码', async () => {
    // 直接点击登录按钮，不填写任何信息
    await wrapper.find('button.login-button').trigger('click')
    await flushPromises()
    
    // 验证表单验证是否触发
    expect(wrapper.find('.el-form-item__error').exists()).toBe(true)
    expect(wrapper.findAll('.el-form-item__error').length).toBeGreaterThanOrEqual(2)
    expect(wrapper.findAll('.el-form-item__error')[0].text()).toContain('请输入用户名')
  })

  it('表单验证 - 用户名或密码过短', async () => {
    // 输入短用户名
    await wrapper.find('input[placeholder="用户名"]').setValue('ab')
    
    // 输入短密码
    await wrapper.find('input[placeholder="密码"]').setValue('12345')
    
    // 点击登录按钮
    await wrapper.find('button.login-button').trigger('click')
    await flushPromises()
    
    // 验证表单验证是否触发
    expect(wrapper.find('.el-form-item__error').exists()).toBe(true)
    expect(wrapper.find('.el-form-item__error').text()).toContain('用户名不能少于3个字符')
  })

  it('登录成功后应跳转到首页', async () => {
    // 模拟登录成功的响应
    vi.mocked(axios.post).mockResolvedValue({
      data: {
        access_token: 'test-token',
        user: {
          id: 1,
          name: '测试用户',
          role_id: 'admin'
        }
      }
    })
    
    // 填写表单
    await wrapper.find('input[placeholder="用户名"]').setValue('admin')
    await wrapper.find('input[placeholder="密码"]').setValue('password123')
    await wrapper.find('input[type="checkbox"]').setChecked(true)
    
    // 点击登录按钮
    await wrapper.find('button.login-button').trigger('click')
    await flushPromises()
    
    // 验证请求是否使用正确参数
    expect(axios.post).toHaveBeenCalledWith('/api/auth/login', {
      username: 'admin',
      password: 'password123'
    })
    
    // 验证用户信息是否保存到本地存储
    expect(localStorage.getItem('token')).toBe('test-token')
    expect(localStorage.getItem('userName')).toBe('测试用户')
    expect(localStorage.getItem('userId')).toBe('1')
    expect(localStorage.getItem('userRole')).toBe('admin')
    expect(localStorage.getItem('rememberedUsername')).toBe('admin')
    
    // 验证是否显示成功消息
    expect(ElMessage.success).toHaveBeenCalledWith('登录成功')
    
    // 验证路由是否跳转到首页
    expect(router.currentRoute.value.path).toBe('/')
  })

  it('登录失败应显示错误消息', async () => {
    // 模拟登录失败的响应
    vi.mocked(axios.post).mockRejectedValue({
      response: {
        status: 401,
        data: { detail: '用户名或密码不正确' }
      }
    })
    
    // 填写表单
    await wrapper.find('input[placeholder="用户名"]').setValue('wrong')
    await wrapper.find('input[placeholder="密码"]').setValue('wrong123')
    
    // 点击登录按钮
    await wrapper.find('button.login-button').trigger('click')
    await flushPromises()
    
    // 验证是否显示错误消息
    expect(ElMessage.error).toHaveBeenCalledWith('用户名或密码不正确')
    
    // 验证本地存储是否未保存任何数据
    expect(localStorage.getItem('token')).toBeNull()
  })

  it('记住我功能应该正常工作', async () => {
    // 设置本地存储中已有记住的用户名
    localStorage.setItem('rememberedUsername', 'saveduser')
    
    // 重新挂载组件以触发初始化
    wrapper = mount(LoginView, {
      global: {
        plugins: [router]
      }
    })
    
    // 验证用户名是否自动填充
    const usernameInput = wrapper.find('input[placeholder="用户名"]')
    expect(usernameInput.element.value).toBe('saveduser')
    
    // 验证"记住我"复选框是否被选中
    const checkbox = wrapper.find('input[type="checkbox"]')
    expect(checkbox.element.checked).toBe(true)
  })
}) 