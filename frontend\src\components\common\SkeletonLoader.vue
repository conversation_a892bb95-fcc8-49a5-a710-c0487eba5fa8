<template>
  <div
    class="skeleton-loader"
    :class="[
      `skeleton-${type}`,
      animated ? 'skeleton-animated' : '',
      rounded ? 'skeleton-rounded' : ''
    ]"
    :style="computedStyle"
  >
    <template v-if="type === 'custom'">
      <slot></slot>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps({
  /**
   * 骨架屏类型
   */
  type: {
    type: String,
    default: 'text',
    validator: (value: string) => {
      return ['text', 'circle', 'rect', 'image', 'button', 'card', 'list', 'table', 'custom'].includes(value)
    }
  },
  /**
   * 是否显示动画
   */
  animated: {
    type: Boolean,
    default: true
  },
  /**
   * 是否圆角
   */
  rounded: {
    type: Boolean,
    default: true
  },
  /**
   * 宽度
   */
  width: {
    type: [String, Number],
    default: ''
  },
  /**
   * 高度
   */
  height: {
    type: [String, Number],
    default: ''
  },
  /**
   * 圆角大小
   */
  borderRadius: {
    type: [String, Number],
    default: ''
  },
  /**
   * 背景色
   */
  bgColor: {
    type: String,
    default: ''
  },
  /**
   * 行数（仅对text类型有效）
   */
  rows: {
    type: Number,
    default: 1
  },
  /**
   * 行高（仅对text类型有效）
   */
  rowHeight: {
    type: [String, Number],
    default: '16px'
  },
  /**
   * 行间距（仅对text类型有效）
   */
  rowSpacing: {
    type: [String, Number],
    default: '8px'
  },
  /**
   * 最后一行宽度百分比（仅对text类型有效）
   */
  lastRowWidth: {
    type: [String, Number],
    default: '70%'
  }
})

// 计算样式
const computedStyle = computed(() => {
  const style: Record<string, string> = {}
  
  // 设置宽度
  if (props.width) {
    style.width = typeof props.width === 'number' ? `${props.width}px` : props.width
  } else {
    // 默认宽度
    switch (props.type) {
      case 'text':
        style.width = '100%'
        break
      case 'circle':
        style.width = '40px'
        break
      case 'rect':
        style.width = '100%'
        break
      case 'image':
        style.width = '100%'
        break
      case 'button':
        style.width = '80px'
        break
      case 'card':
        style.width = '100%'
        break
      case 'list':
        style.width = '100%'
        break
      case 'table':
        style.width = '100%'
        break
    }
  }
  
  // 设置高度
  if (props.height) {
    style.height = typeof props.height === 'number' ? `${props.height}px` : props.height
  } else {
    // 默认高度
    switch (props.type) {
      case 'text':
        if (props.rows === 1) {
          style.height = typeof props.rowHeight === 'number' ? `${props.rowHeight}px` : props.rowHeight
        }
        break
      case 'circle':
        style.height = style.width
        break
      case 'rect':
        style.height = '100px'
        break
      case 'image':
        style.height = '200px'
        break
      case 'button':
        style.height = '32px'
        break
      case 'card':
        style.height = '300px'
        break
      case 'list':
        style.height = '200px'
        break
      case 'table':
        style.height = '300px'
        break
    }
  }
  
  // 设置圆角
  if (props.borderRadius) {
    style.borderRadius = typeof props.borderRadius === 'number' ? `${props.borderRadius}px` : props.borderRadius
  }
  
  // 设置背景色
  if (props.bgColor) {
    style.backgroundColor = props.bgColor
  }
  
  // 设置行数相关样式
  if (props.type === 'text' && props.rows > 1) {
    style.height = 'auto'
    style.display = 'flex'
    style.flexDirection = 'column'
    style.gap = typeof props.rowSpacing === 'number' ? `${props.rowSpacing}px` : props.rowSpacing
  }
  
  return style
})
</script>

<style scoped>
.skeleton-loader {
  display: inline-block;
  position: relative;
  overflow: hidden;
  background-color: var(--el-fill-color-light);
  vertical-align: middle;
}

.skeleton-text {
  height: 16px;
}

.skeleton-circle {
  border-radius: 50%;
}

.skeleton-rounded {
  border-radius: 4px;
}

.skeleton-button {
  border-radius: 4px;
}

.skeleton-card {
  border-radius: 8px;
}

.skeleton-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-table {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-animated {
  position: relative;
  z-index: 0;
  overflow: hidden;
}

.skeleton-animated::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: skeleton-loading 1.5s infinite;
  z-index: 1;
}

@keyframes skeleton-loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
</style>
