import { describe, it, expect, vi, beforeEach } from 'vitest';
import { exportToExcel } from '../src/utils/excel';
import * as XLSX from 'xlsx';
import * as FileSaver from 'file-saver';

// 模拟依赖
vi.mock('xlsx', () => ({
  utils: {
    json_to_sheet: vi.fn(),
    book_new: vi.fn(),
    book_append_sheet: vi.fn(),
    decode_range: vi.fn(() => ({
      s: { r: 1, c: 0 },
      e: { r: 2, c: 2 }
    })),
    encode_range: vi.fn(),
    encode_col: vi.fn(),
    sheet_add_json: vi.fn()
  },
  write: vi.fn(),
}));

vi.mock('file-saver', () => ({
  saveAs: vi.fn(),
}));

describe('Excel导出功能测试', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('应调用正确的导出函数', () => {
    // 测试数据
    const data = [
      { name: '张三', age: 30, city: '北京' },
      { name: '李四', age: 25, city: '上海' },
    ];
    
    // 表头
    const headers = [
      { header: '姓名', key: 'name', width: 15 },
      { header: '年龄', key: 'age', width: 10 },
      { header: '城市', key: 'city', width: 15 },
    ];
    
    // 设置模拟
    const mockSheet = { mockSheet: true, '!ref': 'A1:C3' };
    const mockWorkbook = { mockWorkbook: true };
    
    (XLSX.utils.json_to_sheet as any).mockReturnValue(mockSheet);
    (XLSX.utils.book_new as any).mockReturnValue(mockWorkbook);
    (XLSX.write as any).mockReturnValue(new ArrayBuffer(10));
    
    // 调用函数
    exportToExcel(data, headers, '测试文件');
    
    // 验证XLSX工具函数是否被正确调用
    expect(XLSX.utils.json_to_sheet).toHaveBeenCalledTimes(1);
    expect(XLSX.utils.book_new).toHaveBeenCalledTimes(1);
    expect(XLSX.utils.book_append_sheet).toHaveBeenCalledTimes(1);
    expect(XLSX.write).toHaveBeenCalledTimes(1);
    
    // 验证文件保存
    expect(FileSaver.saveAs).toHaveBeenCalledTimes(1);
    
    // 验证文件名
    const saveAsArgs = (FileSaver.saveAs as any).mock.calls[0];
    expect(saveAsArgs[1]).toBe('测试文件.xlsx');
  });

  it('应处理空数据数组', () => {
    // 空数据测试
    const data: any[] = [];
    const headers = [
      { header: '姓名', key: 'name', width: 15 },
      { header: '年龄', key: 'age', width: 10 },
    ];
    
    const mockSheet = { mockSheet: true, '!ref': 'A1:B1' };
    (XLSX.utils.json_to_sheet as any).mockReturnValue(mockSheet);
    
    // 调用函数
    exportToExcel(data, headers, '空数据');
    
    // 验证空数据处理
    expect(XLSX.utils.json_to_sheet).toHaveBeenCalledTimes(1);
  });

  it('应处理自定义文件名', () => {
    // 测试数据
    const data = [{ id: 1, value: 'test' }];
    const headers = [
      { header: 'ID', key: 'id', width: 10 },
      { header: '值', key: 'value', width: 15 },
    ];
    
    const mockSheet = { mockSheet: true, '!ref': 'A1:B2' };
    (XLSX.utils.json_to_sheet as any).mockReturnValue(mockSheet);
    
    // 指定自定义文件名
    exportToExcel(data, headers, 'export');
    
    // 验证文件名
    const saveAsArgs = (FileSaver.saveAs as any).mock.calls[0];
    expect(saveAsArgs[1]).toBe('export.xlsx');
  });
}); 