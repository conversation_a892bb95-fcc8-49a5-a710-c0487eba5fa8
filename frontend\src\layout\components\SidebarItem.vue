<template>
  <div v-if="shouldShowItem">
    <template v-if="onlyOneShowingChild && !onlyOneChild.children">
      <!-- 使用原生点击事件处理，确保在折叠状态下也能正确响应 -->
      <div @click="() => handleMenuClick(resolvePath(onlyOneChild.path))" class="menu-item-wrapper">
        <app-link :to="resolvePath(onlyOneChild.path)" v-if="onlyOneChild.meta">
          <el-menu-item :index="resolvePath(onlyOneChild.path)">
            <el-icon v-if="onlyOneChild.meta && onlyOneChild.meta.icon">
              <component :is="onlyOneChild.meta.icon"></component>
            </el-icon>
            <template #title>
              <span>{{ onlyOneChild.meta.title }}</span>
            </template>
          </el-menu-item>
        </app-link>
      </div>
    </template>

    <el-sub-menu v-else :index="resolvePath(item.path)" popper-append-to-body>
      <template #title>
        <el-icon v-if="item.meta && item.meta.icon">
          <component :is="item.meta.icon"></component>
        </el-icon>
        <span v-if="item.meta && item.meta.title">{{ item.meta.title }}</span>
      </template>

      <template v-if="item.children">
        <div v-for="(child, index) in item.children" :key="child.path || index">
          <sidebar-item
            v-if="!child.meta || !child.meta.hidden"
            :item="child"
            :base-path="resolvePath(child.path)"
          />
        </div>
      </template>
    </el-sub-menu>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { isExternal } from '../../utils/validate'
import AppLink from './Link.vue'
import * as Icons from '@element-plus/icons-vue'
import { useSettingsStore } from '../../store/settings'

const router = useRouter()
const settingsStore = useSettingsStore()

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  basePath: {
    type: String,
    default: ''
  }
})

// 计算是否只有一个可显示的子路由
const showingChildren = computed(() => {
  const children = props.item.children || []
  const isDeveloperMode = settingsStore.settings.developerMode

  return children.filter(item => {
    // 隐藏的菜单项不显示
    if (item.meta && item.meta.hidden) {
      return false
    }

    // 需要开发者模式的菜单项，在非开发者模式下不显示
    if (item.meta && item.meta.requireDeveloperMode && !isDeveloperMode) {
      return false
    }

    // 如果菜单项需要管理员权限，检查用户是否为管理员
    if (item.meta && item.meta.requireAdmin) {
      // 从用户信息中获取角色ID
      const userInfoStr = localStorage.getItem('user_info')
      let isAdmin = false

      if (userInfoStr) {
        try {
          const userInfo = JSON.parse(userInfoStr)
          // 角色ID 1和2分别对应超级管理员和管理员
          isAdmin = userInfo.role_id === 1 || userInfo.role_id === 2
        } catch (e) {
          console.error('解析用户信息失败', e)
        }
      }

      if (!isAdmin) {
        return false
      }
    }

    return true
  })
})

// 是否只有一个要显示的子路由
const onlyOneShowingChild = computed(() => {
  const showingChildrenLen = showingChildren.value.length

  // 当只有一个子路由时，直接显示子路由
  if (showingChildrenLen === 1) {
    return true
  }

  // 没有子路由时，显示父路由本身
  if (showingChildrenLen === 0) {
    return true
  }

  return false
})

// 唯一的子路由(用于直接显示)
const onlyOneChild = computed(() => {
  if (showingChildren.value.length === 1) {
    return showingChildren.value[0]
  }

  if (showingChildren.value.length === 0) {
    return { ...props.item, path: '', noShowingChildren: true }
  }

  return null
})

// 处理菜单点击 - 增强版本
const handleMenuClick = (path) => {
  console.log("菜单项被点击, 路径: ", path)
  if (!path) {
    console.warn("无效的路径")
    return
  }

  // 如果是外部链接，直接在新窗口打开
  if (isExternal(path)) {
    window.open(path, '_blank')
    return
  }

  // 获取当前路由路径
  const currentPath = router.currentRoute.value.path

  // 防止重复导航到当前路径
  if (path === currentPath) {
    console.log('已在当前路径，不需要导航:', path)
    return
  }

  try {
    console.log("准备导航到:", path)
    // 使用 catch 捕获导航错误
    router.push(path).catch(err => {
      console.error('路由导航错误:', err)
    })
  } catch (error) {
    console.error("导航异常:", error)
    // 尝试备用方法导航
    window.location.href = path
  }
}

// 判断是否应该显示菜单项
const shouldShowItem = computed(() => {
  // 如果没有meta或没有标题，不显示
  if (!props.item.meta || !props.item.meta.title) {
    return false
  }

  // 如果菜单项被显式隐藏，不显示
  if (props.item.meta.hidden) {
    return false
  }

  // 如果菜单项需要开发者模式但当前未启用开发者模式，不显示
  if (props.item.meta.requireDeveloperMode && !settingsStore.settings.developerMode) {
    return false
  }

  // 如果菜单项需要管理员权限，检查用户是否为管理员
  if (props.item.meta.requireAdmin) {
    // 从用户信息中获取角色ID
    const userInfoStr = localStorage.getItem('user_info')
    let isAdmin = false

    if (userInfoStr) {
      try {
        const userInfo = JSON.parse(userInfoStr)
        // 角色ID 1和2分别对应超级管理员和管理员
        isAdmin = userInfo.role_id === 1 || userInfo.role_id === 2
      } catch (e) {
        console.error('解析用户信息失败', e)
      }
    }

    if (!isAdmin) {
      return false
    }
  }

  return true
})

// 解析路径
const resolvePath = (routePath: string) => {
  if (!routePath) return props.basePath

  if (isExternal(routePath)) {
    return routePath
  }

  if (isExternal(props.basePath)) {
    return props.basePath
  }

  // 确保路径格式正确 (斜杠处理)
  const hasBasePath = props.basePath && props.basePath !== '/'

  // 如果子路由已经是绝对路径，直接返回
  if (routePath.startsWith('/')) {
    return routePath
  }

  // 路径拼接，确保只有一个斜杠连接
  const basePath = hasBasePath ? (props.basePath.endsWith('/') ? props.basePath.slice(0, -1) : props.basePath) : ''
  const childPath = routePath.startsWith('/') ? routePath : '/' + routePath

  return basePath + childPath
}
</script>

<style scoped>
.el-menu-item {
  background-color: #001f3f;
}
.el-menu-item:hover {
  background-color: #263445;
}

/* 菜单项包装器样式 */
.menu-item-wrapper {
  cursor: pointer;
  display: block;
  width: 100%;
  height: 100%;
}

/* 确保在折叠状态下也能正确响应点击 */
:deep(.el-menu--collapse) .menu-item-wrapper {
  width: 64px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>