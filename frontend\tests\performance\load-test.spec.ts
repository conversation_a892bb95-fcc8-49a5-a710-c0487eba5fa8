import { describe, it, expect } from 'vitest'
import { LoadGenerator } from './load-generator'
import axios from 'axios'

/**
 * 系统负载测试
 * 使用负载生成器模拟多用户并发请求场景
 */
describe('系统负载测试', () => {
  // 测试配置
  const BASE_URL = process.env.API_BASE_URL || 'http://localhost:8000'
  let token: string | null = null
  
  // 跳过标志，如果无法连接到API，将跳过测试
  let skipTests = false
  
  // 测试前获取令牌
  beforeEach(async () => {
    if (skipTests) return
    
    if (!token) {
      try {
        const response = await axios.post(`${BASE_URL}/api/auth/login`, {
          username: 'test_user',
          password: 'password'
        })
        token = response.data.access_token
      } catch (error) {
        console.warn('无法登录API，负载测试将被跳过')
        skipTests = true
      }
    }
  })
  
  // 由于这些测试可能需要实际的API服务器，且会产生大量请求，默认跳过
  // 可以通过设置环境变量 RUN_LOAD_TESTS=true 来运行
  const itConditional = process.env.RUN_LOAD_TESTS === 'true' ? it : it.skip
  
  itConditional('产品列表API应处理中等负载', async () => {
    if (skipTests) {
      console.warn('跳过负载测试: 无法连接到API')
      return
    }
    
    // 创建负载生成器
    const loadGenerator = new LoadGenerator({
      baseURL: BASE_URL,
      concurrentUsers: 10,  // 10个并发用户
      requestsPerUser: 5,   // 每用户5个请求
      delayBetweenRequests: 200, // 每个请求间隔200ms
      headers: token ? { Authorization: `Bearer ${token}` } : undefined
    })
    
    // 运行负载测试
    const result = await loadGenerator.runUserSimulation(
      '产品列表API负载测试',
      (userIndex, requestIndex) => {
        // 每个用户请求不同页码的产品列表
        const page = (userIndex + requestIndex) % 5
        return {
          url: `/api/v1/products?limit=20&offset=${page * 20}`,
          method: 'GET'
        }
      }
    )
    
    // 打印结果摘要
    LoadGenerator.printResultSummary(result)
    
    // 验证性能指标
    expect(result.successfulRequests).toBeGreaterThanOrEqual(result.totalRequests * 0.9) // 至少90%成功
    expect(result.avgResponseTime).toBeLessThanOrEqual(500) // 平均响应时间<=500ms
    expect(result.p95ResponseTime).toBeLessThanOrEqual(1000) // 95%响应时间<=1000ms
  })
  
  itConditional('搜索API应处理中等负载', async () => {
    if (skipTests) {
      console.warn('跳过负载测试: 无法连接到API')
      return
    }
    
    // 搜索关键词列表
    const searchTerms = ['product', 'test', 'sale', 'item', 'new']
    
    // 创建负载生成器
    const loadGenerator = new LoadGenerator({
      baseURL: BASE_URL,
      concurrentUsers: 5, // 5个并发用户
      requestsPerUser: 4, // 每用户4个请求
      delayBetweenRequests: 300, // 每个请求间隔300ms
      headers: token ? { Authorization: `Bearer ${token}` } : undefined
    })
    
    // 运行负载测试
    const result = await loadGenerator.runUserSimulation(
      '搜索API负载测试',
      (userIndex, requestIndex) => {
        // 轮流使用不同的搜索词
        const searchTerm = searchTerms[(userIndex + requestIndex) % searchTerms.length]
        return {
          url: `/api/v1/products/search?query=${searchTerm}`,
          method: 'GET'
        }
      }
    )
    
    // 打印结果摘要
    LoadGenerator.printResultSummary(result)
    
    // 验证性能指标
    expect(result.successfulRequests).toBeGreaterThanOrEqual(result.totalRequests * 0.9)
    expect(result.avgResponseTime).toBeLessThanOrEqual(600) // 平均响应时间<=600ms
    expect(result.p95ResponseTime).toBeLessThanOrEqual(1200) // 95%响应时间<=1200ms
  })
  
  itConditional('报表API应处理低负载', async () => {
    if (skipTests) {
      console.warn('跳过负载测试: 无法连接到API')
      return
    }
    
    // 报表类型
    const reportTypes = [
      '/api/v1/reports/sales/daily',
      '/api/v1/reports/sales/weekly',
      '/api/v1/reports/sales/monthly'
    ]
    
    // 创建负载生成器 - 报表生成通常负载较低，但每个请求耗时较长
    const loadGenerator = new LoadGenerator({
      baseURL: BASE_URL,
      concurrentUsers: 3, // 3个并发用户
      requestsPerUser: 2, // 每用户2个请求
      delayBetweenRequests: 500, // 每个请求间隔500ms
      timeout: 60000, // 更长的超时时间，因为报表生成可能耗时
      headers: token ? { Authorization: `Bearer ${token}` } : undefined
    })
    
    // 运行负载测试
    const result = await loadGenerator.runUserSimulation(
      '报表API负载测试',
      (userIndex, requestIndex) => {
        // 选择报表类型
        const reportUrl = reportTypes[(userIndex + requestIndex) % reportTypes.length]
        return {
          url: reportUrl,
          method: 'GET'
        }
      }
    )
    
    // 打印结果摘要
    LoadGenerator.printResultSummary(result)
    
    // 验证性能指标 - 报表API响应时间较长，但仍应满足基本性能要求
    expect(result.successfulRequests).toBeGreaterThanOrEqual(result.totalRequests * 0.8)
    expect(result.avgResponseTime).toBeLessThanOrEqual(5000) // 平均响应时间<=5s
    expect(result.p95ResponseTime).toBeLessThanOrEqual(10000) // 95%响应时间<=10s
  })
  
  itConditional('登录API应处理高负载', async () => {
    // 创建负载生成器 - 登录API需要承受较高并发
    const loadGenerator = new LoadGenerator({
      baseURL: BASE_URL,
      concurrentUsers: 20, // 20个并发用户
      requestsPerUser: 2, // 每用户2个请求
      delayBetweenRequests: 100 // 每个请求间隔100ms
    })
    
    // 生成测试用户凭据
    const generateTestCredentials = (index: number) => {
      return {
        username: `test_user_${index}`,
        password: 'password123'
      }
    }
    
    // 运行负载测试
    const result = await loadGenerator.runUserSimulation(
      '登录API负载测试',
      (userIndex, requestIndex) => {
        const credentials = generateTestCredentials(userIndex * 100 + requestIndex)
        return {
          url: '/api/auth/login',
          method: 'POST',
          data: credentials
        }
      }
    )
    
    // 打印结果摘要
    LoadGenerator.printResultSummary(result)
    
    // 验证性能指标
    // 注意：由于使用了测试用户，预计大部分请求会返回401未授权，这是预期的行为
    // 这里我们主要关注响应时间而不是成功率
    expect(result.avgResponseTime).toBeLessThanOrEqual(300) // 平均响应时间<=300ms
    expect(result.p95ResponseTime).toBeLessThanOrEqual(600) // 95%响应时间<=600ms
  })
}) 