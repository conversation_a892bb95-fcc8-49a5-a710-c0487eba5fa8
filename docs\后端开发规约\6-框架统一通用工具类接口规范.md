# 6-框架统一通用工具类接口规范

本文档自动生成自项目中的工具类文件，包含每个工具类的主要用途和方法说明。

## 目录大纲

| 序号 | 工具类名称 | 主要用途 |
|------|------------|----------|
| 1 | AnnotationUtils | 注解工具类，用于获取和处理类、方法、字段上的注解信息 |
| 2 | AccessTokenUtils | 访问令牌工具类，用于生成和验证访问令牌 |
| 3 | AuthenticationUtil | 认证工具类，用于处理用户认证相关的操作 |
| 4 | RSAUtils | RSA加密工具类，用于非对称加密和解密 |
| 5 | SysDictValueCacheUtil | 系统字典值缓存工具类，用于管理系统字典缓存 |
| 6 | MessageOperatorUtil | 消息操作工具类，用于处理消息模板和发送 |
| 7 | AppFileSftpUtil | FTP/SFTP文件操作工具类，支持文件上传、下载和删除 |
| 8 | AppFileUtil | 文件处理工具类，支持文件上传、下载、压缩和格式转换 |
| 9 | AppSessionUtil | 会话管理工具类，基于Spring Session with Redis，支持多种会话超时策略 |
| 10 | BigDecimalUtil | 大数运算工具类，用于处理金额计算和格式化 |
| 11 | CustomBeanUtil | 自定义Bean工具类，用于对象属性复制和转换 |
| 12 | DateUtil | 日期时间工具类，提供日期格式化、解析和计算功能 |
| 13 | FileUtils | 文件操作工具类，提供文件读写、复制、删除等基础操作 |
| 14 | BrowserUtil | 浏览器工具类，用于识别和判断浏览器类型 |
| 15 | MvcUtil | MVC工具类，用于处理HTTP请求和响应操作 |
| 16 | ImageUtil | 图片处理工具类，提供图片压缩和格式转换功能 |
| 17 | GetJsonRequestUtil | JSON请求处理工具类，用于从HTTP请求中获取JSON数据 |
| 18 | JacksonUtils | JSON处理工具类，基于Jackson库，提供JSON序列化和反序列化功能 |
| 19 | ListUtil | 列表操作工具类，提供高效的集合差集计算功能 |
| 20 | MapUtil | Map操作工具类，提供Map与对象转换、XML转换、URL参数处理等功能 |
| 21 | NetworkUtil | 网络工具类，用于获取服务器IP地址和MAC地址，支持网卡过滤和地址格式化 |
| 22 | ObjectUtil | 对象工具类，提供对象属性操作、持久化实体操作等工具方法 |
| 23 | ExcelUtil | Excel导入导出工具类，支持Excel文件的读写和数据转换 |
| 24 | PhoneCheckUtil | 手机号码校验工具类，提供中国大陆和香港手机号码格式的校验功能 |
| 25 | QrCodeUtil | 二维码工具类，提供二维码的生成、解析和定制化处理功能 |
| 26 | RedisUtil | Redis客户端工具类，提供Redis数据结构的操作和缓存管理功能 |
| 27 | ReflectionUtils | 反射工具类，提供Java反射相关的操作和工具方法 |
| 28 | LoginUtils | 登录工具类，提供用户登录、登出和日志记录功能 |
| 29 | SecurityUtils | 安全工具类，提供用户认证、鉴权和密码管理等安全相关功能 |
| 30 | HostUtils | 主机工具类，提供服务器主机信息获取和网络连接检测功能 |
| 31 | SocketUtils | Socket工具类，提供服务器心跳检测和端口可用性检查功能 |
| 32 | SpringContextUtils | Spring上下文工具类，提供Spring容器和Bean管理功能 |
| 33 | SSHUtils | SSH连接工具类，提供远程服务器连接和命令执行功能 |
| 34 | UrlEncoderUtils | URL编码工具类，用于判断字符串是否已经过URL编码 |
| 35 | XMLConverUtil | XML数据转换工具类，提供XML与Java对象之间的双向转换功能 |
| 36 | XmlUtils | XML工具类，基于XStream提供XML与Java对象的转换功能 |
| 37 | Zip4JUtil | ZIP压缩包工具类，提供文件压缩和解压缩功能，支持密码保护 |

## 详细接口说明

### 1. AnnotationUtils.java

#### 主要用途

注解工具类，用于扫描和管理带有 EntityCnName 注解的类，提供实体类中文名称的缓存和管理功能。

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| loadEntityCnNames | 在Spring容器初始化完成后执行，加载所有带有EntityCnName注解的类信息 | 无 | 无 |
| findAnnotatedEntityCnNameClasses | 扫描指定包路径下所有带有EntityCnName注解的类 | 无 | 无 |
| putEntityCnNameMetadata | 处理单个带有EntityCnName注解的类，将类名和对应的中文名称存入映射集合中 | - beanDef (BeanDefinition): Spring Bean定义对象 | 无 |
| getEntityCnNameClassifyMap | 获取存储实体类名称和对应的中文名称的映射关系 | 无 | Map<String, String> |

---

### 2. AccessTokenUtils.java

#### 主要用途

验签工具类

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| init | 验签工具类 | 无 | 无 |
| verifyAccessToken | AccessToken 验签 | - authorizationToken (String): *<br> | 无 |

---


### 3. AuthenticationUtil.java

#### 主要用途

用途：Authentication 工具类

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| authenticationIsRequired | 判断单点用户名是否需要验证 | - existingAuth (Authentication): <br>- username (String): <br>- appCode (String): <br> | 无 |
| checkIpIsDevOps | 检查当前请求的IP地址是否是合法运维IP地址 | - clientIp (String): <br> | 无 |

---


### 4. RSAUtils.java

#### 主要用途

创建 RSA 工具类 RSAUtils 和 access_token 签名

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| encryptByPubKey | 无 | - data (String): 加密前的字符串<br>- publicKey (String): 公钥<br> | 加密后的字符串 |
| encryptByPubKey | 无 | - data (byte[]): 待加密数据<br>- pubKey (byte[]): 公钥<br> | * |
| encryptByPriKey | 无 | - text (String): 加密前的字符串<br>- privateKey (String): 私钥<br> | 加密后的字符串 |
| encryptByPriKey | 无 | - data (byte[]): 待加密的数据<br>- priKey (byte[]): 私钥<br> | 加密后的数据 |
| decryptByPubKey | 无 | - data (byte[]): 待解密的数据<br>- pubKey (byte[]): 公钥<br> | 解密后的数据 |
| decryptByPubKey | 无 | - data (String): 解密前的字符串<br>- publicKey (String): 公钥<br> | 解密后的字符串 |
| decryptByPriKey | 无 | - data (byte[]): 待解密的数据<br>- priKey (byte[]): 私钥<br> | * |
| decryptByPriKey | 无 | - secretText (String): 解密前的字符串<br>- privateKey (String): 私钥<br> | 解密后的字符串 |
| sign | 无 | - data (byte[]): 待签名数据<br>- priKey (byte[]): 私钥<br> | 签名 |
| verify | 无 | - data (byte[]): 待校验数据<br>- sign (byte[]): 数字签名<br>- pubKey (byte[]): 公钥<br> | boolean 校验成功返回true，失败返回false |
| generateRsaKey | 无 | - keySize (int): <br> | * |
| publicKeyToBase64 | 无 | - key (RSAPublicKey): *<br> | * |
| privateKeyToBase64 | 无 | - key (RSAPrivateKey): *<br> | * |
| base64ToPublicKey | 无 | - base64 (String): *<br> | * |
| base64ToPrivateKey | 无 | - base64 (String): *<br> | * |
| main | 无 | - args (String[]): <br> | * |

---


### 5. SysDictValueCacheUtil.java

#### 主要用途

用途：数据字典值缓存工具类

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| loadByDictTypeAndNameAndBlocidAndCorpid | 用途：数据字典值缓存工具类 | - dictType (String): <br>- name (String): <br>- blocid (String): <br>- corpid (String): <br> | 无 |
| setByDictTypeAndNameAndBlocidAndCorpid | 用途：数据字典值缓存工具类 | - dictType (String): <br>- name (String): <br>- blocid (String): <br>- corpid (String): <br>- dv (SysDictValue): <br> | 无 |
| loadByDictTypeAndName | 用途：数据字典值缓存工具类 | - dictType (String): <br>- name (String): <br> | 无 |
| setByDictTypeAndName | 用途：数据字典值缓存工具类 | - dictType (String): <br>- name (String): <br>- dv (SysDictValue): <br> | 无 |
| loadByParameters | 用途：数据字典值缓存工具类 | - params (List<String>): <br> | 无 |
| setByParameters | 用途：数据字典值缓存工具类 | - params (List<String>): <br>- listDv (List<SysDictValue>): <br> | 无 |
| expireAllCache | 用途：数据字典值缓存工具类 | 无 | 无 |

---


### 6. MessageOperatorUtil.java

#### 主要用途

消息操作工具类

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| queryWorkFlowMessage | 查询流程类的消息 | - messageCode (Long): 消息模板编码<br> | String |

---


### 7. AppFileSftpUtil.java

#### 主要用途

用途：FTP和SFTP文件操作工具类

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| init | 用途：FTP和SFTP文件操作工具类 | 无 | 无 |
| upload | 将输入流的数据上传到sftp作为文件 | - directory (String): 上传到该目录<br>- fileName (String): sftp端文件名<br>- uploadFile (File): 文件<br> | 无 |
| upload | 将byte[]上传到sftp，作为文件。注意:从String生成byte[]是，要指定字符集。 | - directory (String): 上传到sftp目录<br>- fileName (String): 文件在sftp端的命名<br>- byteArr (byte[]): 要上传的字节数组<br> | 无 |
| upload | 将输入流的数据上传到sftp作为文件 | - directory (String): 上传到该目录<br>- fileName (String): sftp端文件名<br>- inputStream (InputStream): <br> | 无 |
| download2File | 下载文件 | - directory (String): 下载目录<br>- fileName (String): 下载的文件<br>- saveTempFile (File): 存在本地的路径<br> | 无 |
| download2Byte | 下载文件 | - directory (String): 下载目录<br>- fileName (String): 下载的文件<br> | 无 |
| deleteFile | 删除文件，传递文件绝对路径地址 | - fileFullPath (String): <br> | 无 |

---


### 8.AppFileUtil.java

#### 主要用途

<strong>Title : AppFileUtil<strong><br>
<strong>Description : 文件处理工具类<strong><br>
<strong>功能包括：<strong><br>
1. 文件上传与下载处理<br>
2. 文件类型校验与过滤<br>
3. 文件名称处理与路径解析<br>
4. 文件存储（本地磁盘、FTPSFTP、FastDFS等）<br>
5. 图片压缩与处理<br>
6. 临时文件与目录管理<br>
7. URL文件下载与处理<br>

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| init | 初始化方法 初始化文件格式匹配模式、存储位置以及SFTP工具 | 无 | 无 |
| validateUploadFileType | 判断是否允许上传指定的文件类型 通过文件后缀名与配置的允许上传的文件格式正则表达式进行匹配 | - fileName (String): 文件名<br> | 如果文件类型允许上传返回true，否则返回false |
| getFileNameMd5 | 将文件名称进行MD5编码 解决文件保存服务器时的乱码问题，并进行XSS防护 | - fileName (String): 原始文件名<br> | MD5编码后的文件名（保留原扩展名） |
| getFileName | 根据路径提取文件名 支持URL路径和本地文件路径 | - fileUrl (String): 文件URL或本地路径<br> | 文件名（带扩展名） |
| getFileBaseName | 根据路径提取文件基本名称（不含扩展名） 支持URL路径和本地文件路径 | - fileUrl (String): 文件URL或本地路径<br> | 文件基本名称（不含扩展名） |
| getFileSuffix | 根据路径提取文件扩展名 支持URL路径和本地文件路径 | - fileUrl (String): 文件URL或本地路径<br> | 文件扩展名（不含点） |
| getFileNameByContentDisposition | 通过URL连接头信息获取文件名称 从HTTP响应的Content-Disposition头中提取文件名 | - fileUrl (String): 文件URL<br> | 从Content-Disposition头中提取的文件名 |
| getFileType | 返回 text/plain image/jpeg image/png video/mp4  application/vnd.openxmlformats-officedocument.wordprocessingml.document 等文件类型 | - file (File): *<br> | 无 |
| isImage | 返回 text/plain image/jpeg image/png video/mp4  application/vnd.openxmlformats-officedocument.wordprocessingml.document 等文件类型 | - file (File): *<br> | 无 |
| replaceSlash | 返回 text/plain image/jpeg image/png video/mp4  application/vnd.openxmlformats-officedocument.wordprocessingml.document 等文件类型 | - directory (String): <br> | 无 |
| uploadFromUrl | 将文件在远程URL读取后，再上传到指定存储位置 支持多种存储方式：本地磁盘、FastDFS、FTP/SFTP | - fileUrl (String): 远程文件URL地址<br>- directory (String): 存储的相对路径<br> | 上传后的文件信息对象 |
| uploadFile | 上传单个MultipartFile文件到指定目录 | - directory (String): 相对存储目录<br>- multipartFile (MultipartFile): 待上传的MultipartFile对象<br> | 上传成功后的文件信息对象 |
| uploadFile | 上传单个MultipartFile文件到指定目录，可自定义文件名 | - directory (String): 相对存储目录<br>- multipartFile (MultipartFile): 待上传的MultipartFile对象<br>- fileName (String): 自定义的文件名(为null时使用原文件名)<br> | 上传成功后的文件信息对象 |
| uploadFiles | 批量上传多个MultipartFile文件到指定目录 使用系统自动创建的年月日格式目录结构 | - directory (String): 相对存储目录<br>- multipartFiles (Collection<MultipartFile>): 待上传的MultipartFile集合<br> | 上传成功的文件信息对象列表 |
| uploadFiles | 批量上传多个MultipartFile文件到指定目录，可自定义文件名 使用系统自动创建的年月日格式目录结构 | - directory (String): 相对存储目录<br>- multipartFiles (Collection<MultipartFile>): 待上传的MultipartFile集合<br>- fileName (String): 自定义文件名(为空时使用原文件名)<br> | 上传成功的文件信息对象列表 |
| customUploadFiles | 批量上传多个MultipartFile文件到自定义目录 使用指定的目录路径，不进行年月日自动分类 | - customDirectory (String): 自定义的目录路径<br>- multipartFiles (Collection<MultipartFile>): 待上传的MultipartFile集合<br> | 上传成功的文件信息对象列表 |
| customUploadFiles | 批量上传多个MultipartFile文件到自定义目录，可自定义文件名 使用指定的目录路径，不进行年月日自动分类 | - customDirectory (String): 自定义的目录路径<br>- multipartFiles (Collection<MultipartFile>): 待上传的MultipartFile集合<br>- fileName (String): 自定义文件名(为空时使用原文件名)<br> | 上传成功的文件信息对象列表 |
| uploadFromLocalAutoDirectory | 上传本地文件，并保存在系统默认的年月日目录下 根据当前日期自动创建年/月/日目录结构 | - directory (String): 基础目录<br>- localFile (File): 本地文件对象<br>- fileName (String): 自定义文件名(为空时使用原文件名)<br> | 上传成功后的文件信息对象 |
| uploadFromLocalCustomDirectory | 上传本地文件，并保存在指定远程服务器路径 直接使用指定路径，不做年月日目录处理 | - customDirectory (String): 自定义目录路径<br>- localFile (File): 本地文件对象<br>- fileName (String): 自定义文件名(为空时使用原文件名)<br> | 上传成功后的文件信息对象 |
| uploadCompressImage | 图片压缩上传 按指定质量压缩图片并上传到存储位置 | - imageFile (File): 原始图片文件<br>- quality (float): 压缩质量(0-1之间的浮点数)<br>- directory (String): 存储目录<br> | 上传成功后的文件信息对象 |
| uploadCompressImage2 | 图片压缩上传（按宽度等比压缩） 根据指定宽度等比例压缩图片并上传到存储位置 | - imageFile (File): 原始图片文件<br>- compressWidth (String): 压缩后的目标宽度<br>- directory (String): 存储目录<br>- fileSuffix (String): 输出文件后缀名<br> | 上传成功后的文件信息对象 |
| uploadCompressImageFromUrl | 通过URL下载图片并压缩上传 | - imageUrl (String): 图片URL地址<br>- quality (float): 压缩质量(0-1之间的浮点数)<br>- directory (String): 存储目录<br> | 上传成功后的文件信息对象 |
| createAutoUploadDirPath | 创建自动分类的上传目录路径 根据当前日期自动创建年/月/日格式的目录结构 | - directory (String): 基础目录<br> | 包含年月日结构的完整目录路径 |
| createAutoUploadDirFile | 创建自动分类的上传目录文件对象 根据当前日期自动创建年/月/日格式的目录结构，并返回File对象 | - directory (String): 基础目录<br> | 创建好的目录File对象 |
| forceCreateCustomUploadDirectory | 强制创建自定义上传目录 如果目录不存在则创建，存在则跳过 | - directory (String): 自定义目录路径<br> | 创建好的目录File对象 |
| createTempDirectory | 创建临时目录 在系统临时目录下创建指定名称的子目录 | - dir (String): 临时目录名称<br> | 创建好的临时目录File对象 |
| createTempFile | 创建临时文件 在系统临时目录下创建一个带随机名称的临时文件 | 无 | 创建好的临时文件File对象 |
| createTempFileWithName | 创建指定名称的临时文件 在系统临时目录下创建指定名称的临时文件 | - filename (String): 临时文件名称<br> | 创建好的临时文件File对象 |
| createTempFile | 在指定目录创建指定名称的临时文件 | - dir (String): 临时目录<br>- filename (String): 临时文件名称<br> | 创建好的临时文件File对象 |
| createTempFile | 创建指定后缀的临时文件 A convenience method to create a temporary file with specific suffix | - suffix (String): 文件后缀（不含点号）<br> | 创建好的临时文件File对象 |
| getFileFromSystem | 从系统存储位置获取文件 根据文件信息对象获取对应的实际文件 | - sysFile (SysFile): 文件信息对象<br> | 实际存储的文件对象 |
| getFileFromSystem | 从指定存储位置获取文件 根据存储位置和文件信息对象获取对应的实际文件 | - serverUploadLocation (StoreLocation): 服务器存储位置（disk/fastdfs/ftp/sftp）<br>- sysFile (SysFile): 文件信息对象<br> | 实际存储的文件对象 |
| downloadFileFromDir | 从指定目录下载文件 根据目录和文件名获取文件对象 | - directory (String): 文件所在目录<br>- fileName (String): 文件名<br> | 下载的文件对象 |
| downloadByteFromDir | 从指定目录下载文件并返回字节数组 根据目录和文件名获取文件内容 | - directory (String): 文件所在目录<br>- fileName (String): 文件名<br> | 文件内容的字节数组 |
| downloadFileFromUrl | 从URL下载文件 将远程URL指向的文件下载到本地临时目录 | - fileUrl (String): 文件URL地址<br> | 下载后的文件对象 |
| deleteFile | 删除系统文件 根据文件信息对象删除实际存储的文件 | - sysFile (SysFile): 文件信息对象<br> | 删除成功返回true，否则返回false |
| deleteFile | 删除指定存储位置的文件 根据存储位置和文件路径删除文件 | - serverUploadLocation (StoreLocation): 服务器存储位置（disk/fastdfs/ftp/sftp）<br>- filePath (String): 文件完整路径<br> | 删除成功返回true，否则返回false |
| getFileByte | 获取文件字节内容 将文件内容读取为字节数组 | - filePath (String): 文件路径<br> | 文件内容的字节数组 |
| main | Java获取文件类型的5种方法 https://www.163.com/dy/article/GBKUQ1GV0538NIYQ.html | - args (String[]): *<br> | 无 |

---


### 9.AppSessionUtil.java

#### 主要用途

用途：基于Spring Session with Redis的工具类

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| getSession | 获取默认HttpSession, 超时时间基于server.servlet.session.timeout 参考 com.simbest.boot.config.RedisConfiguration | - request (HttpServletRequest): *<br> | HttpSession |
| getShortSession | 返回五分钟内有效的短Session | - request (HttpServletRequest): *<br> | HttpSession |
| getCustomSession | 返回任意时间超时的Session， 超时单位：秒 | - request (HttpServletRequest): *<br>- timeout (int): *<br> | HttpSession |
| getNewSession | 获取默认HttpSession, 超时时间基于server.servlet.session.timeout 参考 com.simbest.boot.config.RedisConfiguration | - request (HttpServletRequest): *<br> | HttpSession |
| getNewShortSession | 返回五分钟内有效的短Session | - request (HttpServletRequest): *<br> | HttpSession |
| getNewCustomSession | 返回任意时间超时的Session， 超时单位：秒 | - request (HttpServletRequest): *<br>- timeout (int): *<br> | HttpSession |

---


### 10. BigDecimalUtil.java

#### 主要用途

BigDecimal 工具类，提供精确的浮点数运算工具方法
包括加减乘除、四舍五入、金额单位转换等操作
解决Java中浮点数计算精度丢失问题

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| add | 提供精确的加法运算 | - v1 (double): 被加数<br>- v2 (double): 加数<br> | 两个参数的和 |
| add | 提供精确的加法运算 | - v1 (double): 被加数<br>- v2 (double): 加数<br>- scale (int): <br> | 两个参数的和 |
| add | 提供精确的加法运算 | - v1 (String): 被加数<br>- v2 (String): 加数<br> | 两个参数的和 |
| add | 提供精确的加法运算 | - v1 (String): 被加数<br>- v2 (String): 加数<br>- scale (int): 保留scale 位小数<br> | 两个参数的和 |
| sub | 提供精确的减法运算 | - v1 (double): 被减数<br>- v2 (double): 减数<br> | 两个参数的差 |
| sub | 提供精确的减法运算 | - v1 (double): 被减数<br>- v2 (double): 减数<br>- scale (int): <br> | 两个参数的差 |
| sub | 提供精确的减法运算。 | - v1 (String): 被减数<br>- v2 (String): 减数<br> | 两个参数的差 |
| sub | 提供精确的减法运算 | - v1 (String): 被减数<br>- v2 (String): 减数<br>- scale (int): 保留scale 位小数<br> | 两个参数的差 |
| mul | 提供精确的乘法运算 | - v1 (double): 被乘数<br>- v2 (double): 乘数<br> | 两个参数的积 |
| mul | 提供精确的乘法运算 | - v1 (double): 被乘数<br>- v2 (double): 乘数<br>- scale (int): <br> | 两个参数的积 |
| mul | 提供精确的乘法运算 | - v1 (String): 被乘数<br>- v2 (String): 乘数<br> | 两个参数的积 |
| mul | 提供精确的乘法运算 | - v1 (String): 被乘数<br>- v2 (String): 乘数<br>- scale (int): 保留scale 位小数<br> | 两个参数的积 |
| div | 提供（相对）精确的除法运算，当发生除不尽的情况时，精确到 小数点以后10位，以后的数字四舍五入 | - v1 (double): 被除数<br>- v2 (double): 除数<br> | 两个参数的商 |
| div | 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指 定精度，以后的数字四舍五入 | - v1 (double): 被除数<br>- v2 (double): 除数<br>- scale (int): 表示表示需要精确到小数点以后几位。<br> | 两个参数的商 |
| div | 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指 定精度，以后的数字四舍五入 | - v1 (String): 被除数<br>- v2 (String): 除数<br>- scale (int): 表示需要精确到小数点以后几位<br> | 两个参数的商 |
| round | 提供精确的小数位四舍五入处理 | - v (double): 需要四舍五入的数字<br>- scale (int): 小数点后保留几位<br> | 四舍五入后的结果 |
| round | 提供精确的小数位四舍五入处理 | - v (String): 需要四舍五入的数字<br>- scale (int): 小数点后保留几位<br> | 四舍五入后的结果 |
| remainder | 取余数 | - v1 (String): 被除数<br>- v2 (String): 除数<br>- scale (int): 小数点后保留几位<br> | 余数 |
| remainder | 取余数  BigDecimal | - v1 (BigDecimal): 被除数<br>- v2 (BigDecimal): 除数<br>- scale (int): 小数点后保留几位<br> | 余数 |
| compare | 比较大小 | - v1 (String): 被比较数<br>- v2 (String): 比较数<br> | 如果v1 大于v2 则 返回true 否则false |
| fenToYuan | 比较大小 | - money (Integer): <br> | 如果v1 大于v2 则 返回true 否则false |
| yuanToFen | 比较大小 | - money (String): <br> | 如果v1 大于v2 则 返回true 否则false |

---


### 11. CustomBeanUtil.java

#### 主要用途

<strong>Description : 基于Spring BeanUtils的自定义工具类<strong><br>
<strong>Create on : 2017年08月23日<strong><br>
<strong>Modify on : 2017年11月08日<strong><br>

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| getNullPropertyNames | <strong>Description : 基于Spring BeanUtils的自定义工具类</strong><br> <strong>Create on : 2017年08月23日</strong><br> <strong>Modify on : 2017年11月08日</strong><br> | - source (Object): <br> | 无 |
| copyPropertiesIgnoreNull | 拷贝source非空的字段至target | - source (Object): <br>- target (Object): <br> | 无 |
| copyPropertiesIgnoreNull | 拷贝source非空的字段至target | - source (Object): <br>- target (Object): <br>- ignoreProperties (String...): <br> | 无 |
| copyPropertiesIgnoreNullAndId | 拷贝source非空的字段至target，并排除target中的持久化ID | - source (Object): <br>- target (Object): <br> | 无 |
| copyTransientProperties | 拷贝source非持久化字段至target | - source (Object): <br>- target (Object): <br> | 无 |

---


### 12. DateUtil.java

#### 主要用途

一些有用的日期时间工具类

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| main | 测试方法，用于演示和测试各种日期工具方法的功能 包括日期格式化、解析、比较、时间区间判断等功能 | - args (String[]): 命令行参数<br> | 无 |
| getNow | 无 | 无 | long 当前时间戳 |
| getCurrent | 无 | 无 | Date 当前日期对象 |
| getCurrentStr | 无 | 无 | String 当前日期字符串 2018-03-14 |
| getCurrentTimestamp | 无 | 无 | String 当前时间戳字符串 2018-03-14 17:13:08 |
| getDateStr | 无 | - pattern (String): 指定类型<br> | String 当前日期字符串 2018-03-14 |
| getTodayTimestamp | 无 | 无 | Date 获取今天的开始时间：比如：2014-06-19 00:00:00 |
| getYesterday | 无 | 无 | String 当前昨日日期字符串 2018-03-14 |
| getYesterday | 无 | - pattern (String): 指定类型<br> | String 当前昨日日期字符串 2018-03-14 |
| getDate | 无 | - date (Date): <br> | String 当前昨日日期字符串 2018-03-14 |
| getDate | 无 | - date (Date): <br>- pattern (String): 指定类型<br> | String 当前昨日日期字符串 2018-03-14 |
| getTime | 无 | - date (Date): <br> | String 当前昨日日期字符串 2018-03-14 |
| getTimestamp | 无 | - date (Date): <br> | String 当前昨日日期字符串 2018-03-14 |
| getTimestamp | 无 | - date (Date): <br>- pattern (String): 指定类型<br> | String 当前昨日日期字符串 2018-03-14 |
| getLongTimestamp | 无 | - date (Date): <br> | String 当前昨日日期字符串 2018-03-14 |
| parseDate | 无 | - source (String): <br> | String 当前昨日日期字符串 2018-03-14 |
| parseTimestamp | 无 | - source (String): <br> | String 当前昨日日期字符串 2018-03-14 |
| parseLongTimestamp | 无 | - time (long): <br> | String 当前昨日日期字符串 2018-03-14 |
| parseCustomDate | 无 | - source (String): <br>- pattern (String): 指定类型<br> | String 当前昨日日期字符串 2018-03-14 |
| compareDate | 无 | - src (Date): <br>- desc (Date): <br> | String 当前昨日日期字符串 2018-03-14 |
| compareTimestamp | 无 | - src (Date): <br>- desc (Date): <br> | String 当前昨日日期字符串 2018-03-14 |
| compareTime | 无 | - src (Date): <br>- desc (Date): <br> | String 当前昨日日期字符串 2018-03-14 |
| belongTimeZone | 比较时间区间(只关心时间，不区分年月日) | - nowTime (Date): 当前时间<br>- startTime (Date): 开始时间<br>- endTime (Date): 结束时间<br> | boolean |
| belongDate | 比较时间区间(完整时间) | - nowTime (Date): 当前时间<br>- startTime (Date): 开始时间<br>- endTime (Date): 结束时间<br> | boolean |
| compareTime | 比较时间区间(完整时间) | - src (String): <br>- desc (String): <br> | boolean |
| getCurrYear | 当前年 | 无 | String 2014 |
| getCurrMonth | 当前月 2014-08 | 无 | String |
| getCurrSimpleMonth | 无 | 无 | String 当前月08 |
| getCurrSimpleDay | 无 | 无 | String 当前日28 |
| getLastMonth | 上一个月 | 无 | String 2014-08 |
| getNextMonth | 下一个月 | 无 | String 2014-08 |
| getCurrMonthFirstDay | 当前月第一天 | 无 | String 2014-08-01 |
| getCurrMonthLastDay | 当前月最后一天 | 无 | String 2014-08-31 |
| getLastMonthFirstDay | 上月第一天 | 无 | String 2014-08-01 |
| getLastMonthLastDay | 上月最后一天 | 无 | String 2014-08-31 |
| getNextMonthFirstDay | 下月第一天 | 无 | String 2014-08-01 |
| getNextMonthLastDay | 下月最后一天 | 无 | String 2014-08-31 |
| getCurrWeekFirstDay | 本周周第一天 | 无 | 无 |
| getCurrWeekLastDay | 本周最后一天 | 无 | 无 |
| getNextWeekFirstDay | 下周第一天 | 无 | 无 |
| getNextWeekLastDay | 下周最后一天 | 无 | 无 |
| addDays | 当前时间向前增加天数 | - days (int): *<br> | Date |
| subDays | 当前时间向后减少天数 | - days (int): *<br> | Date |
| addDays | 指定时间向前增加天数 | - date (Date): *<br>- days (int): *<br> | Date |
| subDays | 指定时间向后减少天数 | - date (Date): *<br>- days (int): *<br> | Date |
| addMinutes | 在当前时间增加时间 | - minutes (int): *<br> | Date |
| subMinutes | 在当前时间向后减少时间 | - minutes (int): *<br> | Date |
| addMinutes | 指定时间增加时间 | - date (Date): *<br>- minutes (int): *<br> | Date |
| subMinutes | 在指定时间向后减少 | - date (Date): *<br>- minutes (int): *<br> | Date |
| addSeconds | 在当前时间增加时间 | - seconds (int): *<br> | Date |
| subSeconds | 在当前时间向后减少时间 | - seconds (int): *<br> | Date |
| addSeconds | 指定时间增加时间 | - date (Date): *<br>- seconds (int): *<br> | Date |
| subSeconds | 在指定时间向后减少 | - date (Date): *<br>- seconds (int): *<br> | Date |
| removeTime | 时间置零 | - date (Date): *<br> | Date |
| removeDate | 日期置零 | - date (Date): *<br> | Date |
| startTimeOfDay | 获取今天的开始时间：比如：2014-06-19 00:00:00 | - date (Date): *<br> | DateTime |
| endTimeOfDay | 获取今天的结束时间：比如：2014-06-19 23:59:59 | - date (Date): *<br> | DateTime |
| overTimeOfToday | 获取现在距离今天结束还有多长时间 | 无 | long |
| daysBetweenDates | 得到两个日期之间相差的天数 | - startDate (Date): 2006-03-01<br>- endDate (Date): 2006-05-01<br> | int 61 |
| minuteBetweenDates | 计算两个时间相差的分钟数 | - startDate (Date): *<br>- endDate (Date): *<br> | long |
| secondBetweenDates | 计算两个时间相差的秒数 | - startDate (Date): *<br>- endDate (Date): *<br> | long |
| timeBetweenDates | 计算两个时间相差的天数、小时数、分数 | - startDate (Date): *<br>- endDate (Date): *<br> | long[] |
| descBetweenDates | 返回两个时间相差的天数、小时数、分数的中文描述 | - startDate (Date): *<br>- endDate (Date): *<br> | String |
| dateToWeek | 获取日期在一个星期的周几 | - date (Date): *<br> | String |
| getJodaDateTime | 获取日期在一个星期的周几 | - object (Object): <br> | String |
| getJodaDateTime | 获取日期在一个星期的周几 | - dateStr (String): <br>- datePattern (String): <br> | String |
| getDateHourPrefix | 返回当前年（2位）+当前天在当前年的第几天（3位）+当前小时（2位） | - date (Date): *<br> | String 1836517 |
| getDatePrefix | 返回当前年（2位）+当前日期（4位） | - date (Date): *<br> | String 181231 |
| date2XmlDate | 将Date类转换为XMLGregorianCalendar | - date (Date): *<br> | XMLGregorianCalendar |
| xmlDate2Date | 将XMLGregorianCalendar转换为Date | - cal (XMLGregorianCalendar): *<br> | Date |
| date2LocalDate | 将Date转换为LocalDate | - date (Date): *<br> | Date |
| date2LocalDateTime | 将Date转换为LocalDateTime | - date (Date): *<br> | Date |
| dateStrFormaterLocalDate | 将Date转换为LocalDateTime | - dateStr (String): <br> | Date |
| dateStrFormaterLocalDateTime | 将Date转换为LocalDateTime | - dateTimeStr (String): <br>- type (String): <br> | Date |
| localDateTime2Date | 将LocalDateTime转换为Date | - localDateTime (LocalDateTime): *<br> | Date |
| localDateTimeToXmlDate | 将LocalDateTime类转换为XMLGregorianCalendar | - localDateTime (LocalDateTime): *<br> | XMLGregorianCalendar |
| xmlDate2LocalDateTime | 将XMLGregorianCalendar转换为LocalDateTime | - cal (XMLGregorianCalendar): *<br> | LocalDateTime |
| getDateStrNumByGroups | 根据传入的2017年9月20日 09:30格式的日期时间字符串，正则获取其中的数字存为字符数组中 Eg：2017年9月20日 09:30  结果为：nums = {"2017","9","20","09","30"} | - dateStr (String): 格式为 2017年9月20日 09:30<br> | String[] |
| localDateIsBefore | 比较第一个日期是否大于第二个日期 | - firstDate (LocalDate): 第一个日期<br>- secondDate (LocalDate): 第二个日期<br> | true-大于;false-不大于 |
| localDateTimeIsBefore | 比较第一个时间是否大于第二个时间 | - firstDateTime (LocalDateTime): 第一个时间<br>- secondDateTime (LocalDateTime): 第二个时间<br> | true-大于;false-不大于 |
| localDateTimeIsAfter | 比较第一个时间是否小于第二个时间 | - firstDateTime (LocalDateTime): 第一个时间<br>- secondDateTime (LocalDateTime): 第二个时间<br> | true-小于;false-大于 |
| localDateIsAfter | 比较第一个日期是否小于第二个日期 | - firstDate (LocalDate): 第一个日期<br>- secondDate (LocalDate): 第二个日期<br> | true-小于;false-大于 |
| localDateTimeIsEqual | 比较两个时间是否相等 | - firstDateTime (LocalDateTime): 第一个时间<br>- secondDateTime (LocalDateTime): 第二个时间<br> | true-相等;false-不相等 |
| localDateIsEqual | 比较两个日期是否相等 | - firstDate (LocalDate): 第一个日期<br>- secondDate (LocalDate): 第二个日期<br> | true-相等;false-不相等 |

---


### 13. FileUtils.java

#### 主要用途
文件操作工具



#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| writeContentToFile | <strong>Title : FileUtils</strong><br> <strong>Description : 文件操作工具</strong><br> <strong>Create on : 2020/7/8</strong><br> <strong>Modify on : 2020/7/8</strong><br> <strong>修改历史:</strong><br> 修改人 修改日期 修改描述<br> -------------------------------------------<br> | - filePath (String): <br>- fileContent (String): <br> | 无 |
| copyFile | 拷贝文件 | - sourceFilePath (String): 源文件路径<br>- targetFilePath (String): 目标文件路径<br> | 无 |
| moveFile | 移动文件 | - sourceFilePath (String): 源文件路径<br>- targetFilePath (String): 目标文件路径<br> | 无 |
| extractFilePathAndName | 从给定的文件路径字符串中提取文件路径和文件名。 | - filePath (String): 完整的文件路径<br> | 包含文件路径和文件名的字符串数组，其中索引0是文件路径，索引1是文件名 |
| main | 从给定的文件路径字符串中提取文件路径和文件名。 | - args (String[]): <br> | 包含文件路径和文件名的字符串数组，其中索引0是文件路径，索引1是文件名 |

---


### 14. BrowserUtil.java

#### 主要用途

用途：浏览器工具类

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| isMSBrowser | 判断是否为微软浏览器 | - request (HttpServletRequest): *<br> | boolean |

---


### 15. MvcUtil.java

#### 主要用途

用途：一些常用的MVC工具类

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| obtainUserFormHeader | 将授权加密的信息返回 根据请求头获取用户名及密码 | - httpHeaders (HttpHeaders): *<br> | IUser |
| writeJsonToResponse | This allows the CAS server to reach to a remote REST endpoint via a POST for verification of credentials. Credentials are passed via an Authorization header whose value is Basic XYZ where XYZ is a Base64 encoded version of the credentials. | - response (HttpServletResponse): <br>- object (Object): <br> | 无 |

---


### 16. ImageUtil.java

#### 主要用途
图片压缩工具类



#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| commpressPicForSize | 根据指定大小和指定精度压缩图片 递归压缩图片直到达到指定大小，同时保持图片质量 | - srcPath (String): 源图片地址<br>- desPath (String): 目标图片地址<br>- desFileSize (long): 指定图片大小，单位kb<br>- accuracy (double): 精度，递归压缩的比率，建议小于0.9<br> | 无 |
| commpressPicForScale | 按照比例进行缩放压缩图片 递归按比例缩小图片直到达到指定大小 | - srcPath (String): 源图片地址<br>- desPath (String): 目标图片地址<br>- desFileSize (long): 指定图片大小，单位kb<br>- accuracy (double): 精度，递归压缩的比率，建议小于0.9<br> | 无 |
| commpressPicForScaleSize | 图片尺寸不变，压缩文件大小 只降低图片质量而不改变尺寸 | - srcPath (String): 源图片地址<br>- desPath (String): 目标图片地址<br>- desFileSize (long): 指定图片大小，单位kb<br>- accuracy (double): 精度，递归压缩的比率，建议小于0.9<br> | 无 |
| checkImage | 检查上传的文件是否为有效的图片文件 通过尝试读取为BufferedImage来判断文件是否为合法图片 | - multipartFile (MultipartFile): 需要检查的上传文件<br> | 如果是有效图片文件返回true，否则返回false |

---


### 17. GetJsonRequestUtil.java

#### 主要用途

用途：从HttpServletRequest获取JSON数据

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| getRequestJsonString | 获取 request 中 json 字符串的内容 | - request (HttpServletRequest): *<br> | : <code>byte[]</code> |

---


### 18. JacksonUtils.java

#### 主要用途

用途：基于Jackson 封装的工具类

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| obj2json | javaBean,list,array 转JSON字符串 | - obj (Object): 对象<br> | JSON串 |
| json2obj | JSON串转对象 | - jsonStr (String): JSON字符串<br>- clazz (Class<T>): 序列化的对象<br> | 返回转换好的对象 |
| json2node | JSON串 转 JsonNode | - jsonStr (String): JSON串<br> | 转换结果 |
| jsonFile2obj | JSON串 转 JsonNode | - jsonFile (File): <br>- clazz (Class<T>): <br> | 转换结果 |
| json2Type | 将Json字符串转换为对象实体列表 | - jsonStr (String): *<br>- listType (TypeReference<T>): *<br> | 无 |
| format | 将Json字符串转换为对象实体列表 | - jsonStr (String): *<br> | 无 |
| escapeString | 将Json字符串转换为对象实体列表 | - value (String): <br> | 无 |
| unescapeString | 将Json字符串转换为对象实体列表 | - value (String): <br> | 无 |

---


### 19. ListUtil.java

#### 主要用途

<strong>Title : ListUtil<strong><br>
<strong>Description : List操作工具类<strong><br>
<strong>Create on : 2019215<strong><br>
<strong>Modify on : 2019215<strong><br>



#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| removeAll | 获取src和target中的差集（src中有但target中没有的元素） 采用高效算法实现，适用于大数据量场景 测试结果：src = 100万条数据，target = 1万条数据，处理用时仅需191ms | - src (List<Object>): 源集合（较大的集合）<br>- target (List<Object>): 目标集合（较小的集合，用于排除）<br> | 返回差集（src中存在但target中不存在的元素） |

---


### 20. MapUtil.java

#### 主要用途

用途：Map工具类

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| mapToObject | Map转对象 | - map (Object>): *<br>- beanClass (Class<?>): *<br> | * |
| xmlToMap | 简单 xml 转换为 Map | - xml (String): *<br> | 无 |
| mapJoin | url 参数串连 | - map (String>): *<br>- valueUrlencode (boolean): *<br> | 无 |
| orderMapByKey | Map key 排序 | - map (String>): *<br> | 无 |
| compare | Map key 排序 | - o1 (String>): <br>- o2 (String>): <br> | 无 |
| sortMapByKey | Map key 排序 | - map (String>): *<br> | 无 |
| getRequestParameters | 获取Http请求的参数 | - request (HttpServletRequest): *<br> | 无 |
| getRequestUrlWithParameters | 获取Http请求的URL及参数 按照腾讯官方要求，将请求参数拼装至URL中，以便计算签名 | - request (HttpServletRequest): *<br> | 无 |
| addRequestUrlWithParameters | 追加Http请求的URL参数 | - request (HttpServletRequest): *<br>- parameters (String>): <br> | 无 |
| transformUpperCase | 追加Http请求的URL参数 | - orgMap (Object>): <br> | 无 |
| transformLowerCase | 追加Http请求的URL参数 | - orgMap (Object>): <br> | 无 |
| formatHumpNameForList | 将List中map的key值命名方式格式化为驼峰 | - list (Object>>): <br> | 无 |
| formatHumpName | 将map的key值命名方式格式化为驼峰 | - map (Object>): <br> | 无 |
| underlineToCamel | 将map的key值命名方式格式化为驼峰 | - param (String): <br> | 无 |
| compare | Map键比较器 用于对Map的键进行字典序排序 在需要对Map按键排序的场景下使用，如签名生成等 | - str1 (String): <br>- str2 (String): <br> | 无 |

---


### 21. NetworkUtil.java

#### 主要用途

<strong>Title : NetworkUtil<strong><br>
<strong>Description : 获取服务器ip地址和mac地址工具类<strong><br>
<strong>Create on : 2022922<strong><br>
<strong>Modify on : 2022922<strong><br>



#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| apply | 过滤器:物理网卡 | - input (NetworkInterface): <br> | 无 |
| getNICs | 根据过滤器{@code filters}指定的条件(AND)返回网卡设备对象 | - ...filters (Predicate<NetworkInterface>): <br> | 符合条件的网卡设备集合 |
| getPhysicalNICs | 返回所有物理网卡 过滤出所有物理网卡（非虚拟）且处于启用状态的网络接口 | 无 | 物理网卡集合 |
| format | 将{@code byte[]} 转换为{@code radix}指定格式的字符串 | - source (byte[]): 源字节数组<br>- separator (String): 分隔符，用于分隔转换后的字符<br>- radix (final Radix): 进制基数，指定转换格式（二进制、十进制或十六进制）<br> | 格式化后的字符串，{ |
| apply | 将{@code byte[]} 转换为{@code radix}指定格式的字符串 | - input (Byte): <br> | 格式化后的字符串，{ |
| formatMac | MAC地址格式(16进制)格式化{@code source}指定的字节数组 | - source (byte[]): MAC地址字节数组<br>- separator (String): MAC地址分隔符，如冒号(:)或连字符(-)<br> | 格式化后的MAC地址字符串 |
| formatIp | 以IP地址格式(点分位)格式化{@code source}指定的字节数组 | - source (byte[]): IP地址字节数组，通常为4字节（IPv4）<br> | 点分十进制格式的IP地址字符串 |
| getMacAddress | 返回指定{@code address}绑定的网卡的物理地址(MAC) | - address (InetAddress): IP地址<br> | 该IP地址绑定的网卡MAC地址的字节数组，如果指定的{ |
| getMacAddress | 获取指定网卡的MAC地址并格式化为字符串 | - nic (NetworkInterface): 网卡对象<br>- separator (String): 格式化分隔符<br> | 表示MAC地址的字符串 |
| getMacAddress | 获取指定IP地址绑定的网卡MAC地址并格式化为字符串 | - address (InetAddress): IP地址<br>- separator (String): 格式化分隔符<br> | 表示MAC地址的字符串 |

---


### 22. ObjectUtil.java

#### 主要用途

对象工具类，提供对象属性操作、持久化实体操作等工具方法
包含对象空值判断、主键字段获取、实体属性管理等功能

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| isEmpty | 判断对象的所有属性是否均为空 | - obj (Object): *<br> | boolean |
| getEntityIdField | 获取持久化对象的主键Id字段 | - obj (Object): *<br> | Field |
| getEntityIdField | 根据类名获取持久化对象的主键Id字段 | - objName (String): 类的全限定名<br> | Field 主键字段，如果找不到或类不存在则返回null |
| getEntityIdVaue | 获取持久化对象的主键Id字段值 | - obj (Object): *<br> | Object |
| setEntityIdVaue | 设置持久化对象的主键值 | - obj (Object): *<br>- value (Object): <br> | 无 |
| getEntityIdPrefixVaue | 获取数据库主键EntityIdPrefix的定义前缀值 | - obj (Object): *<br> | String |
| getEntityPersistentFieldExceptId | 获取被Column标注的持久化字段 | - obj (Object): *<br> | Set<Field> |
| getEntityPersistentFieldValueExceptId | 获取被Column标注的持久化字段值 | - obj (Object): *<br> | Map<String, Object> |
| getIndicateField | 获取指定字段的Field | - obj (Object): *<br>- fieldName (String): *<br> | Field |
| getEntityTransientField | 获取被Transient标注的非持久化字段 | - obj (Object): *<br> | Set<Field> |
| getAllFields | 返回所有字段 | - clazz (Class<?>): <br> | 无 |

---


### 23. ExcelUtil.java

#### 主要用途

用途：Excel导入导出工具类

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| importExcel0307 | 用途：Excel导入导出工具类 | - sheetName (String): <br>- input (InputStream): <br> | 无 |
| importExcel | 导入Excel文件，支持多个sheet页 | - input (InputStream): *<br> | 无 |
| importExcel | 导入指定sheet页 | - sheetName (String): *<br>- input (InputStream): *<br> | 无 |
| importExcel | 从指定行数开始读取数据 | - sheetName (String): 工作区<br>- input (InputStream): 输入流<br>- inputRow (int): 指定行数<br> | 无 |
| importExcel | excel2003 | - sheetName (String): <br>- input (InputStream): <br> | 无 |
| getExcelCellValue | excel2003 | - cell (Cell): <br> | 无 |
| exportExcel | 传递过来的list的元素大于65535时，会自动分离成多个list导出到多个sheet页中 为什么是65535？ excel的sheet页支持65536行 第一行被表头占用，所以都用65536-1=65535来表示sheetSize。 | - list (List<T>): 集合记录<br>- sheetName (String): sheet页名称<br>- output (OutputStream): 输出流<br>- isTagValue (String): 标记是否导出此列<br> | 无 |
| exportExcel | 对list数据源将其里面的数据导入到excel表单 java输出流 | - lists[] (List<T>): <br>- sheetNames[] (String): <br>- output (OutputStream): *            java输出流<br>- isTagValue (String): <br> | 无 |
| getExcelCol | 将EXCEL中A,B,C,D,E列映射成0,1,2,3 | - col (String): <br> | 无 |
| setHSSFPrompt | 设置单元格上提示 要设置的sheet. 标题 内容 开始行 结束行 开始列 结束列 | - sheet (HSSFSheet): *            要设置的sheet.<br>- promptTitle (String): *            标题<br>- promptContent (String): *            内容<br>- firstRow (int): *            开始行<br>- endRow (int): *            结束行<br>- firstCol (int): *            开始列<br>- endCol (int): *            结束列<br> | 设置好的sheet. |
| setHSSFValidation | 设置某些列的值只能输入预制的数据,显示下拉框. 要设置的sheet. 下拉框显示的内容 开始行 结束行 开始列 结束列 | - sheet (HSSFSheet): *            要设置的sheet.<br>- textlist (String[]): *            下拉框显示的内容<br>- firstRow (int): *            开始行<br>- endRow (int): *            结束行<br>- firstCol (int): *            开始列<br>- endCol (int): *            结束列<br> | 设置好的sheet. |

---


### 24. PhoneCheckUtil.java

#### 主要用途

手机号码校验工具类
提供中国大陆和香港手机号码格式的校验功能
支持不同运营商号段的识别和验证

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| main | 测试方法 用于验证各种手机号码格式是否符合规则 包含各种运营商号段的测试用例 | - args (String[]): 命令行参数<br> | 无 |
| isPhoneLegal | 校验手机号码格式是否正确 可同时验证大陆号码或香港号码 | - str (String): 待校验的手机号码字符串<br> | 如果是合法的大陆或香港手机号码则返回true，否则返回false |
| isChinaPhoneLegal | 验证中国大陆手机号码格式 大陆手机号码11位数，匹配格式：前三位固定格式+后8位任意数 支持的前三位格式包括： - 13+任意数 - 15+除4的任意数 - 18+除1和4的任意数 - 17+除9的任意数 - 147 - 166 - 198、199 | - str (String): 待校验的手机号码字符串<br> | 如果是合法的大陆手机号码则返回true，否则返回false |
| isHKPhoneLegal | 验证香港手机号码格式 香港手机号码8位数，以5|6|8|9开头+7位任意数 | - str (String): 待校验的手机号码字符串<br> | 如果是合法的香港手机号码则返回true，否则返回false |

---


### 25. QrCodeUtil.java

#### 主要用途

用途：二维码工具类
提供二维码的生成、解析和定制化处理功能

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| encode | 生成二维码(内嵌LOGO) 二维码文件名随机生成，可能会有重复 | - content (String): 二维码内容<br>- logoPath (String): LOGO图片地址<br>- destPath (String): 存放目标目录<br>- needCompress (boolean): 是否压缩LOGO<br> | 生成的二维码文件名 |
| encode | 生成二维码(内嵌LOGO) 使用指定的文件名保存二维码图片 | - content (String): 二维码内容<br>- logoPath (String): LOGO图片地址<br>- destPath (String): 存放目标目录<br>- fileName (String): 指定的二维码文件名<br>- needCompress (boolean): 是否压缩LOGO<br> | 生成的二维码文件名 |
| mkdirs | 创建目录 当文件夹不存在时，mkdirs会自动创建多层目录 区别于mkdir(如果父目录不存在则会抛出异常) | - destPath (String): 存放目录<br> | 无 |
| encode | 生成二维码(内嵌LOGO) 使用默认参数，不压缩LOGO | - content (String): 二维码内容<br>- logoPath (String): LOGO图片地址<br>- destPath (String): 存储目录<br> | 生成的二维码文件名 |
| encode | 生成二维码 不包含LOGO，可选择是否压缩 | - content (String): 二维码内容<br>- destPath (String): 存储目录<br>- needCompress (boolean): 是否压缩文件<br> | 生成的二维码文件名 |
| encode | 生成二维码 不包含LOGO，不压缩 | - content (String): 二维码内容<br>- destPath (String): 存储目录<br> | 生成的二维码文件名 |
| encode | 生成二维码(内嵌LOGO)并输出到流 适用于web应用中直接输出二维码图片 | - content (String): 二维码内容<br>- logoPath (String): LOGO图片地址<br>- output (OutputStream): 输出流<br>- needCompress (boolean): 是否压缩LOGO<br> | 无 |
| encode | 生成二维码并输出到流 不包含LOGO，适用于web应用 | - content (String): 二维码内容<br>- output (OutputStream): 输出流<br> | 无 |
| decode | 解析二维码图片文件 识别图片中的二维码内容 | - file (File): 二维码图片文件<br> | 解析得到的二维码文本内容，如果解析失败返回null |
| decode | 解析二维码图片 通过图片路径解析二维码内容 | - path (String): 二维码图片地址<br> | 解析得到的二维码文本内容 |
| main | 测试方法 演示二维码的生成与解析功能 支持不含Logo、含Logo且指定或不指定二维码图片名称的情况 | - args (String[]): 命令行参数<br> | 无 |

---


### 26. RedisUtil.java

#### 主要用途

用途：Redis客户端工具类

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| init | 无 | 无 | 无 |
| getRedisTemplate | 无 | 无 | 无 |
| delete | 删除key | - key (String): <br> | 无 |
| deleteGlobal | 删除key | - key (String): <br> | 无 |
| delete | 批量删除key | - keys (Collection<String>): <br> | 无 |
| deleteGlobal | 批量删除key | - keys (Collection<String>): <br> | 无 |
| mulDelete | 模糊删除key | - pattern (String): <br> | 无 |
| mulDeleteGlobal | 模糊删除key(全局) | - pattern (String): <br> | 无 |
| dump | 序列化key | - key (String): *<br> | byte[] |
| hasKey | 是否存在key | - key (String): *<br> | Boolean |
| expire | 设置过期时间 | - key (String): *<br>- timeout (long): *<br>- unit (TimeUnit): *<br> | Boolean |
| expireGlobal | 设置过期时间 | - key (String): *<br>- timeout (long): *<br>- unit (TimeUnit): *<br> | Boolean |
| expireAt | 设置过期时间 | - key (String): *<br>- date (Date): *<br> | Boolean |
| expireGlobalAt | 设置过期时间 | - key (String): *<br>- date (Date): *<br> | Boolean |
| keys | 查找匹配的key | - pattern (String): *<br> | Set<String> |
| globalKeys | 查找匹配的key | - pattern (String): *<br> | Set<String> |
| move | 将当前数据库的 key 移动到给定的数据库 db 当中 | - key (String): *<br>- dbIndex (int): *<br> | Boolean |
| persist | 移除 key 的过期时间，key 将持久保持 | - key (String): *<br> | Boolean |
| getExpire | 返回 key 的剩余的过期时间 | - key (String): *<br>- unit (TimeUnit): *<br> | Long |
| getExpire | 返回 key 的剩余的过期时间 | - key (String): *<br> | Long |
| randomKey | 从当前数据库中随机返回一个 key | 无 | String |
| rename | 修改 key 的名称 | - oldKey (String): *<br>- newKey (String): <br> | 无 |
| renameIfAbsent | 仅当 newkey 不存在时，将 oldKey 改名为 newkey | - oldKey (String): *<br>- newKey (String): *<br> | Boolean |
| type | 返回 key 所储存的值的类型 | - key (String): *<br> | DataType |
| globalType | 返回 key 所储存的值的类型 | - key (String): *<br> | DataType |
| set | 设置指定 key 的值 | - key (String): *<br>- value (String): <br> | 无 |
| setGlobal | 设置指定 key 的值 | - key (String): *<br>- value (String): <br> | 无 |
| set | 设置指定 key 的值 | - key (String): *<br>- value (String): *<br>- seconds (int): <br> | 无 |
| setGlobal | 设置指定 key 的值 | - key (String): *<br>- value (String): *<br>- seconds (int): <br> | 无 |
| get | 获取指定 key 的值 | - key (String): *<br> | String |
| getGlobal | 获取指定 key 的值 | - key (String): *<br> | String |
| setBean | 设置指定 key 的对象 | - key (String): *<br>- obj (T): *<br> | 无 |
| setBeanGlobal | 设置指定 key 的对象 | - key (String): *<br>- obj (T): *<br> | 无 |
| setBean | 保存复杂类型数据到缓存（并设置失效时间） | - key (String): *<br>- obj (T): *<br>- seconds (int): *<br> | 无 |
| setBeanGlobal | 保存复杂类型数据到缓存（并设置失效时间） | - key (String): *<br>- obj (T): *<br>- seconds (int): *<br> | 无 |
| getBean | 取得复杂类型数据 | - key (String): *<br>- clazz (Class<T>): *<br> | 无 |
| getBean | 取得复杂类型数据 | - key (String): *<br>- listType (TypeReference<T>): <br> | 无 |
| getBeanGlobal | 取得复杂类型数据 | - key (String): *<br>- clazz (Class<T>): *<br> | 无 |
| getBeanGlobal | 取得复杂类型数据 | - key (String): *<br>- listType (TypeReference<T>): <br> | 无 |
| getRange | 返回 key 中字符串值的子字符 | - key (String): *<br>- start (long): *<br>- end (long): *<br> | String |
| getAndSet | 将给定 key 的值设为 value ，并返回 key 的旧值(old value) | - key (String): *<br>- value (String): *<br> | String |
| getBit | 对 key 所储存的字符串值，获取指定偏移量上的位(bit) | - key (String): *<br>- offset (long): *<br> | Boolean |
| multiGet | 批量获取 | - keys (Collection<String>): *<br> | List<String> |
| setBit | 设置ASCII码, 字符串'a'的ASCII码是97, 转为二进制是'01100001', 此方法是将二进制第offset位值变为value 位置 值,true为1, false为0 | - key (String): *<br>- offset (long): *            位置<br>- value (boolean): *            值,true为1, false为0<br> | boolean |
| setEx | 将值 value 关联到 key ，并将 key 的过期时间设为 timeout 过期时间 时间单位, 天:TimeUnit.DAYS 小时:TimeUnit.HOURS 分钟:TimeUnit.MINUTES 秒:TimeUnit.SECONDS 毫秒:TimeUnit.MILLISECONDS | - key (String): *<br>- value (String): *<br>- timeout (long): *            过期时间<br>- unit (TimeUnit): *            时间单位, 天:TimeUnit.DAYS 小时:TimeUnit.HOURS 分钟:TimeUnit.MINUTES<br> | 无 |
| setIfAbsent | 只有在 key 不存在时设置 key 的值 | - key (String): *<br>- value (String): *<br> | boolean 之前已经存在返回false,不存在返回true |
| setRange | 用 value 参数覆写给定 key 所储存的字符串值，从偏移量 offset 开始 从指定位置开始覆写 | - key (String): *<br>- value (String): *<br>- offset (long): *            从指定位置开始覆写<br> | 无 |
| size | 获取字符串的长度 | - key (String): *<br> | Long |
| multiSet | 批量添加 | - maps (String>): <br> | 无 |
| multiSetIfAbsent | 同时设置一个或多个 key-value 对，当且仅当所有给定 key 都不存在 | - maps (String>): *<br> | boolean 之前已经存在返回false,不存在返回true |
| incrBy | 增加(自增长) | - key (String): *<br> | Long |
| incrBy | 增加(自增长) | - key (String): *<br>- increment (long): *<br> | Long |
| decrBy | 减少(自减少) | - key (String): *<br> | Long |
| decrBy | 减少(自减少) | - key (String): *<br>- increment (long): *<br> | 无 |
| append | 追加到末尾 | - key (String): *<br>- value (String): *<br> | Integer |
| hGet | 获取存储在哈希表中指定字段的值 | - key (String): <br>- hkey (Object): <br> | 无 |
| hGetAll | 获取所有给定字段的值 | - key (String): <br> | 无 |
| hMultiGet | 获取所有给定字段的值 | - key (String): <br>- hkeys (Collection<Object>): <br> | 无 |
| hPut | 获取所有给定字段的值 | - key (String): <br>- hkey (Object): <br>- value (Object): <br> | 无 |
| hPutAll | 获取所有给定字段的值 | - key (String): <br>- maps (Object>): <br> | 无 |
| hPutIfAbsent | 仅当hashKey不存在时才设置 | - key (String): <br>- hkey (Object): <br>- value (Object): <br> | 无 |
| hDelete | 删除一个或多个哈希表字段 | - key (String): <br>- hkeys (Object...): <br> | 无 |
| hExists | 查看哈希表 key 中，指定的字段是否存在 | - key (String): <br>- hkey (Object): <br> | 无 |
| hIncrBy | 为哈希表 key 中的指定字段的整数值加上增量 increment | - key (String): <br>- hkey (Object): <br>- increment (long): <br> | 无 |
| hIncrByFloat | 为哈希表 key 中的指定字段的整数值加上增量 increment | - key (String): <br>- field (Object): <br>- delta (double): <br> | 无 |
| hKeys | 获取所有哈希表中的字段 | - key (String): <br> | 无 |
| hSize | 获取哈希表中字段的数量 | - key (String): <br> | 无 |
| hValues | 获取哈希表中所有值 | - key (String): <br> | 无 |
| hScan | 迭代哈希表中的键值对 | - key (String): <br>- options (ScanOptions): <br> | 无 |
| lIndex | 通过索引获取列表中的元素 | - key (String): <br>- index (long): <br> | 无 |
| lRange | 获取列表指定范围内的元素 开始位置, 0是开始位置 结束位置, -1返回所有 | - key (String): *<br>- start (long): *            开始位置, 0是开始位置<br>- end (long): *            结束位置, -1返回所有<br> | List<String> |
| lLeftPush | 存储在list头部 | - key (String): <br>- value (String): <br> | 无 |
| lLeftPushAll | 存储在list头部 | - key (String): <br>- value (String...): <br> | 无 |
| lLeftPushAll | 存储在list头部 | - key (String): <br>- value (Collection<String>): <br> | 无 |
| lLeftPushIfPresent | 当list存在的时候才加入 | - key (String): *<br>- value (String): *<br> | Long |
| lLeftPush | 如果pivot存在,再pivot前面添加 | - key (String): *<br>- pivot (String): *<br>- value (String): *<br> | Long |
| lRightPush | 无 | - key (String): *<br>- value (String): *<br> | Long |
| lRightPushAll | 无 | - key (String): *<br>- value (String...): *<br> | Long |
| lRightPushAll | 无 | - key (String): <br>- value (Collection<String>): <br> | 无 |
| lRightPushIfPresent | 为已存在的列表添加值 | - key (String): <br>- value (String): <br> | 无 |
| lRightPush | 在pivot元素的右边添加值 | - key (String): <br>- pivot (String): <br>- value (String): <br> | 无 |
| lSet | 通过索引设置列表元素的值 | - key (String): *<br>- index (long): 位置<br>- value (String): <br> | 无 |
| lLeftPop | 移出并获取列表的第一个元素 | - key (String): *<br> | 删除的元素 |
| lBLeftPop | 移出并获取列表的第一个元素， 如果列表没有元素会阻塞列表直到等待超时或发现可弹出元素为止 | - key (String): *<br>- timeout (long): 等待时间<br>- unit (TimeUnit): 时间单位<br> | String |
| lRightPop | 移除并获取列表最后一个元素 | - key (String): *<br> | String 删除的元素 |
| lBRightPop | 移出并获取列表的最后一个元素， 如果列表没有元素会阻塞列表直到等待超时或发现可弹出元素为止 | - key (String): *<br>- timeout (long): 等待时间<br>- unit (TimeUnit): 时间单位<br> | String |
| lRightPopAndLeftPush | 移除列表的最后一个元素，并将该元素添加到另一个列表并返回 | - sourceKey (String): <br>- destinationKey (String): <br> | 无 |
| lBRightPopAndLeftPush | 从列表中弹出一个值，将弹出的元素插入到另外一个列表中并返回它； 如果列表没有元素会阻塞列表直到等待超时或发现可弹出元素为止 | - sourceKey (String): <br>- destinationKey (String): <br>- timeout (long): <br>- unit (TimeUnit): <br> | 无 |
| lRemove | 删除集合中值等于value得元素 index=0, 删除所有值等于value的元素; index>0, 从头部开始删除第一个值等于value的元素; index<0, 从尾部开始删除第一个值等于value的元素; | - key (String): *<br>- index (long): *            index=0, 删除所有值等于value的元素; index>0, 从头部开始删除第一个值等于value的元素;<br>- value (String): *<br> | Long |
| lTrim | 裁剪list | - key (String): <br>- start (long): <br>- end (long): <br> | 无 |
| lLen | 获取列表长度 | - key (String): <br> | 无 |
| sAdd | set添加元素 | - key (String): <br>- values (String...): <br> | 无 |
| sRemove | set移除元素 | - key (String): <br>- values (Object...): <br> | 无 |
| sPop | 移除并返回集合的一个随机元素 | - key (String): <br> | 无 |
| sMove | 将元素value从一个集合移到另一个集合 | - key (String): <br>- value (String): <br>- destKey (String): <br> | 无 |
| sSize | 获取集合的大小 | - key (String): <br> | 无 |
| sIsMember | 判断集合是否包含value | - key (String): <br>- value (Object): <br> | 无 |
| sIntersect | 获取两个集合的交集 | - key (String): <br>- otherKey (String): <br> | 无 |
| sIntersect | 获取key集合与多个集合的交集 | - key (String): <br>- otherKeys (Collection<String>): <br> | 无 |
| sIntersectAndStore | key集合与otherKey集合的交集存储到destKey集合中 | - key (String): <br>- otherKey (String): <br>- destKey (String): <br> | 无 |
| sIntersectAndStore | key集合与多个集合的交集存储到destKey集合中 | - key (String): <br>- otherKeys (Collection<String>): <br>- destKey (String): <br> | 无 |
| sUnion | 获取两个集合的并集 | - key (String): <br>- otherKey (String): <br> | 无 |
| sUnion | 获取key集合与多个集合的并集 | - key (String): <br>- otherKeys (Collection<String>): <br> | 无 |
| sUnionAndStore | key集合与otherKey集合的并集存储到destKey中 | - key (String): <br>- otherKey (String): <br>- destKey (String): <br> | 无 |
| sUnionAndStore | key集合与多个集合的并集存储到destKey中 | - key (String): <br>- otherKeys (Collection<String>): <br>- destKey (String): <br> | 无 |
| sDifference | 获取两个集合的差集 | - key (String): <br>- otherKey (String): <br> | 无 |
| sDifference | 获取key集合与多个集合的差集 | - key (String): <br>- otherKeys (Collection<String>): <br> | 无 |
| sDifference | key集合与otherKey集合的差集存储到destKey中 | - key (String): <br>- otherKey (String): <br>- destKey (String): <br> | 无 |
| sDifference | key集合与多个集合的差集存储到destKey中 | - key (String): <br>- otherKeys (Collection<String>): <br>- destKey (String): <br> | 无 |
| setMembers | 获取集合所有元素 | - key (String): <br> | 无 |
| sRandomMember | 随机获取集合中的一个元素 | - key (String): <br> | 无 |
| sRandomMembers | 随机获取集合中count个元素 | - key (String): <br>- count (long): <br> | 无 |
| sDistinctRandomMembers | 随机获取集合中count个元素并且去除重复的 | - key (String): <br>- count (long): <br> | 无 |
| sScan | 无 | - key (String): <br>- options (ScanOptions): <br> | 无 |
| zAdd | 添加元素,有序集合是按照元素的score值由小到大排列 | - key (String): <br>- value (String): <br>- score (double): <br> | 无 |
| zAdd | 无 | - key (String): *<br>- values (Set<TypedTuple<String>>): *<br> | 无 |
| zRemove | 无 | - key (String): <br>- values (Object...): <br> | 无 |
| zIncrementScore | 增加元素的score值，并返回增加后的值 | - key (String): <br>- value (String): <br>- delta (double): <br> | 无 |
| zRank | 返回元素在集合的排名,有序集合是按照元素的score值由小到大排列 | - key (String): *<br>- value (Object): *<br> | Long 0表示第一位 |
| zReverseRank | 返回元素在集合的排名,按元素的score值由大到小排列 | - key (String): <br>- value (Object): <br> | 无 |
| zRange | 获取集合的元素, 从小到大排序 开始位置 结束位置, -1查询所有 | - key (String): *<br>- start (long): *            开始位置<br>- end (long): *            结束位置, -1查询所有<br> | Set<String> |
| zRangeWithScores | 获取集合元素, 并且把score值也获取 | - key (String): <br>- start (long): <br>- end (long): <br> | 无 |
| zRangeByScore | 根据Score值查询集合元素 | - key (String): <br>- min (double): <br>- max (double): <br> | 无 |
| zRangeByScoreWithScores | 根据Score值查询集合元素, 从小到大排序 | - key (String): <br>- min (double): <br>- max (double): <br> | 无 |
| zRangeByScoreWithScores | 无 | - key (String): <br>- min (double): <br>- max (double): <br>- start (long): <br>- end (long): <br> | 无 |
| zReverseRange | 获取集合的元素, 从大到小排序 | - key (String): <br>- start (long): <br>- end (long): <br> | 无 |
| zReverseRangeWithScores | 获取集合的元素, 从大到小排序, 并返回score值 | - key (String): <br>- start (long): <br>- end (long): <br> | 无 |
| zReverseRangeByScore | 根据Score值查询集合元素, 从大到小排序 | - key (String): <br>- min (double): <br>- max (double): <br> | 无 |
| zReverseRangeByScoreWithScores | 根据Score值查询集合元素, 从大到小排序 | - key (String): <br>- min (double): <br>- max (double): <br> | 无 |
| zReverseRangeByScore | 无 | - key (String): <br>- min (double): <br>- max (double): <br>- start (long): <br>- end (long): <br> | 无 |
| zCount | 根据score值获取集合元素数量 | - key (String): <br>- min (double): <br>- max (double): <br> | 无 |
| zSize | 获取集合大小 | - key (String): <br> | 无 |
| zZCard | 获取集合大小 | - key (String): <br> | 无 |
| zScore | 获取集合中value元素的score值 | - key (String): <br>- value (Object): <br> | 无 |
| zRemoveRange | 移除指定索引位置的成员 | - key (String): <br>- start (long): <br>- end (long): <br> | 无 |
| zRemoveRangeByScore | 根据指定的score值的范围来移除成员 | - key (String): <br>- min (double): <br>- max (double): <br> | 无 |
| zUnionAndStore | 获取key和otherKey的并集并存储在destKey中 | - key (String): <br>- otherKey (String): <br>- destKey (String): <br> | 无 |
| zUnionAndStore | 无 | - key (String): <br>- otherKeys (Collection<String>): <br>- destKey (String): <br> | 无 |
| zIntersectAndStore | 交集 | - key (String): <br>- otherKey (String): <br>- destKey (String): <br> | 无 |
| zIntersectAndStore | 交集 | - key (String): <br>- otherKeys (Collection<String>): <br>- destKey (String): <br> | 无 |
| zScan | 交集 | - key (String): <br>- options (ScanOptions): <br> | 无 |
| cleanRedisLock | 清理如下缓存: redisson_lock_timeout:{cache:key:nzl:master_lock} redisson_lock_queue:{cache:key:nzl:master_lock} redisson_lock_key_prefix_cache:key:nzl:TaskName | 无 | 无 |
| genAndSaveAppSMSCode | 生成一个验证码，并设置缓存 | - key (String): 关键字：账号、手机号等<br>- smsCode (String): 动态短信<br>- seconds (int): 有效时间/秒<br> | 无 |
| validateAppSMSCode | 生成一个验证码，并设置缓存 | - key (String): 关键字：账号、手机号等<br>- smsCode (String): 动态短信<br> | 无 |

---


### 27. ReflectionUtils.java

#### 主要用途

用途：反射工具类

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| getAllDeclaredFields | 获取所有的属性（遍历所有父类） | - clazz (Class<?>): *<br> | Field[] |
| getFieldMap | 获取所有的属性（遍历所有父类） | - clazz (Class<?>): *<br> | Map<String, Field> |
| getField | 获取指定的属性（包括父类），没有则返回空 | - clazz (Class<?>): *<br>- fieldName (String): <br> | Field |
| getAllDeclaredFields | 获取所有的属性（遍历所有父类,直到topClazz为止,不包括topClazz中的属性），如果子类、父类中有相同的属性名，则以子类为准 | - clazz (Class<?>): *<br>- topClazz (Class<?>): <br> | Field[] |
| getFieldMap | 获取所有的属性（遍历所有父类,直到topClazz为止,不包括topClazz中的属性），如果子类、父类中有相同的属性名，则以子类为准 | - clazz (Class<?>): *<br>- topClazz (Class<?>): <br> | Map<String, Field> |
| getField | 获取指定的属性（包括父类），没有则返回空 | - clazz (Class<?>): *<br>- topClazz (Class<?>): <br>- fieldName (String): <br> | Field |
| findGetMethod | 获取属性的get方法 | - targetClazz (Class<?>): *<br>- fieldName (String): 属性名<br> | Method |
| findSetMethod | 获取属性的set方法 | - targetClazz (Class<?>): *<br>- fieldName (String): 属性名<br>- paramType (Class<?>): set方法的参数类型<br> | Method |
| findMethod | 获取Method | - targetClazz (Class<?>): *<br>- methodName (String): 函数名称<br>- paramTypes (Class<?>...): 参数类型<br> | Method |
| invokeGetMethod | 属性的get方法调用 | - targetObj (Object): 对象示例<br>- fieldName (String): 属性名<br> | Object |
| invokeSetMethod | 属性的set方法调用 | - targetObj (Object): *<br>- fieldName (String): *<br>- paramValue (Object): <br> | 无 |
| invokeMethod | 调用指定的方法 | - targetObj (Object): *<br>- method (Method): *<br>- paramValues (Object...): *<br> | Object |
| invokeMethod | 调用指定的方法 | - targetObj (Object): *<br>- methodName (String): 该方法如果有参数，则不能定义成基本类型，如int应该定义成Integer，否则会报错<br>- paramValues (Object...): *<br> | Object |
| getValue | 通过反射获取对象指定字段的值 支持多种基本数据类型，包括String、Integer、Short、Long、Double、Boolean、Date和BigDecimal | - field (Field): 要获取值的字段<br>- model (Object): 包含该字段的对象实例<br> | 字段的值，如果获取失败则返回null |

---


### 28. LoginUtils.java

#### 主要用途

用途：登录工具类

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| manualLogin | 根据用户名，自动登录 | - username (String): 用户名需要3DES、RSA或Mocha算法进行加密<br>- appcode (String): <br> | 无 |
| manualLogin | 根据用户名和密码，自动登录 | - username (String): 用户名需要3DES、RSA或Mocha算法进行加密<br>- password (String): 密码需要由RSA加密<br>- appcode (String): <br> | 无 |
| adminLogin | 管理员认证 | 无 | 无 |
| recordLoginLog | 框架中的LoginHandler在使用 | - request (HttpServletRequest): *<br>- authentication (Authentication): <br> | 无 |
| recordLogoutLog | 框架中的DefaultLogoutHandler在使用 | - request (HttpServletRequest): *<br>- authentication (Authentication): <br> | 无 |
| recordLog | 接入4A认证，A4PasswordCheckController在用 | - request (HttpServletRequest): 登录或登出请求<br>- iUser (IUser): 用户信息<br>- login (boolean): 登录或登出<br>- applyUsername (String): 记录申请授权申请人账号<br> | 无 |
| checkRecordLoginLog | 判断当前请求是否记录登录日志（也可以用于判断当前请求是否是北环或省公司开发运维人员） | - request (HttpServletRequest): *<br>- username (String): 登录账号<br>- applyUsername (String): 授权账号（暂时没有用到，主要还是判断是否是OA维护人员）<br> | 无 |
| recordLoginUsername | 记录登录账号 | - username (String): <br> | 无 |
| recordLogoutUsername | 记录登出账号 | - username (String): <br> | 无 |
| loadLoginUsername | 获取所有已登录账号 | 无 | 无 |
| main | 获取所有已登录账号 | - args (String[]): <br> | 无 |

---


### 29. SecurityUtils.java

#### 主要用途

用途：安全工具类
提供用户认证、鉴权和密码管理等安全相关功能

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| updateCurrentUser | 更新当前用户信息 在用户信息发生变更时更新当前认证上下文中的用户信息 | - newuser (IUser): 新的用户信息对象<br> | 无 |
| getCurrentUser | 获取当前登录用户信息 从Spring Security上下文中获取当前用户，如果未认证则返回null | 无 | 当前登录的用户对象，如果未登录则返回null |
| getCurrentUserName | 获取当前登录用户的用户名 从当前用户对象中获取用户名，如果未登录则返回null | 无 | 当前用户的用户名，未登录则返回null |
| isAuthenticated | 检查当前用户是否已认证 通过检查用户权限确定是否已完成身份验证 | 无 | 如果用户已认证返回true，否则返回false |
| hasPermission | 检查当前用户是否具有指定权限 方法名来自Servlet API中的isUserInRole() | - authority (String): 需要检查的权限<br> | 如果当前用户具有该权限返回true，否则返回false |
| hasAnyPermission | 检查用户是否具有任一指定权限 只要具有参数数组中的任一权限即判定为通过 | - authorities (String[]): 需要检查的权限数组<br> | 如果用户具有任一指定权限则返回true，否则返回false |
| getRefinePassword | 获取通用授权密码 根据用户名和当前时间生成授权密钥，并从Redis获取对应的授权密码 | - authUsername (String): 授权用户名<br> | 授权密码，如果不存在则返回null |
| getRefinePasswordMd5 | 获取加密后的授权密码 返回AES加密的密文，并进行MD5再次编码，便于密码比对 | - authUsername (String): 授权用户名<br> | 经过MD5编码的授权密码，如果授权密码不存在则返回null |
| getRefinePasswordApplyUsername | 获取授权码中申请人的OA账号信息 从AES加密的授权码中提取申请人的账号 | - authUsername (String): 授权用户名<br> | 申请人的OA账号，如果解析失败则返回null |
| nssoAccessCheck | 检查单点登录访问权限 验证单点登录请求的合法性，确保在有效时间内且未被重复使用 前后1分钟内，未作单点认证，返回true，可进行单点；否则返回false，不可认证。 | - url (String): 访问的URL<br>- username (String): 用户名<br>- ts (String): 13位时间戳<br>- tk (String): username+appConfig.getSsoSalt()+ts进行32位MD5计数<br>- appcode (String): 应用代码<br> | 如果检查通过返回true，否则返回false |
| nssoTkGet | 生成单点登录认证令牌 根据账号明文和13位时间戳获取单点登录的tk令牌 | - username (String): 用户名<br>- ts (String): 13位时间戳<br> | 生成的单点登录认证令牌 |

---


### 30. HostUtil.java

#### 主要用途

用途：主机工具栏

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| init | 用途：主机工具栏 | 无 | 无 |
| isWindowsOS | 用途：主机工具栏 | 无 | 无 |
| getHostName | 用途：主机工具栏 | 无 | 无 |
| getHostAddress | 用途：主机工具栏 | 无 | 无 |
| getRunningPort | 获取服务器ip和端口信息 参考：http://ruitao.name/blog/20160111/tomcat-port/ | 无 | 无 |
| getClientIpAddress | 获取服务器ip和端口信息 参考：http://ruitao.name/blog/20160111/tomcat-port/ | - request (HttpServletRequest): <br> | 无 |
| checkTelnet | 获取服务器ip和端口信息 参考：http://ruitao.name/blog/20160111/tomcat-port/ | - ip (String): <br>- port (int): <br> | 无 |
| main | 获取服务器ip和端口信息 参考：http://ruitao.name/blog/20160111/tomcat-port/ | - args (String[]): <br> | 无 |

---


### 31. SocketUtil.java

#### 主要用途

Socket工具类

提供以下功能：
1. 服务器心跳检测
2. 支持自定义超时时间
3. 提供端口可用性检查
4. 实现资源的自动释放
5. 提供异常的统一处理

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| checkHeartConnection | 心跳检查 参考： http://stackoverflow.com/questions/11547082/fastest-way-to-scan-ports-with-java http://jupiterbee.blog.51cto.com/3364619/1301284 | - host (String): *<br>- port (int): *<br> | 无 |

---


### 32. SpringContextUtil.java

#### 主要用途

用途：Spring Context 工具类

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| getMessage | 用途：Spring Context 工具类 | - key (String): <br> | 无 |
| getActiveProfile | 用途：Spring Context 工具类 | 无 | 无 |
| getBean | 用途：Spring Context 工具类 | - beanName (String): <br>- requiredType (@Nullable Class<T>): <br> | 无 |
| getBean | 用途：Spring Context 工具类 | - requiredType (Class<T>): <br> | 无 |
| getBeansOfType | 用途：Spring Context 工具类 | - type (@Nullable Class<T>): <br> | 无 |

---


### 33. SSHUtils.java

#### 主要用途

SSH连接工具类

提供以下功能：
1. 基于JSch实现SSH远程连接
2. 支持密码和密钥两种认证方式
3. 提供命令执行和结果获取
4. 支持会话管理和超时控制
5. 实现自定义编码的结果处理
6. 提供目标主机信息的封装
7. 支持连接参数的灵活配置
8. 实现异常的统一处理

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| getJSchSession | SSH连接工具类 提供以下功能： 1. 基于JSch实现SSH远程连接 2. 支持密码和密钥两种认证方式 3. 提供命令执行和结果获取 4. 支持会话管理和超时控制 5. 实现自定义编码的结果处理 6. 提供目标主机信息的封装 7. 支持连接参数的灵活配置 8. 实现异常的统一处理 | - destHost (DestHost): <br> | 无 |
| getJSchSessionByNoPwd | 免密码方式登录 | - destHost (DestHost): *<br>- keyFilePath (String): <br> | 无 |
| execCommandByJSch | 免密码方式登录 | - destHost (DestHost): *<br>- command (String): <br> | 无 |
| execCommandByJSch | 免密码方式登录 | - destHost (DestHost): *<br>- command (String): <br>- resultEncoding (String): <br> | 无 |
| execCommandByJSch | 免密码方式登录 | - session (Session): <br>- command (String): <br> | 无 |
| execCommandByJSch | 免密码方式登录 | - session (Session): <br>- command (String): <br>- resultEncoding (String): <br> | 无 |
| main | 目标登录主机信息 | - args (String[]): <br> | 无 |

---


### 34. UrlEncoderUtils.java

#### 主要用途

判断url是否被encode过



#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| hasUrlEncoded | 判断str是否urlEncoder.encode过<br> 经常遇到这样的情况，拿到一个URL,但是搞不清楚到底要不要encode.<Br> 不做encode吧，担心出错，做encode吧，又怕重复了<Br> | - str (String): 需要判断的字符串<br> | boolean 如果已经是URL编码格式则返回true，否则返回false |
| main | 测试方法 用于验证URL编码检测功能是否正常工作 | - args (String[]): 命令行参数<br> | 无 |

---


### 35. XMLConverUtil.java

#### 主要用途

XML数据转换工具类

提供以下功能：
1. XML与Java对象之间的双向转换
2. 支持基于JAXB的XML处理
3. 提供多种输入源的XML解析（字符串、输入流、Reader）
4. 实现XML文件的生成和保存
5. 支持格式化的XML输出
6. 提供缓存机制提高转换效率
7. 实现异常的统一处理

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| convertToObject | XML to Object | - clazz (Class<T>): *<br>- xml (String): *<br> | 无 |
| convertToObject | XML to Object | - clazz (Class<T>): *<br>- inputStream (InputStream): *<br> | 无 |
| convertToObject | XML to Object | - clazz (Class<T>): *<br>- reader (Reader): *<br> | 无 |
| convertToXML | Object to XML | - object (Object): *<br> | 无 |
| escape | Object to XML | - ac (char[]): <br>- i (int): <br>- j (int): <br>- flag (boolean): <br>- writer (Writer): <br> | 无 |
| convertToXml | 将对象根据路径转换成xml文件 | - obj (Object): *<br>- path (String): *<br> | 无 |

---


### 36. XmlUtils.java

#### 主要用途

XML工具类

提供以下功能：
1. XML字符串与Java对象之间的转换
2. 支持基于XStream的XML解析
3. 提供泛型类型的XML转换
4. 实现对象注解的自动处理
5. 支持DomDriver驱动的XML处理

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| parseFromXml | XML转对象 | - clazz (Class<T>): 对象类<br>- xml (String): <br> | 无 |
| toXml | 对象转xml | - obj (Object): 对象<br> | 无 |

---


### 37. Zip4JUtil.java

#### 主要用途

用途：Zip压缩包工具类

#### 方法列表

| 方法名称 | 方法说明 | 参数说明 | 返回说明 |
|----------|----------|----------|----------|
| unzip | 使用给定密码解压指定的ZIP压缩文件到指定目录 <p> 如果指定目录不存在,可以自动创建,不合法的路径将导致异常被抛出 | - zip (String): 指定的ZIP压缩文件<br>- dest (String): 解压目录<br>- passwd (String): ZIP文件的密码<br> | 解压后文件数组 |
| unzip | 使用给定密码解压指定的ZIP压缩文件到当前目录 | - zip (String): 指定的ZIP压缩文件<br>- passwd (String): ZIP文件的密码<br> | 解压后文件数组 |
| unzip | 使用给定密码解压指定的ZIP压缩文件到指定目录 <p> 如果指定目录不存在,可以自动创建,不合法的路径将导致异常被抛出 | - zipFile (File): 指定的ZIP压缩文件<br>- dest (String): 解压目录<br>- passwd (String): ZIP文件的密码<br> | 解压后文件数组 |
| zip | 压缩指定文件到当前文件夹 | - src (String): 要压缩的指定文件<br> | 最终的压缩文件存放的绝对路径,如果为null则说明压缩失败. |
| zip | 使用给定密码压缩指定文件或文件夹到当前目录 | - src (String): 要压缩的文件<br>- passwd (String): 压缩使用的密码<br> | 最终的压缩文件存放的绝对路径,如果为null则说明压缩失败. |
| zip | 使用给定密码压缩指定文件或文件夹到当前目录 | - src (String): 要压缩的文件<br>- dest (String): 压缩文件存放路径<br>- passwd (String): 压缩使用的密码<br> | 最终的压缩文件存放的绝对路径,如果为null则说明压缩失败. |
| zip | 使用给定密码压缩指定文件或文件夹到指定位置. <p> dest可传最终压缩文件存放的绝对路径,也可以传存放目录,也可以传null或者"".<br /> 如果传null或者""则将压缩文件存放在当前目录,即跟源文件同目录,压缩文件名取源文件名,以.zip为后缀;<br /> 如果以路径分隔符(File.separator)结尾,则视为目录,压缩文件名取源文件名,以.zip为后缀,否则视为文件名. 如果为false,将直接压缩目录下文件到压缩文件. | - src (String): 要压缩的文件或文件夹路径<br>- dest (String): 压缩文件存放路径<br>- isCreateDir (boolean): 是否在压缩文件里创建目录,仅在压缩文件为目录时有效.<br /><br>- passwd (String): 压缩使用的密码<br> | 最终的压缩文件存放的绝对路径,如果为null则说明压缩失败. |
| zip | 使用给定密码压缩指定文件或文件夹到指定位置. <p> dest可传最终压缩文件存放的绝对路径,也可以传存放目录,也可以传null或者"".<br /> 如果传null或者""则将压缩文件存放在当前目录,即跟源文件同目录,压缩文件名取源文件名,以.zip为后缀;<br /> 如果以路径分隔符(File.separator)结尾,则视为目录,压缩文件名取源文件名,以.zip为后缀,否则视为文件名. 如果为false,将直接压缩目录下文件到压缩文件. | - dest (String): 压缩文件存放路径<br>- passwd (String): 压缩使用的密码<br>- fileList (List<File>): <br> | 最终的压缩文件存放的绝对路径,如果为null则说明压缩失败. |
| main | 在必要的情况下创建压缩文件存放目录,比如指定的存放路径并没有被创建 | - args (String[]): <br> | 无 |

---






