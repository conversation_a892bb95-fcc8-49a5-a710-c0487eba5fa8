# 数据库设计

## 数据库概述

本系统采用MySQL 8.0作为主要数据库管理系统，设计遵循第三范式，确保数据的一致性和完整性。数据库设计主要围绕用户权限管理、部门组织架构、审批流程定义、审批单据管理等核心功能进行。

系统采用微服务架构，各微服务可以使用独立的数据库实例，主要分为以下几个数据库：

1. **系统基础数据库**：存储用户、角色、权限等基础数据
2. **业务数据库**：存储审批业务相关数据
3. **Camunda流程引擎数据库**：存储流程定义、流程实例、任务等数据

### Camunda流程引擎表结构概述

Camunda流程引擎的表可以分为以下几类：

1. **流程存储表(ACT_RE_)**：用于存储流程定义和流程资源等静态信息
2. **流程运行时表(ACT_RU_)**：用于存储流程实例、用户任务、变量等运行时数据
3. **流程历史表(ACT_HI_)**：用于存储已完成的流程实例、任务等历史数据
4. **用户与组表(ACT_ID_)**：用于存储用户和组的信息

## 数据库表设计

### 系统基础表

#### 1. 用户表 sys_user

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 用户ID |
| username | varchar | 50 | 否 | | | 用户名 |
| password | varchar | 100 | 否 | | | 密码(加密) |
| real_name | varchar | 50 | 否 | | | 真实姓名 |
| employee_no | varchar | 30 | 是 | | | 工号 |
| dept_id | bigint | 20 | 否 | | | 部门ID |
| position | varchar | 50 | 是 | | | 职位 |
| email | varchar | 100 | 是 | | | 邮箱 |
| mobile | varchar | 20 | 是 | | | 手机号 |
| gender | tinyint | 1 | 是 | | 0 | 性别(0未知,1男,2女) |
| avatar | varchar | 200 | 是 | | | 头像 |
| status | tinyint | 1 | 否 | | 1 | 状态(0禁用,1正常) |
| creator | bigint | 20 | 是 | | | 创建人 |
| create_time | datetime | | 否 | | CURRENT_TIMESTAMP | 创建时间 |
| updater | bigint | 20 | 是 | | | 更新人 |
| update_time | datetime | | 是 | | | 更新时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

#### 2. 角色表 sys_role

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 角色ID |
| role_name | varchar | 50 | 否 | | | 角色名称 |
| role_code | varchar | 50 | 否 | | | 角色编码 |
| description | varchar | 200 | 是 | | | 角色描述 |
| status | tinyint | 1 | 否 | | 1 | 状态(0禁用,1正常) |
| create_time | datetime | | 否 | | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | | 是 | | | 更新时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

#### 3. 用户角色关联表 sys_user_role

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 记录ID |
| user_id | bigint | 20 | 否 | | | 用户ID |
| role_id | bigint | 20 | 否 | | | 角色ID |
| create_time | datetime | | 否 | | CURRENT_TIMESTAMP | 创建时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

#### 4. 部门表 sys_dept

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 部门ID |
| parent_id | bigint | 20 | 否 | | 0 | 父部门ID |
| dept_name | varchar | 50 | 否 | | | 部门名称 |
| dept_code | varchar | 50 | 是 | | | 部门编码 |
| order_num | int | 11 | 否 | | 0 | 显示顺序 |
| leader_id | bigint | 20 | 是 | | | 负责人ID |
| status | tinyint | 1 | 否 | | 1 | 状态(0禁用,1正常) |
| create_time | datetime | | 否 | | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | | 是 | | | 更新时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

#### 5. 菜单权限表 sys_menu

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 菜单ID |
| parent_id | bigint | 20 | 否 | | 0 | 父菜单ID |
| menu_name | varchar | 50 | 否 | | | 菜单名称 |
| menu_type | char | 1 | 否 | | | 菜单类型(M目录,C菜单,F按钮) |
| permission | varchar | 100 | 是 | | | 权限标识 |
| path | varchar | 200 | 是 | | | 路由地址 |
| component | varchar | 200 | 是 | | | 组件路径 |
| icon | varchar | 100 | 是 | | | 图标 |
| order_num | int | 11 | 否 | | 0 | 显示顺序 |
| status | tinyint | 1 | 否 | | 1 | 状态(0禁用,1正常) |
| create_time | datetime | | 否 | | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | | 是 | | | 更新时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

#### 6. 角色菜单关联表 sys_role_menu

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 记录ID |
| role_id | bigint | 20 | 否 | | | 角色ID |
| menu_id | bigint | 20 | 否 | | | 菜单ID |
| create_time | datetime | | 否 | | CURRENT_TIMESTAMP | 创建时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

### 审批流程相关表

#### 1. 业务流程与Camunda流程映射表 wf_process_mapping

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 映射ID |
| process_code | varchar | 50 | 否 | | | 业务流程编码 |
| process_name | varchar | 100 | 否 | | | 业务流程名称 |
| process_type | varchar | 50 | 否 | | | 流程类型(travel/leave/vehicle/meeting/office) |
| camunda_process_key | varchar | 100 | 否 | | | Camunda流程定义Key |
| camunda_process_id | varchar | 64 | 否 | | | Camunda流程定义ID |
| camunda_deployment_id | varchar | 64 | 否 | | | Camunda部署ID |
| current_version | int | 11 | 否 | | 1 | 当前版本号 |
| bpmn_xml | text | | 否 | | | BPMN XML内容 |
| description | varchar | 200 | 是 | | | 流程描述 |
| form_id | bigint | 20 | 是 | | | 关联表单ID |
| status | tinyint | 1 | 否 | | 1 | 状态(0禁用,1正常) |
| creator | bigint | 20 | 否 | | | 创建人 |
| create_time | datetime | | 否 | | CURRENT_TIMESTAMP | 创建时间 |
| updater | bigint | 20 | 是 | | | 更新人 |
| update_time | datetime | | 是 | | | 更新时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

#### 2. 流程实例关联表 wf_instance_mapping

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 关联ID |
| business_key | varchar | 64 | 否 | | | 业务键(关联业务表ID) |
| instance_code | varchar | 50 | 否 | | | 业务实例编码 |
| process_mapping_id | bigint | 20 | 否 | | | 流程映射ID |
| camunda_process_instance_id | varchar | 64 | 否 | | | Camunda流程实例ID |
| camunda_process_definition_id | varchar | 64 | 否 | | | Camunda流程定义ID |
| title | varchar | 200 | 否 | | | 标题 |
| applicant_id | bigint | 20 | 否 | | | 申请人ID |
| dept_id | bigint | 20 | 否 | | | 申请部门ID |
| status | varchar | 20 | 否 | | draft | 状态(draft/running/approved/rejected/canceled) |
| create_time | datetime | | 否 | | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | | 是 | | | 更新时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

#### 3. 流程任务关联表 wf_task_mapping

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 任务映射ID |
| instance_mapping_id | bigint | 20 | 否 | | | 实例映射ID |
| camunda_task_id | varchar | 64 | 否 | | | Camunda任务ID |
| camunda_task_definition_key | varchar | 64 | 否 | | | Camunda任务定义Key |
| task_name | varchar | 100 | 否 | | | 任务名称 |
| assignee_id | bigint | 20 | 是 | | | 审批人ID |
| candidate_users | varchar | 500 | 是 | | | 候选用户(逗号分隔) |
| candidate_groups | varchar | 500 | 是 | | | 候选组(逗号分隔) |
| status | varchar | 20 | 否 | | pending | 状态(pending/approved/rejected/transferred) |
| comment | varchar | 500 | 是 | | | 审批意见 |
| create_time | datetime | | 否 | | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | | 是 | | | 更新时间 |
| complete_time | datetime | | 是 | | | 完成时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

#### 4. 表单定义表 wf_form

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 表单ID |
| form_code | varchar | 50 | 否 | | | 表单编码 |
| form_name | varchar | 100 | 否 | | | 表单名称 |
| form_type | varchar | 50 | 否 | | | 表单类型(travel/leave/vehicle/meeting/office) |
| form_content | text | | 否 | | | 表单JSON结构 |
| form_html | text | | 是 | | | 表单HTML结构 |
| version | int | 11 | 否 | | 1 | 版本号 |
| status | tinyint | 1 | 否 | | 1 | 状态(0禁用,1正常) |
| creator | bigint | 20 | 否 | | | 创建人 |
| create_time | datetime | | 否 | | CURRENT_TIMESTAMP | 创建时间 |
| updater | bigint | 20 | 是 | | | 更新人 |
| update_time | datetime | | 是 | | | 更新时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

#### 5. 审批操作记录表 wf_operation_log

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 记录ID |
| instance_mapping_id | bigint | 20 | 否 | | | 实例映射ID |
| task_mapping_id | bigint | 20 | 是 | | | 任务映射ID |
| camunda_task_id | varchar | 64 | 是 | | | Camunda任务ID |
| operation_type | varchar | 50 | 否 | | | 操作类型(submit/approve/reject/cancel/transfer) |
| operator_id | bigint | 20 | 否 | | | 操作人ID |
| operation_result | varchar | 20 | 否 | | | 操作结果(success/fail) |
| comment | varchar | 500 | 是 | | | 操作意见 |
| operation_time | datetime | | 否 | | CURRENT_TIMESTAMP | 操作时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

### Camunda流程引擎主要表

#### 1. 流程定义表 ACT_RE_PROCDEF

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ---- |
| ID_ | varchar | 64 | 否 | 是 | 流程定义ID |
| REV_ | int | 11 | 是 | | 版本号 |
| CATEGORY_ | varchar | 255 | 是 | | 分类 |
| NAME_ | varchar | 255 | 是 | | 流程定义名称 |
| KEY_ | varchar | 255 | 否 | | 流程定义Key |
| VERSION_ | int | 11 | 否 | | 流程定义版本 |
| DEPLOYMENT_ID_ | varchar | 64 | 是 | | 部署ID |
| RESOURCE_NAME_ | varchar | 4000 | 是 | | 资源名称 |
| DGRM_RESOURCE_NAME_ | varchar | 4000 | 是 | | 图表资源名称 |
| HAS_START_FORM_KEY_ | tinyint | 1 | 是 | | 是否有开始表单Key |
| SUSPENSION_STATE_ | int | 11 | 是 | | 挂起状态 |
| TENANT_ID_ | varchar | 64 | 是 | | 租户ID |
| VERSION_TAG_ | varchar | 64 | 是 | | 版本标签 |
| HISTORY_TTL_ | int | 11 | 是 | | 历史保留时间 |

#### 2. 流程部署表 ACT_RE_DEPLOYMENT

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ---- |
| ID_ | varchar | 64 | 否 | 是 | 部署ID |
| NAME_ | varchar | 255 | 是 | | 部署名称 |
| DEPLOY_TIME_ | timestamp | | 是 | | 部署时间 |
| SOURCE_ | varchar | 255 | 是 | | 来源 |
| TENANT_ID_ | varchar | 64 | 是 | | 租户ID |

#### 3. 流程实例表 ACT_RU_EXECUTION

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ---- |
| ID_ | varchar | 64 | 否 | 是 | 流程实例ID |
| REV_ | int | 11 | 是 | | 版本号 |
| PROC_INST_ID_ | varchar | 64 | 是 | | 流程实例ID |
| BUSINESS_KEY_ | varchar | 255 | 是 | | 业务Key |
| PARENT_ID_ | varchar | 64 | 是 | | 父ID |
| PROC_DEF_ID_ | varchar | 64 | 是 | | 流程定义ID |
| SUPER_EXEC_ | varchar | 64 | 是 | | 父执行ID |
| SUPER_CASE_EXEC_ | varchar | 64 | 是 | | 父案例执行ID |
| CASE_INST_ID_ | varchar | 64 | 是 | | 案例实例ID |
| ACT_ID_ | varchar | 255 | 是 | | 活动ID |
| ACT_INST_ID_ | varchar | 64 | 是 | | 活动实例ID |
| IS_ACTIVE_ | tinyint | 1 | 是 | | 是否活动 |
| IS_CONCURRENT_ | tinyint | 1 | 是 | | 是否并行 |
| IS_SCOPE_ | tinyint | 1 | 是 | | 是否范围 |
| IS_EVENT_SCOPE_ | tinyint | 1 | 是 | | 是否事件范围 |
| SUSPENSION_STATE_ | int | 11 | 是 | | 挂起状态 |
| CACHED_ENT_STATE_ | int | 11 | 是 | | 缓存的实体状态 |
| TENANT_ID_ | varchar | 64 | 是 | | 租户ID |

#### 4. 任务表 ACT_RU_TASK

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ---- |
| ID_ | varchar | 64 | 否 | 是 | 任务ID |
| REV_ | int | 11 | 是 | | 版本号 |
| EXECUTION_ID_ | varchar | 64 | 是 | | 执行ID |
| PROC_INST_ID_ | varchar | 64 | 是 | | 流程实例ID |
| PROC_DEF_ID_ | varchar | 64 | 是 | | 流程定义ID |
| CASE_EXECUTION_ID_ | varchar | 64 | 是 | | 案例执行ID |
| CASE_INST_ID_ | varchar | 64 | 是 | | 案例实例ID |
| CASE_DEF_ID_ | varchar | 64 | 是 | | 案例定义ID |
| NAME_ | varchar | 255 | 是 | | 任务名称 |
| PARENT_TASK_ID_ | varchar | 64 | 是 | | 父任务ID |
| DESCRIPTION_ | varchar | 4000 | 是 | | 任务描述 |
| TASK_DEF_KEY_ | varchar | 255 | 是 | | 任务定义Key |
| OWNER_ | varchar | 255 | 是 | | 所有者 |
| ASSIGNEE_ | varchar | 255 | 是 | | 受理人 |
| DELEGATION_ | varchar | 64 | 是 | | 委派状态 |
| PRIORITY_ | int | 11 | 是 | | 优先级 |
| CREATE_TIME_ | timestamp | | 是 | | 创建时间 |
| DUE_DATE_ | datetime | | 是 | | 到期日期 |
| FOLLOW_UP_DATE_ | datetime | | 是 | | 跟进日期 |
| SUSPENSION_STATE_ | int | 11 | 是 | | 挂起状态 |
| TENANT_ID_ | varchar | 64 | 是 | | 租户ID |

#### 5. 流程变量表 ACT_RU_VARIABLE

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ---- |
| ID_ | varchar | 64 | 否 | 是 | 变量ID |
| REV_ | int | 11 | 是 | | 版本号 |
| TYPE_ | varchar | 255 | 否 | | 变量类型 |
| NAME_ | varchar | 255 | 否 | | 变量名称 |
| EXECUTION_ID_ | varchar | 64 | 是 | | 执行ID |
| PROC_INST_ID_ | varchar | 64 | 是 | | 流程实例ID |
| CASE_EXECUTION_ID_ | varchar | 64 | 是 | | 案例执行ID |
| CASE_INST_ID_ | varchar | 64 | 是 | | 案例实例ID |
| TASK_ID_ | varchar | 64 | 是 | | 任务ID |
| BYTEARRAY_ID_ | varchar | 64 | 是 | | 二进制数据ID |
| DOUBLE_ | double | | 是 | | 双精度值 |
| LONG_ | bigint | 20 | 是 | | 长整型值 |
| TEXT_ | varchar | 4000 | 是 | | 文本值 |
| TEXT2_ | varchar | 4000 | 是 | | 文本值2 |
| VAR_SCOPE_ | varchar | 64 | 是 | | 变量范围 |
| TENANT_ID_ | varchar | 64 | 是 | | 租户ID |

### 业务表单数据表

#### 1. 出差申请表 biz_travel

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 记录ID |
| business_key | varchar | 64 | 否 | | | 业务键(用于与Camunda流程关联) |
| instance_mapping_id | bigint | 20 | 否 | | | 流程实例映射ID |
| title | varchar | 200 | 否 | | | 标题 |
| travel_type | varchar | 50 | 否 | | | 出差类型 |
| start_time | datetime | | 否 | | | 开始时间 |
| end_time | datetime | | 否 | | | 结束时间 |
| days | decimal | 5,1 | 否 | | | 天数 |
| destination | varchar | 200 | 否 | | | 目的地 |
| travel_reason | varchar | 500 | 否 | | | 出差事由 |
| travel_members | varchar | 500 | 是 | | | 同行人员 |
| transport_type | varchar | 50 | 是 | | | 交通方式 |
| estimate_cost | decimal | 10,2 | 是 | | 0.00 | 预计费用 |
| attachments | varchar | 500 | 是 | | | 附件 |
| remarks | varchar | 500 | 是 | | | 备注 |
| process_status | varchar | 20 | 否 | | draft | 流程状态(draft/running/approved/rejected/canceled) |
| creator | bigint | 20 | 否 | | | 创建人 |
| create_time | datetime | | 否 | | CURRENT_TIMESTAMP | 创建时间 |
| updater | bigint | 20 | 是 | | | 更新人 |
| update_time | datetime | | 是 | | | 更新时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

#### 2. 请假申请表 biz_leave

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 记录ID |
| business_key | varchar | 64 | 否 | | | 业务键(用于与Camunda流程关联) |
| instance_mapping_id | bigint | 20 | 否 | | | 流程实例映射ID |
| title | varchar | 200 | 否 | | | 标题 |
| leave_type | varchar | 50 | 否 | | | 请假类型 |
| start_time | datetime | | 否 | | | 开始时间 |
| end_time | datetime | | 否 | | | 结束时间 |
| days | decimal | 5,1 | 否 | | | 天数 |
| leave_reason | varchar | 500 | 否 | | | 请假事由 |
| contact | varchar | 50 | 是 | | | 紧急联系方式 |
| handover_person | varchar | 50 | 是 | | | 工作交接人 |
| attachments | varchar | 500 | 是 | | | 附件 |
| remarks | varchar | 500 | 是 | | | 备注 |
| process_status | varchar | 20 | 否 | | draft | 流程状态(draft/running/approved/rejected/canceled) |
| creator | bigint | 20 | 否 | | | 创建人 |
| create_time | datetime | | 否 | | CURRENT_TIMESTAMP | 创建时间 |
| updater | bigint | 20 | 是 | | | 更新人 |
| update_time | datetime | | 是 | | | 更新时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

#### 3. 车辆申请表 biz_vehicle

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 记录ID |
| business_key | varchar | 64 | 否 | | | 业务键(用于与Camunda流程关联) |
| instance_mapping_id | bigint | 20 | 否 | | | 流程实例映射ID |
| title | varchar | 200 | 否 | | | 标题 |
| vehicle_type | varchar | 50 | 是 | | | 车辆类型 |
| start_time | datetime | | 否 | | | 用车开始时间 |
| end_time | datetime | | 否 | | | 用车结束时间 |
| destination | varchar | 200 | 否 | | | 目的地 |
| purpose | varchar | 500 | 否 | | | 用车事由 |
| passengers | varchar | 500 | 是 | | | 乘车人员 |
| driver_name | varchar | 50 | 是 | | | 司机姓名 |
| estimated_mileage | decimal | 10,2 | 是 | | 0.00 | 预计里程 |
| remarks | varchar | 500 | 是 | | | 备注 |
| process_status | varchar | 20 | 否 | | draft | 流程状态(draft/running/approved/rejected/canceled) |
| creator | bigint | 20 | 否 | | | 创建人 |
| create_time | datetime | | 否 | | CURRENT_TIMESTAMP | 创建时间 |
| updater | bigint | 20 | 是 | | | 更新人 |
| update_time | datetime | | 是 | | | 更新时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

#### 4. 会议申请表 biz_meeting

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 记录ID |
| business_key | varchar | 64 | 否 | | | 业务键(用于与Camunda流程关联) |
| instance_mapping_id | bigint | 20 | 否 | | | 流程实例映射ID |
| title | varchar | 200 | 否 | | | 标题 |
| meeting_type | varchar | 50 | 否 | | | 会议类型 |
| meeting_room | varchar | 100 | 否 | | | 会议室 |
| start_time | datetime | | 否 | | | 开始时间 |
| end_time | datetime | | 否 | | | 结束时间 |
| participants | varchar | 1000 | 否 | | | 参会人员 |
| meeting_theme | varchar | 200 | 否 | | | 会议主题 |
| meeting_content | varchar | 1000 | 是 | | | 会议内容 |
| device_requirements | varchar | 500 | 是 | | | 设备需求 |
| catering_requirements | varchar | 500 | 是 | | | 餐饮需求 |
| attachments | varchar | 500 | 是 | | | 附件 |
| remarks | varchar | 500 | 是 | | | 备注 |
| process_status | varchar | 20 | 否 | | draft | 流程状态(draft/running/approved/rejected/canceled) |
| creator | bigint | 20 | 否 | | | 创建人 |
| create_time | datetime | | 否 | | CURRENT_TIMESTAMP | 创建时间 |
| updater | bigint | 20 | 是 | | | 更新人 |
| update_time | datetime | | 是 | | | 更新时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

#### 5. 办公用品申请表 biz_office_supplies

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 记录ID |
| business_key | varchar | 64 | 否 | | | 业务键(用于与Camunda流程关联) |
| instance_mapping_id | bigint | 20 | 否 | | | 流程实例映射ID |
| title | varchar | 200 | 否 | | | 标题 |
| apply_date | date | | 否 | | | 申请日期 |
| purpose | varchar | 500 | 否 | | | 申请用途 |
| total_amount | decimal | 10,2 | 否 | | 0.00 | 总金额 |
| attachments | varchar | 500 | 是 | | | 附件 |
| remarks | varchar | 500 | 是 | | | 备注 |
| process_status | varchar | 20 | 否 | | draft | 流程状态(draft/running/approved/rejected/canceled) |
| creator | bigint | 20 | 否 | | | 创建人 |
| create_time | datetime | | 否 | | CURRENT_TIMESTAMP | 创建时间 |
| updater | bigint | 20 | 是 | | | 更新人 |
| update_time | datetime | | 是 | | | 更新时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

#### 6. 办公用品申请详情表 biz_office_supplies_item

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 记录ID |
| supplies_id | bigint | 20 | 否 | | | 办公用品申请ID |
| item_name | varchar | 100 | 否 | | | 物品名称 |
| specification | varchar | 100 | 是 | | | 规格型号 |
| unit | varchar | 20 | 否 | | | 单位 |
| quantity | int | 11 | 否 | | | 数量 |
| price | decimal | 10,2 | 否 | | 0.00 | 单价 |
| amount | decimal | 10,2 | 否 | | 0.00 | 金额 |
| remarks | varchar | 200 | 是 | | | 备注 |
| create_time | datetime | | 否 | | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | | 是 | | | 更新时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

### 系统管理表

#### 1. 数据字典表 sys_dict

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 字典ID |
| dict_name | varchar | 100 | 否 | | | 字典名称 |
| dict_type | varchar | 100 | 否 | | | 字典类型 |
| status | tinyint | 1 | 否 | | 1 | 状态(0禁用,1正常) |
| remark | varchar | 500 | 是 | | | 备注 |
| create_time | datetime | | 否 | | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | | 是 | | | 更新时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

#### 2. 字典数据表 sys_dict_data

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 字典数据ID |
| dict_type | varchar | 100 | 否 | | | 字典类型 |
| dict_label | varchar | 100 | 否 | | | 字典标签 |
| dict_value | varchar | 100 | 否 | | | 字典键值 |
| order_num | int | 11 | 否 | | 0 | 显示顺序 |
| status | tinyint | 1 | 否 | | 1 | 状态(0禁用,1正常) |
| remark | varchar | 500 | 是 | | | 备注 |
| create_time | datetime | | 否 | | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | | 是 | | | 更新时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

#### 3. 系统配置表 sys_config

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 配置ID |
| config_name | varchar | 100 | 否 | | | 配置名称 |
| config_key | varchar | 100 | 否 | | | 配置键名 |
| config_value | varchar | 500 | 否 | | | 配置键值 |
| config_type | tinyint | 1 | 否 | | 0 | 配置类型(0系统,1业务) |
| status | tinyint | 1 | 否 | | 1 | 状态(0禁用,1正常) |
| remark | varchar | 500 | 是 | | | 备注 |
| create_time | datetime | | 否 | | CURRENT_TIMESTAMP | 创建时间 |
| update_time | datetime | | 是 | | | 更新时间 |
| is_deleted | tinyint | 1 | 否 | | 0 | 是否删除(0否,1是) |

#### 4. 系统日志表 sys_log

| 字段名 | 数据类型 | 长度 | 允许空 | 主键 | 默认值 | 说明 |
| ----- | ------- | ---- | ----- | ---- | ----- | ---- |
| id | bigint | 20 | 否 | 是 | | 日志ID |
| user_id | bigint | 20 | 是 | | | 用户ID |
| module | varchar | 50 | 是 | | | 功能模块 |
| operation | varchar | 50 | 是 | | | 操作类型 |
| method | varchar | 100 | 是 | | | 方法名 |
| params | text | | 是 | | | 请求参数 |
| ip | varchar | 50 | 是 | | | IP地址 |
| status | tinyint | 1 | 否 | | 1 | 状态(0失败,1成功) |
| error_msg | varchar | 2000 | 是 | | | 错误消息 |
| operation_time | datetime | | 否 | | CURRENT_TIMESTAMP | 操作时间 |

## 数据库关系图

### 基础关系图

```
+-------------+      +------------+      +----------------+
|   sys_user  |<-----|sys_user_role|----->|    sys_role    |
+-------------+      +------------+      +----------------+
      |                                          |
      |                                          |
      v                                          v
+-------------+                           +----------------+
|   sys_dept  |                           |  sys_role_menu |
+-------------+                           +----------------+
                                                 |
                                                 |
                                                 v
                                          +----------------+
                                          |    sys_menu    |
                                          +----------------+
```

### 流程引擎与业务集成关系图

```
+-------------------+         +-----------------------+
|  wf_process_mapping|<------->| ACT_RE_PROCDEF       |
+-------------------+         +-----------------------+
        |
        |
        v
+-------------------+         +-----------------------+
| wf_instance_mapping|<------->| ACT_RU_EXECUTION     |
+-------------------+         +-----------------------+
        |                             |
        |                             |
        v                             v
+-------------------+         +-----------------------+
|  wf_task_mapping  |<------->| ACT_RU_TASK          |
+-------------------+         +-----------------------+
        |                             |
        |                             |
        v                             v
+-------------------+         +-----------------------+
| wf_operation_log  |         | ACT_RU_VARIABLE      |
+-------------------+         +-----------------------+

```

### 业务表关系图

```
+-------------------+
| wf_instance_mapping|
+-------------------+
        |
        |
+-------+---------+
|                 |
v                 v                           v
+----------+  +----------+     +---------------+
|biz_travel|  |biz_leave |     |biz_office_sup.|
+----------+  +----------+     +---------------+
                                      |
                                      |
                                      v
                               +----------------+
                               |biz_office_sup_item|
                               +----------------+
```

## 索引设计

为了提高查询性能，需要对关键字段添加索引:

1. 所有主键字段使用自增长主键索引
2. 外键关联字段添加普通索引
3. 经常作为查询条件的字段添加索引
4. 唯一性字段添加唯一索引

### 主要索引清单

1. **系统基础表索引**
   - `sys_user` 表添加 `username` 字段的唯一索引
   - `sys_user` 表添加 `dept_id` 字段的普通索引
   - `sys_dept` 表添加 `parent_id` 字段的普通索引
   - `sys_menu` 表添加 `parent_id` 字段的普通索引

2. **流程相关表索引**
   - `wf_process_mapping` 表添加 `process_code` 字段的唯一索引
   - `wf_process_mapping` 表添加 `camunda_process_key` 字段的普通索引
   - `wf_instance_mapping` 表添加 `business_key` 字段的唯一索引
   - `wf_instance_mapping` 表添加 `camunda_process_instance_id` 字段的唯一索引
   - `wf_instance_mapping` 表添加 `applicant_id` 和 `status` 字段的组合索引
   - `wf_task_mapping` 表添加 `camunda_task_id` 字段的唯一索引
   - `wf_task_mapping` 表添加 `instance_mapping_id` 字段的普通索引
   - `wf_task_mapping` 表添加 `assignee_id` 和 `status` 字段的组合索引

3. **业务表索引**
   - 所有业务表添加 `business_key` 字段的唯一索引
   - 所有业务表添加 `instance_mapping_id` 字段的普通索引
   - 所有业务表添加 `creator` 和 `process_status` 字段的组合索引

## 分库分表策略

由于系统采用微服务架构，各服务可以使用独立的数据库。同时，对于数据量大的表，可以采用分表策略：

1. **水平分表**：
   - 历史数据表（如 `wf_operation_log`）可按时间范围分表，如按年或按季度
   - Camunda历史表可按时间范围分表

2. **垂直分表**：
   - 将不常用的大字段（如BPMN XML、表单内容）单独存储

## 数据库优化策略

1. **SQL优化**：
   - 使用合适的索引提高查询效率
   - 避免使用SELECT *，只查询需要的字段
   - 大数据量查询使用分页

2. **表结构优化**：
   - 合理选择字段类型和长度，节省存储空间
   - 使用适当的存储引擎（InnoDB）
   - 大字段使用单独的表存储

3. **分库分表与读写分离**：
   - 按微服务边界进行分库
   - 历史数据与实时数据分离
   - 读多写少的场景实施读写分离

4. **缓存策略**：
   - 热点数据使用Redis缓存
   - 流程定义信息缓存
   - 用户权限信息缓存

## Camunda与业务数据集成策略

1. **使用业务键关联**：
   - 业务表主键作为流程实例的业务键(BusinessKey)
   - 通过业务键快速关联业务数据和流程数据

2. **映射表管理**：
   - 使用映射表（如 `wf_instance_mapping`）管理业务数据与Camunda流程的对应关系
   - 减少对Camunda表的直接查询，提高性能

3. **流程变量存储策略**：
   - 关键业务数据作为流程变量存储，用于流程条件判断
   - 大量业务数据存储在业务表中，不作为流程变量
   - 使用JSON序列化复杂对象的流程变量 