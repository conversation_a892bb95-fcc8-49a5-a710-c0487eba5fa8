/**
 * HTTP客户端
 * 提供统一的HTTP请求功能
 */

import axios from "axios";
import type {
  AxiosInstance,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from "axios";

// 注意：在 axios 1.8.x 中，AxiosRequestConfig 类型已更改为 InternalAxiosRequestConfig
import { setupInterceptors } from "./interceptors";
import { API_CONFIG } from "../config";

/**
 * HTTP客户端类
 * 封装Axios，提供统一的请求方法
 */
export class HttpClient {
  private instance: AxiosInstance;

  /**
   * 构造函数
   * @param config Axios配置
   */
  constructor(config: Partial<InternalAxiosRequestConfig> = {}) {
    // 创建axios实例
    this.instance = axios.create({
      baseURL: API_CONFIG.baseURL,
      timeout: API_CONFIG.timeout,
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      ...config,
    });

    // 设置拦截器
    setupInterceptors(this.instance);
  }

  /**
   * 发送GET请求
   * @param url 请求URL
   * @param params 请求参数
   * @param config 请求配置
   * @returns Promise<T>
   */
  async get<T = any>(
    url: string,
    params?: any,
    config?: Partial<InternalAxiosRequestConfig>
  ): Promise<T> {
    try {
      const response = await this.instance.get<T>(url, { ...config, params });
      return this.handleResponse<T>(response);
    } catch (error) {
      throw error;
    }
  }

  /**
   * 发送POST请求
   * @param url 请求URL
   * @param data 请求数据
   * @param config 请求配置
   * @returns Promise<T>
   */
  async post<T = any>(
    url: string,
    data?: any,
    config?: Partial<InternalAxiosRequestConfig>
  ): Promise<T> {
    try {
      const response = await this.instance.post<T>(url, data, config);
      return this.handleResponse<T>(response);
    } catch (error) {
      throw error;
    }
  }

  /**
   * 发送PUT请求
   * @param url 请求URL
   * @param data 请求数据
   * @param config 请求配置
   * @returns Promise<T>
   */
  async put<T = any>(
    url: string,
    data?: any,
    config?: Partial<InternalAxiosRequestConfig>
  ): Promise<T> {
    try {
      const response = await this.instance.put<T>(url, data, config);
      return this.handleResponse<T>(response);
    } catch (error) {
      throw error;
    }
  }

  /**
   * 发送DELETE请求
   * @param url 请求URL
   * @param config 请求配置
   * @returns Promise<T>
   */
  async delete<T = any>(
    url: string,
    config?: Partial<InternalAxiosRequestConfig>
  ): Promise<T> {
    try {
      const response = await this.instance.delete<T>(url, config);
      return this.handleResponse<T>(response);
    } catch (error) {
      throw error;
    }
  }

  /**
   * 发送PATCH请求
   * @param url 请求URL
   * @param data 请求数据
   * @param config 请求配置
   * @returns Promise<T>
   */
  async patch<T = any>(
    url: string,
    data?: any,
    config?: Partial<InternalAxiosRequestConfig>
  ): Promise<T> {
    try {
      const response = await this.instance.patch<T>(url, data, config);
      return this.handleResponse<T>(response);
    } catch (error) {
      throw error;
    }
  }

  /**
   * 处理响应
   * @param response Axios响应
   * @returns 响应数据
   */
  private handleResponse<T>(response: AxiosResponse<any>): T {
    // 如果响应是标准格式，返回data字段
    if (
      response.data &&
      typeof response.data === "object" &&
      "data" in response.data
    ) {
      return response.data.data;
    }

    // 否则返回整个响应数据
    return response.data;
  }

  /**
   * 获取Axios实例
   * @returns Axios实例
   */
  getInstance(): AxiosInstance {
    return this.instance;
  }
}

// 创建默认HTTP客户端实例
export const http = new HttpClient();

// 导出便捷方法
export const get = http.get.bind(http);
export const post = http.post.bind(http);
export const put = http.put.bind(http);
export const del = http.delete.bind(http);
export const patch = http.patch.bind(http);

export default http;
