<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_2" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.0.0" modeler:executionPlatform="Camunda Cloud" modeler:executionPlatformVersion="8.0.0">
  <bpmn:process id="vehicle-process" name="教育局用车申请流程" isExecutable="true">
    <bpmn:startEvent id="start_event" name="开始">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>

    <bpmn:sequenceFlow id="Flow_1" sourceRef="start_event" targetRef="check_vehicle_type" />

    <bpmn:exclusiveGateway id="check_vehicle_type" name="检查用车类型">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <bpmn:sequenceFlow id="Flow_2" name="公务用车" sourceRef="check_vehicle_type" targetRef="dept_leader_approve">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= vehicleType = "official"</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="Flow_3" name="应急用车" sourceRef="check_vehicle_type" targetRef="emergency_check">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= vehicleType = "emergency"</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:userTask id="dept_leader_approve" name="部门负责人审批">
      <bpmn:extensionElements>
        <zeebe:assignmentDefinition assignee="#{assignee}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
    </bpmn:userTask>

    <bpmn:sequenceFlow id="Flow_4" sourceRef="dept_leader_approve" targetRef="gateway_1" />

    <bpmn:exclusiveGateway id="gateway_1" name="部门审批结果">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
      <bpmn:outgoing>Flow_reject_1</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <bpmn:sequenceFlow id="Flow_5" name="通过" sourceRef="gateway_1" targetRef="vehicle_admin_approve">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= deptApproved = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="Flow_reject_1" name="不通过" sourceRef="gateway_1" targetRef="end_event_fail">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= deptApproved = false</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:userTask id="emergency_check" name="应急情况审核">
      <bpmn:extensionElements>
        <zeebe:assignmentDefinition assignee="#{assignee}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>

    <bpmn:sequenceFlow id="Flow_6" sourceRef="emergency_check" targetRef="gateway_2" />

    <bpmn:exclusiveGateway id="gateway_2" name="应急审核结果">
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_7</bpmn:outgoing>
      <bpmn:outgoing>Flow_reject_2</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <bpmn:sequenceFlow id="Flow_7" name="通过" sourceRef="gateway_2" targetRef="vehicle_admin_approve">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= emergencyApproved = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="Flow_reject_2" name="不通过" sourceRef="gateway_2" targetRef="end_event_fail">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= emergencyApproved = false</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:userTask id="vehicle_admin_approve" name="车辆管理员审批">
      <bpmn:extensionElements>
        <zeebe:assignmentDefinition assignee="#{assignee}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:incoming>Flow_7</bpmn:incoming>
      <bpmn:outgoing>Flow_8</bpmn:outgoing>
    </bpmn:userTask>

    <bpmn:sequenceFlow id="Flow_8" sourceRef="vehicle_admin_approve" targetRef="gateway_3" />

    <bpmn:exclusiveGateway id="gateway_3" name="车辆审批结果">
      <bpmn:incoming>Flow_8</bpmn:incoming>
      <bpmn:outgoing>Flow_9</bpmn:outgoing>
      <bpmn:outgoing>Flow_reject_3</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <bpmn:sequenceFlow id="Flow_9" name="通过" sourceRef="gateway_3" targetRef="end_event_success">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= vehicleAdminApproved = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="Flow_reject_3" name="不通过" sourceRef="gateway_3" targetRef="end_event_fail">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= vehicleAdminApproved = false</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:endEvent id="end_event_success" name="申请通过">
      <bpmn:incoming>Flow_9</bpmn:incoming>
    </bpmn:endEvent>

    <bpmn:endEvent id="end_event_fail" name="申请未通过">
      <bpmn:incoming>Flow_reject_1</bpmn:incoming>
      <bpmn:incoming>Flow_reject_2</bpmn:incoming>
      <bpmn:incoming>Flow_reject_3</bpmn:incoming>
    </bpmn:endEvent>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="vehicle-process">
      <bpmndi:BPMNShape id="Event_1" bpmnElement="start_event">
        <dc:Bounds x="152" y="232" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="275" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1" bpmnElement="check_vehicle_type" isMarkerVisible="true">
        <dc:Bounds x="245" y="225" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="235" y="195" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1" bpmnElement="dept_leader_approve">
        <dc:Bounds x="360" y="145" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_2" bpmnElement="gateway_1" isMarkerVisible="true">
        <dc:Bounds x="525" y="160" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="515" y="130" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_2" bpmnElement="emergency_check">
        <dc:Bounds x="360" y="305" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_3" bpmnElement="gateway_2" isMarkerVisible="true">
        <dc:Bounds x="525" y="320" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="515" y="290" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_3" bpmnElement="vehicle_admin_approve">
        <dc:Bounds x="640" y="225" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_4" bpmnElement="gateway_3" isMarkerVisible="true">
        <dc:Bounds x="805" y="240" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="795" y="210" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_2" bpmnElement="end_event_success">
        <dc:Bounds x="922" y="247" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="915" y="290" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_3" bpmnElement="end_event_fail">
        <dc:Bounds x="922" y="157" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="909" y="200" width="62" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1_di" bpmnElement="Flow_1">
        <di:waypoint x="188" y="250" />
        <di:waypoint x="245" y="250" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_2_di" bpmnElement="Flow_2">
        <di:waypoint x="270" y="225" />
        <di:waypoint x="270" y="185" />
        <di:waypoint x="360" y="185" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="272" y="168" width="46" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_3_di" bpmnElement="Flow_3">
        <di:waypoint x="270" y="275" />
        <di:waypoint x="270" y="345" />
        <di:waypoint x="360" y="345" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="272" y="328" width="46" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_4_di" bpmnElement="Flow_4">
        <di:waypoint x="460" y="185" />
        <di:waypoint x="525" y="185" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_5_di" bpmnElement="Flow_5">
        <di:waypoint x="550" y="210" />
        <di:waypoint x="550" y="265" />
        <di:waypoint x="640" y="265" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="555" y="248" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_reject_1_di" bpmnElement="Flow_reject_1">
        <di:waypoint x="550" y="160" />
        <di:waypoint x="550" y="175" />
        <di:waypoint x="922" y="175" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="545" y="158" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_6_di" bpmnElement="Flow_6">
        <di:waypoint x="460" y="345" />
        <di:waypoint x="525" y="345" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_7_di" bpmnElement="Flow_7">
        <di:waypoint x="550" y="320" />
        <di:waypoint x="550" y="265" />
        <di:waypoint x="640" y="265" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="555" y="248" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_reject_2_di" bpmnElement="Flow_reject_2">
        <di:waypoint x="550" y="370" />
        <di:waypoint x="550" y="385" />
        <di:waypoint x="922" y="385" />
        <di:waypoint x="922" y="193" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="545" y="368" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_8_di" bpmnElement="Flow_8">
        <di:waypoint x="740" y="265" />
        <di:waypoint x="805" y="265" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_9_di" bpmnElement="Flow_9">
        <di:waypoint x="855" y="265" />
        <di:waypoint x="922" y="265" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="878" y="247" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_reject_3_di" bpmnElement="Flow_reject_3">
        <di:waypoint x="830" y="240" />
        <di:waypoint x="830" y="175" />
        <di:waypoint x="922" y="175" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="825" y="205" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions> 