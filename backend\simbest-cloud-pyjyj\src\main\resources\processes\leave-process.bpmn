<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.0.0" modeler:executionPlatform="Camunda Cloud" modeler:executionPlatformVersion="8.0.0">
  <bpmn:process id="leave-process" name="教育局请假申请流程" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>

    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_1" targetRef="check_leave_type" />

    <!-- 检查请假类型网关 -->
    <bpmn:exclusiveGateway id="check_leave_type" name="检查请假类型">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <bpmn:sequenceFlow id="Flow_2" name="病假" sourceRef="check_leave_type" targetRef="check_sick_leave_days">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= leaveType = "sick"</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="Flow_3" name="事假" sourceRef="check_leave_type" targetRef="check_personal_leave_days">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= leaveType = "personal"</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <!-- 检查病假天数网关 -->
    <bpmn:exclusiveGateway id="check_sick_leave_days" name="检查病假天数">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <bpmn:sequenceFlow id="Flow_4" name="大于3天" sourceRef="check_sick_leave_days" targetRef="hr_review">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= leaveDays > 3</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="Flow_5" name="小于等于3天" sourceRef="check_sick_leave_days" targetRef="dept_leader_approve">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= leaveDays &lt;= 3</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <!-- 检查事假天数网关 -->
    <bpmn:exclusiveGateway id="check_personal_leave_days" name="检查事假天数">
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
      <bpmn:outgoing>Flow_7</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <bpmn:sequenceFlow id="Flow_6" name="大于1天" sourceRef="check_personal_leave_days" targetRef="hr_review">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= leaveDays > 1</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="Flow_7" name="小于等于1天" sourceRef="check_personal_leave_days" targetRef="dept_leader_approve">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= leaveDays &lt;= 1</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <!-- 人事审核（检查假期余额） -->
    <bpmn:userTask id="hr_review" name="人事审核">
      <bpmn:extensionElements>
        <zeebe:assignmentDefinition assignee="#{assignee}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_8</bpmn:outgoing>
    </bpmn:userTask>

    <bpmn:sequenceFlow id="Flow_8" sourceRef="hr_review" targetRef="gateway_1" />

    <bpmn:exclusiveGateway id="gateway_1" name="人事审核结果">
      <bpmn:incoming>Flow_8</bpmn:incoming>
      <bpmn:outgoing>Flow_9</bpmn:outgoing>
      <bpmn:outgoing>Flow_reject_1</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <bpmn:sequenceFlow id="Flow_9" name="通过" sourceRef="gateway_1" targetRef="dept_leader_approve">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= hrApproved = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="Flow_reject_1" name="不通过" sourceRef="gateway_1" targetRef="end_event_fail">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= hrApproved = false</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <!-- 部门负责人审批 -->
    <bpmn:userTask id="dept_leader_approve" name="部门负责人审批">
      <bpmn:extensionElements>
        <zeebe:assignmentDefinition assignee="#{assignee}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:incoming>Flow_7</bpmn:incoming>
      <bpmn:incoming>Flow_9</bpmn:incoming>
      <bpmn:outgoing>Flow_10</bpmn:outgoing>
    </bpmn:userTask>

    <bpmn:sequenceFlow id="Flow_10" sourceRef="dept_leader_approve" targetRef="gateway_2" />

    <bpmn:exclusiveGateway id="gateway_2" name="部门审批结果">
      <bpmn:incoming>Flow_10</bpmn:incoming>
      <bpmn:outgoing>Flow_11</bpmn:outgoing>
      <bpmn:outgoing>Flow_reject_2</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <bpmn:sequenceFlow id="Flow_11" name="通过" sourceRef="gateway_2" targetRef="end_event_success">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= deptApproved = true</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="Flow_reject_2" name="不通过" sourceRef="gateway_2" targetRef="end_event_fail">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">= deptApproved = false</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:endEvent id="end_event_success" name="申请通过">
      <bpmn:incoming>Flow_11</bpmn:incoming>
    </bpmn:endEvent>

    <bpmn:endEvent id="end_event_fail" name="申请未通过">
      <bpmn:incoming>Flow_reject_1</bpmn:incoming>
      <bpmn:incoming>Flow_reject_2</bpmn:incoming>
    </bpmn:endEvent>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="leave-process">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_1" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="232" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="275" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1" bpmnElement="check_leave_type" isMarkerVisible="true">
        <dc:Bounds x="245" y="225" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="235" y="195" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_2" bpmnElement="check_sick_leave_days" isMarkerVisible="true">
        <dc:Bounds x="355" y="145" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="345" y="115" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_3" bpmnElement="check_personal_leave_days" isMarkerVisible="true">
        <dc:Bounds x="355" y="305" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="345" y="275" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1" bpmnElement="hr_review">
        <dc:Bounds x="470" y="225" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_4" bpmnElement="gateway_1" isMarkerVisible="true">
        <dc:Bounds x="635" y="240" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="625" y="210" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_2" bpmnElement="dept_leader_approve">
        <dc:Bounds x="750" y="225" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_5" bpmnElement="gateway_2" isMarkerVisible="true">
        <dc:Bounds x="915" y="240" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="905" y="210" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_2" bpmnElement="end_event_success">
        <dc:Bounds x="1032" y="247" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1025" y="290" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_3" bpmnElement="end_event_fail">
        <dc:Bounds x="1032" y="157" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1019" y="200" width="62" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1_di" bpmnElement="Flow_1">
        <di:waypoint x="188" y="250" />
        <di:waypoint x="245" y="250" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_2_di" bpmnElement="Flow_2">
        <di:waypoint x="270" y="225" />
        <di:waypoint x="270" y="170" />
        <di:waypoint x="355" y="170" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="272" y="153" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_3_di" bpmnElement="Flow_3">
        <di:waypoint x="270" y="275" />
        <di:waypoint x="270" y="330" />
        <di:waypoint x="355" y="330" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="272" y="313" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_4_di" bpmnElement="Flow_4">
        <di:waypoint x="380" y="195" />
        <di:waypoint x="380" y="265" />
        <di:waypoint x="470" y="265" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="375" y="248" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_5_di" bpmnElement="Flow_5">
        <di:waypoint x="405" y="170" />
        <di:waypoint x="800" y="170" />
        <di:waypoint x="800" y="225" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="572" y="152" width="60" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_6_di" bpmnElement="Flow_6">
        <di:waypoint x="380" y="355" />
        <di:waypoint x="380" y="265" />
        <di:waypoint x="470" y="265" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="375" y="328" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_7_di" bpmnElement="Flow_7">
        <di:waypoint x="405" y="330" />
        <di:waypoint x="800" y="330" />
        <di:waypoint x="800" y="305" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="572" y="312" width="60" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_8_di" bpmnElement="Flow_8">
        <di:waypoint x="570" y="265" />
        <di:waypoint x="635" y="265" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_9_di" bpmnElement="Flow_9">
        <di:waypoint x="685" y="265" />
        <di:waypoint x="750" y="265" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="708" y="247" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_reject_1_di" bpmnElement="Flow_reject_1">
        <di:waypoint x="660" y="240" />
        <di:waypoint x="660" y="175" />
        <di:waypoint x="1032" y="175" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="655" y="205" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10_di" bpmnElement="Flow_10">
        <di:waypoint x="850" y="265" />
        <di:waypoint x="915" y="265" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11_di" bpmnElement="Flow_11">
        <di:waypoint x="965" y="265" />
        <di:waypoint x="1032" y="265" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="988" y="247" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_reject_2_di" bpmnElement="Flow_reject_2">
        <di:waypoint x="940" y="240" />
        <di:waypoint x="940" y="175" />
        <di:waypoint x="1032" y="175" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="935" y="205" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>