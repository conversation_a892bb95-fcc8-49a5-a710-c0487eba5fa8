# 7-文件与附件及数据字典接口规范

## 一、系统字典相关接口

### 1. SysDict 服务接口
**接口类**: `ISysDictService`
**实现类**: `SysDictService`
**主要功能**: 提供系统字典的基础管理功能

主要方法:
- `findByDictType(String dictType)`: 根据字典类型查询字典定义
- `findAllDictMapList()`: 查询所有字典定义，返回Map结构
- `findDictMapList(String[] typeList)`: 根据字典类型列表查询字典定义

### 2. SysDictValue 服务接口
**接口类**: `ISysDictValueService`  
**实现类**: `SysDictValueService`
**主要功能**: 提供系统字典值的管理功能

主要方法:
- `findAllDictValueMapList()`: 查询所有字典值，返回Map结构
- `findDictValueMapList(String[] typeList)`: 根据字典类型列表查询字典值
- `findByDictTypeAndName(String dictType, String name)`: 根据字典类型和名称查询字典值

### 3. SysDict 控制器
**控制器类**: `SysDictController`
**主要功能**: 提供系统字典的Web接口

主要接口:
- `POST /findAllDictMapList`: 查询所有字典定义
- `POST /findDictMapList`: 根据字典类型列表查询字典定义

### 4. SysDictValue 控制器
**控制器类**: `SysDictValueController`
**主要功能**: 提供系统字典值的Web接口

主要接口:
- `POST /findAllDictValueMapList`: 查询所有字典值
- `POST /findDictValueMapList`: 根据字典类型列表查询字典值

## 二、系统文件相关接口

### 1. SysFile 服务接口
**接口类**: `ISysFileService`
**实现类**: `SysFileService`
**主要功能**: 提供系统文件的管理功能

主要方法:
- `upload()`: 文件上传
- `download()`: 文件下载
- `delete()`: 文件删除
- `findById()`: 根据ID查询文件信息

### 2. SysFile 控制器
**控制器类**: `SysFileController`
**主要功能**: 提供系统文件的Web接口

主要接口:
- `POST /upload`: 文件上传
- `GET /download`: 文件下载
- `DELETE /delete`: 文件删除
- `GET /findById`: 根据ID查询文件信息

## 三、继承关系说明

1. 所有Service接口都继承自`ILogicService`
2. 所有Service实现类都继承自`LogicService`
3. 所有Controller都继承自`LogicController`

## 四、注意事项

1. 字典相关接口支持公共字典和私有字典的管理
2. 文件相关接口支持多种存储方式（本地、FTP、SFTP等）
3. 所有接口都支持SSO和API双重访问方式
4. 缓存工具类使用Redis实现，支持多级缓存策略