/**
 * API配置
 * 提供API相关的配置
 */

// 环境变量
const isDev = import.meta.env.DEV;

// API配置
export const API_CONFIG = {
  // 基础URL
  // baseURL: import.meta.env.VITE_APP_CODE,
  baseURL: isDev
    ? import.meta.env.VITE_APP_CODE
    : import.meta.env.VITE_APP_CODE,

  // 超时时间（毫秒）
  timeout: 15000,

  // 是否启用日志
  enableLogs: isDev,

  // 是否启用缓存
  enableCache: true,

  // 缓存有效期（毫秒）
  cacheMaxAge: 5 * 60 * 1000,

  // 是否自动刷新令牌
  autoRefreshToken: true,
};

// 导出默认配置
export default API_CONFIG;
