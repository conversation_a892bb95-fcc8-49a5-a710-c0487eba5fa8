import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import axios from 'axios'
import { performance } from 'perf_hooks'

/**
 * API性能测试
 * 此测试旨在测量API响应时间，并与设定的性能基准进行比较
 */
describe('API性能测试', () => {
  // 设置axios基础URL
  const BASE_URL = process.env.API_BASE_URL || 'http://localhost:8000'
  let token: string | null = null
  
  // 性能基准（毫秒）
  const PERFORMANCE_BENCHMARKS = {
    LOGIN: 500,      // 登录响应时间
    GET_PRODUCTS: 300, // 获取产品列表
    SEARCH: 400,     // 搜索功能
    GET_REPORTS: 1000 // 获取报表数据
  }
  
  beforeEach(async () => {
    // 登录获取令牌
    if (!token) {
      try {
        const response = await axios.post(`${BASE_URL}/api/auth/login`, {
          username: 'test_user',
          password: 'password'
        })
        token = response.data.access_token
      } catch (error) {
        console.warn('无法登录获取令牌，性能测试将使用模拟数据')
      }
    }
  })
  
  afterEach(() => {
    // 每次测试后暂停一下，避免连续请求
    return new Promise(resolve => setTimeout(resolve, 100))
  })
  
  // 测量API响应时间的辅助函数
  async function measureApiPerformance(
    apiCall: () => Promise<any>,
    benchmark: number,
    retries = 3
  ): Promise<number> {
    let totalTime = 0
    let successfulCalls = 0
    
    for (let i = 0; i < retries; i++) {
      try {
        const start = performance.now()
        await apiCall()
        const end = performance.now()
        
        const callTime = end - start
        totalTime += callTime
        successfulCalls++
        
        // 添加一些调试信息
        console.log(`API调用 #${i+1}: ${callTime.toFixed(2)}ms`)
      } catch (error) {
        console.error(`API调用失败 #${i+1}:`, error)
      }
      
      // 延迟一下再进行下一次调用
      if (i < retries - 1) {
        await new Promise(resolve => setTimeout(resolve, 200))
      }
    }
    
    if (successfulCalls === 0) {
      throw new Error('所有API调用都失败了')
    }
    
    // 计算平均响应时间
    return totalTime / successfulCalls
  }
  
  it('登录API响应时间应该低于基准', async () => {
    const loginTime = await measureApiPerformance(
      () => axios.post(`${BASE_URL}/api/auth/login`, {
        username: 'test_user',
        password: 'password'
      }),
      PERFORMANCE_BENCHMARKS.LOGIN
    )
    
    console.log(`登录平均响应时间: ${loginTime.toFixed(2)}ms, 基准: ${PERFORMANCE_BENCHMARKS.LOGIN}ms`)
    expect(loginTime).toBeLessThanOrEqual(PERFORMANCE_BENCHMARKS.LOGIN * 1.2) // 允许20%的浮动
  })
  
  it('获取产品列表API响应时间应该低于基准', async () => {
    // 跳过测试如果没有令牌
    if (!token) {
      console.warn('跳过产品列表测试: 没有有效令牌')
      return
    }
    
    const productsTime = await measureApiPerformance(
      () => axios.get(`${BASE_URL}/api/v1/products`, {
        headers: { Authorization: `Bearer ${token}` },
        params: { limit: 20, offset: 0 }
      }),
      PERFORMANCE_BENCHMARKS.GET_PRODUCTS
    )
    
    console.log(`获取产品列表平均响应时间: ${productsTime.toFixed(2)}ms, 基准: ${PERFORMANCE_BENCHMARKS.GET_PRODUCTS}ms`)
    expect(productsTime).toBeLessThanOrEqual(PERFORMANCE_BENCHMARKS.GET_PRODUCTS * 1.2)
  })
  
  it('搜索API响应时间应该低于基准', async () => {
    // 跳过测试如果没有令牌
    if (!token) {
      console.warn('跳过搜索测试: 没有有效令牌')
      return
    }
    
    const searchTime = await measureApiPerformance(
      () => axios.get(`${BASE_URL}/api/v1/products/search`, {
        headers: { Authorization: `Bearer ${token}` },
        params: { query: 'test' }
      }),
      PERFORMANCE_BENCHMARKS.SEARCH
    )
    
    console.log(`搜索API平均响应时间: ${searchTime.toFixed(2)}ms, 基准: ${PERFORMANCE_BENCHMARKS.SEARCH}ms`)
    expect(searchTime).toBeLessThanOrEqual(PERFORMANCE_BENCHMARKS.SEARCH * 1.2)
  })
  
  it('获取报表API响应时间应该低于基准', async () => {
    // 跳过测试如果没有令牌
    if (!token) {
      console.warn('跳过报表测试: 没有有效令牌')
      return
    }
    
    const reportTime = await measureApiPerformance(
      () => axios.get(`${BASE_URL}/api/v1/reports/sales/monthly`, {
        headers: { Authorization: `Bearer ${token}` }
      }),
      PERFORMANCE_BENCHMARKS.GET_REPORTS
    )
    
    console.log(`获取报表平均响应时间: ${reportTime.toFixed(2)}ms, 基准: ${PERFORMANCE_BENCHMARKS.GET_REPORTS}ms`)
    expect(reportTime).toBeLessThanOrEqual(PERFORMANCE_BENCHMARKS.GET_REPORTS * 1.2)
  })
  
  it('批量测试多个API端点响应时间', async () => {
    // 跳过测试如果没有令牌
    if (!token) {
      console.warn('跳过批量API测试: 没有有效令牌')
      return
    }
    
    // 待测试的API端点
    const endpoints = [
      { url: '/api/v1/users/me', name: '获取当前用户', benchmark: 200 },
      { url: '/api/v1/products/categories', name: '获取产品分类', benchmark: 250 },
      { url: '/api/v1/sales/statistics', name: '获取销售统计', benchmark: 600 }
    ]
    
    // 测试每个端点
    for (const endpoint of endpoints) {
      const responseTime = await measureApiPerformance(
        () => axios.get(`${BASE_URL}${endpoint.url}`, {
          headers: { Authorization: `Bearer ${token}` }
        }),
        endpoint.benchmark
      )
      
      console.log(`${endpoint.name}平均响应时间: ${responseTime.toFixed(2)}ms, 基准: ${endpoint.benchmark}ms`)
      expect(responseTime).toBeLessThanOrEqual(endpoint.benchmark * 1.2)
    }
  })
}) 