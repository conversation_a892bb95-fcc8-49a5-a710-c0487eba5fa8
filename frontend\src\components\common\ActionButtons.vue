<template>
  <div class="action-buttons">
    <!-- 查看按钮 -->
    <el-button
      v-if="showView"
      :type="viewType"
      :size="size"
      :link="viewLink"
      :disabled="viewDisabled"
      @click="handleView"
    >
      <el-icon v-if="viewIcon"><component :is="viewIcon" /></el-icon>
      {{ viewText }}
    </el-button>
    
    <!-- 编辑按钮 -->
    <el-button
      v-if="showEdit"
      :type="editType"
      :size="size"
      :link="editLink"
      :disabled="editDisabled"
      @click="handleEdit"
    >
      <el-icon v-if="editIcon"><component :is="editIcon" /></el-icon>
      {{ editText }}
    </el-button>
    
    <!-- 删除按钮 -->
    <el-button
      v-if="showDelete"
      :type="deleteType"
      :size="size"
      :link="deleteLink"
      :disabled="deleteDisabled"
      @click="handleDelete"
    >
      <el-icon v-if="deleteIcon"><component :is="deleteIcon" /></el-icon>
      {{ deleteText }}
    </el-button>
    
    <!-- 自定义按钮 -->
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { Delete, Edit, View } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'

const props = defineProps({
  /**
   * 行数据
   */
  row: {
    type: Object,
    required: true
  },
  /**
   * 行索引
   */
  index: {
    type: Number,
    default: 0
  },
  /**
   * 按钮大小
   */
  size: {
    type: String as () => 'large' | 'default' | 'small',
    default: 'small'
  },
  /**
   * 是否显示查看按钮
   */
  showView: {
    type: Boolean,
    default: true
  },
  /**
   * 查看按钮文本
   */
  viewText: {
    type: String,
    default: '查看'
  },
  /**
   * 查看按钮类型
   */
  viewType: {
    type: String,
    default: 'primary'
  },
  /**
   * 查看按钮图标
   */
  viewIcon: {
    type: String,
    default: ''
  },
  /**
   * 查看按钮是否为链接按钮
   */
  viewLink: {
    type: Boolean,
    default: true
  },
  /**
   * 查看按钮是否禁用
   */
  viewDisabled: {
    type: Boolean,
    default: false
  },
  /**
   * 是否显示编辑按钮
   */
  showEdit: {
    type: Boolean,
    default: true
  },
  /**
   * 编辑按钮文本
   */
  editText: {
    type: String,
    default: '编辑'
  },
  /**
   * 编辑按钮类型
   */
  editType: {
    type: String,
    default: 'primary'
  },
  /**
   * 编辑按钮图标
   */
  editIcon: {
    type: String,
    default: ''
  },
  /**
   * 编辑按钮是否为链接按钮
   */
  editLink: {
    type: Boolean,
    default: true
  },
  /**
   * 编辑按钮是否禁用
   */
  editDisabled: {
    type: Boolean,
    default: false
  },
  /**
   * 是否显示删除按钮
   */
  showDelete: {
    type: Boolean,
    default: true
  },
  /**
   * 删除按钮文本
   */
  deleteText: {
    type: String,
    default: '删除'
  },
  /**
   * 删除按钮类型
   */
  deleteType: {
    type: String,
    default: 'danger'
  },
  /**
   * 删除按钮图标
   */
  deleteIcon: {
    type: String,
    default: ''
  },
  /**
   * 删除按钮是否为链接按钮
   */
  deleteLink: {
    type: Boolean,
    default: true
  },
  /**
   * 删除按钮是否禁用
   */
  deleteDisabled: {
    type: Boolean,
    default: false
  },
  /**
   * 删除确认标题
   */
  deleteConfirmTitle: {
    type: String,
    default: '确认删除'
  },
  /**
   * 删除确认消息
   */
  deleteConfirmMessage: {
    type: String,
    default: '确定要删除这条记录吗？'
  },
  /**
   * 是否显示删除确认对话框
   */
  showDeleteConfirm: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['view', 'edit', 'delete'])

// 查看
const handleView = () => {
  emit('view', props.row, props.index)
}

// 编辑
const handleEdit = () => {
  emit('edit', props.row, props.index)
}

// 删除
const handleDelete = () => {
  if (props.showDeleteConfirm) {
    ElMessageBox.confirm(
      props.deleteConfirmMessage,
      props.deleteConfirmTitle,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
      .then(() => {
        emit('delete', props.row, props.index)
      })
      .catch(() => {
        // 用户取消删除
      })
  } else {
    emit('delete', props.row, props.index)
  }
}
</script>

<style scoped>
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
</style>
