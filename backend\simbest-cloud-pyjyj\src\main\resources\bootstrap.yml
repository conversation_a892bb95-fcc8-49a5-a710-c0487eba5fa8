## Configuration loading order by : Project Config > extension-configs > shared-configs
## 配置加载顺序 : 项目DataID主配值 > 扩展配置 > 共享配置
logback:
  groupId: com.simbest.cloud
  artifactId: ${logback.artifactId}

spring:
  profiles:
    active: ${profileActive}
  main:
    allow-bean-definition-overriding: true
  application:
    name: ${logback.artifactId}
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_ADDR}
        username: ${NACOS_USERNAME}
        password: ${NACOS_PASSWORD}
        group: DEFAULT_GROUP
        namespace: ${NACOS_NAMESPACE}
      config:
        server-addr: ${NACOS_ADDR}
        username: ${NACOS_USERNAME}
        password: ${NACOS_PASSWORD}
        group: DEFAULT_GROUP
        namespace: ${NACOS_NAMESPACE}
        file-extension: properties
        refresh-enabled: true
        extension-configs[0]:
          data-id: ${logback.artifactId}-custom.properties
          group: DEFAULT_GROUP
          refresh: true
        shared-configs[0]:
          data-id: commons.properties
          group: DEFAULT_GROUP
          refresh: true
