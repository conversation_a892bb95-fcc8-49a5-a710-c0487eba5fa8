/**
 * HTTP错误处理工具函数
 */
import { AxiosError } from 'axios'
import { ErrorResponse } from '../api'

// 定义HTTP错误类型
export type HttpError = AxiosError<ErrorResponse>

/**
 * 检查错误是否为401未授权错误
 * @param error - Axios错误对象
 * @returns 是否为未授权错误
 */
export function unauthorized(error: HttpError | unknown): boolean {
  return (
    error &&
    (error as HttpError).response &&
    (error as HttpError).response?.status === 401
  )
}

/**
 * 检查错误是否为403禁止访问错误
 * @param error - Axios错误对象
 * @returns 是否为禁止访问错误
 */
export function forbidden(error: HttpError | unknown): boolean {
  return (
    error &&
    (error as HttpError).response &&
    (error as HttpError).response?.status === 403
  )
}

/**
 * 检查错误是否为404资源未找到错误
 * @param error - Axios错误对象
 * @returns 是否为资源未找到错误
 */
export function notFound(error: HttpError | unknown): boolean {
  return (
    error &&
    (error as HttpError).response &&
    (error as HttpError).response?.status === 404
  )
}

/**
 * 检查错误是否为服务器错误（500系列）
 * @param error - Axios错误对象
 * @returns 是否为服务器错误
 */
export function serverError(error: HttpError | unknown): boolean {
  return (
    error &&
    (error as HttpError).response &&
    (error as HttpError).response?.status >= 500 &&
    (error as HttpError).response?.status < 600
  )
}

/**
 * 获取错误的友好消息
 * @param error - Axios错误对象
 * @returns 用户友好的错误消息
 */
export function getErrorMessage(error: HttpError | unknown): string {
  if (!error) {
    return '发生未知错误'
  }

  const axiosError = error as HttpError

  // 检查错误响应结构
  if (axiosError.response) {
    // 服务器返回了错误响应
    const { status, data } = axiosError.response

    // 尝试从响应数据中提取错误消息
    if (data) {
      if (typeof data === 'string') {
        return data
      }

      if (data.detail) {
        return data.detail
      }

      if (data.message) {
        return data.message
      }

      if (data.error) {
        return data.error
      }
    }

    // 根据状态码返回默认错误消息
    switch (status) {
      case 400:
        return '请求参数有误'
      case 401:
        return '请先登录或重新登录'
      case 403:
        return '您没有权限执行此操作'
      case 404:
        return '请求的资源不存在'
      case 405:
        return '不支持的请求方法'
      case 408:
        return '请求超时，请稍后重试'
      case 409:
        return '数据冲突'
      case 429:
        return '请求频率过高，请稍后再试'
      case 500:
        return '服务器内部错误'
      case 502:
        return '网关错误'
      case 503:
        return '服务暂时不可用'
      case 504:
        return '网关超时'
      default:
        return `请求失败 (${status})`
    }
  }

  // 请求被发送但没有收到响应
  if (axiosError.request) {
    return '无法连接到服务器，请检查网络连接'
  }

  // 发送请求时出错
  if (axiosError.message) {
    return axiosError.message
  }

  return '发生未知错误'
}