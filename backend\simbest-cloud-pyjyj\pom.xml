<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.simbest.cloud</groupId>
        <artifactId>simbest-cloud-parent</artifactId>
        <version>1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.simbest.cloud</groupId>
    <artifactId>pyjyj</artifactId>
    <description>SpringBoot3与SpringCloud2023濮阳教育局OA</description>

    <properties>
        <maven.compiler.level>17</maven.compiler.level>
        <simbest.boot.version>0.3</simbest.boot.version>
        <simbest.cloud.version>1.0</simbest.cloud.version>
        <logback.groupId>${project.groupId}</logback.groupId>
        <logback.artifactId>${project.artifactId}</logback.artifactId>
    </properties>

    <profiles>
        <profile>
            <id>pdtest</id>
            <properties>
                <profileActive>pdtest</profileActive>
                <NACOS_ADDR>************:8088</NACOS_ADDR>
                <NACOS_USERNAME>nacos</NACOS_USERNAME>
                <NACOS_PASSWORD>nacos</NACOS_PASSWORD>
                <NACOS_NAMESPACE>4d1f46fe-a791-4518-993c-bc98771ac0a3</NACOS_NAMESPACE>
            </properties>
        </profile>
        <profile>
            <id>pdprd</id>
            <properties>
                <profileActive>pdprd</profileActive>
                <NACOS_ADDR>************:8088</NACOS_ADDR>
                <NACOS_USERNAME>nacos</NACOS_USERNAME>
                <NACOS_PASSWORD>nacos</NACOS_PASSWORD>
                <NACOS_NAMESPACE>3d28f7ba-d510-4909-9a73-30df41a45bb6</NACOS_NAMESPACE>
            </properties>
        </profile>

        <!-- 兼容老系统命名空间别名，进行容器化打包构建部署，容器运行时传递环境变量 START -->
        <profile>
            <id>uat</id>
            <properties>
                <profileActive>uat</profileActive>
            </properties>
        </profile>
        <profile>
            <id>prd</id>
            <properties>
                <profileActive>prd</profileActive>
            </properties>
        </profile>
        <!-- 兼容老系统命名空间别名，进行容器化打包构建部署 END -->
    </profiles>

    <dependencies>
        <dependency>
            <groupId>com.simbest.cloud</groupId>
            <artifactId>simbest-cloud-clients</artifactId>
            <version>${simbest.cloud.version}</version>
        </dependency>
    </dependencies>


    <build>
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <!-- SpringBoot插件 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <addResources>false</addResources>
                    <profiles>
                        <profile>pdtest</profile>
                        <profile>pdprd</profile>
                        <profile>uat</profile>
                        <profile>prd</profile>
                    </profiles>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <!-- SpringBoot插件 -->
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                    <configuration>
                        <finalName>${project.build.finalName}</finalName>
                        <layers>
                            <enabled>true</enabled>
                        </layers>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <!-- 通过Nexus私服管理jar包 START -->
    <repositories>
        <repository>
            <id>thirdparty</id>
            <url>http://10.87.57.26:8082/nexus/repository/maven-thirdparty/</url>
        </repository>
    </repositories>
    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>http://10.87.57.26:8082/nexus/repository/releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://10.87.57.26:8082/nexus/repository/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <!-- 通过Nexus私服管理jar包 END -->
    
</project>