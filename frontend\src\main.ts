import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import 'element-plus/dist/index.css'
import './style.css'
import './styles/element-ui-overrides.css' // 导入Element UI样式覆盖
import App from './App.vue'
import router from './router'

// 导入性能监控工具
import { performanceMonitor } from './utils/performance'

// 创建应用实例
const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 创建Pinia实例
const pinia = createPinia()
// 使用持久化插件
pinia.use(piniaPluginPersistedstate)

// 使用插件
app.use(pinia)
app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
  size: 'default', // 全局组件尺寸
})

// 启动性能监控
performanceMonitor.startMonitoring()

// 挂载应用
app.mount('#app')

// 在应用加载完成后记录性能指标
window.addEventListener('load', () => {
  setTimeout(() => {
    performanceMonitor.report()
  }, 1000)
})