# 2-框架统一领域对象字段规范

本文档描述了SIMBEST框架中的领域模型字段说明。

## 领域对象总览

| 领域对象名称 | 级别 | 用途 | 继承关系 |
|------------|------|------|----------|
| GenericModel | 通用级 | 提供通用功能，包括序列化、动态排序、分页查询等基础功能 | 无（基础父类） |
| SystemModel | 系统级 | 提供系统级功能，包括创建时间和修改时间的自动维护 | 继承自 GenericModel |
| LogicModel | 业务级 | 提供业务逻辑功能，包括数据可用性控制、逻辑删除和审计跟踪 | 继承自 SystemModel |
| WfFormModel | 流程级 | 提供工作流表单基础功能，包含组织机构相关信息 | 继承自 LogicModel |
| ActTaskInstModel | Flowable6流程实体 | Flowable6工作流任务实例，用于记录和跟踪工作流任务的完整生命周期 | 继承自 WfFormModel |

## 1. GenericModel（通用实体模型）

| 字段名称 | 字段类型 | 字段含义 | 是否持久化 |
|---------|---------|---------|-----------|
| orderByClause | String | 动态排序字段，用于传递排序条件 | 否 |
| ssDate | Date | 日期范围查询起始时间 | 否 |
| eeDate | Date | 日期范围查询结束时间 | 否 |
| pageIndex | Integer | 分页查询的页码（从第几页开始） | 否 |
| pagesize | Integer | 分页查询的每页记录数 | 否 |

## 2. SystemModel（系统实体模型）

| 字段名称 | 字段类型 | 字段含义 | 是否持久化 | 自动维护 |
|---------|---------|---------|-----------|---------|
| createdTime | LocalDateTime | 数据创建时间 | 是 | 是（@CreationTimestamp） |
| modifiedTime | LocalDateTime | 数据最后修改时间 | 是 | 是（@UpdateTimestamp） |

## 3. LogicModel（逻辑实体模型）

| 字段名称 | 字段类型 | 字段含义 | 是否持久化 | 是否可空 |
|---------|---------|---------|-----------|---------|
| enabled | Boolean | 数据是否可用标志（true:可用，false:不可用） | 是 | 否 |
| removedTime | LocalDateTime | 数据逻辑删除时间 | 是 | 是 |
| creator | String | 数据创建者 | 是 | 否 |
| modifier | String | 数据最后修改者 | 是 | 否 |

## 4. WfFormModel（工作流表单模型）

| 字段名称 | 字段类型 | 字段含义 | 是否持久化 | 是否可空 |
|---------|---------|---------|-----------|---------|
| belongCompanyCode | String | 所属公司编码 | 是 | 是 |
| belongCompanyName | String | 所属公司名称 | 是 | 是 |
| belongCompanyTypeDictValue | String | 所属公司类型（省公司、地市分公司、县/市区分公司） | 是 | 是 |
| belongCompanyTypeDictDesc | String | 所属公司类型描述 | 否（@Transient） | 是 |
| belongDepartmentCode | String | 所属部门编码 | 是 | 是 |
| belongDepartmentName | String | 所属部门名称 | 是 | 是 |
| belongOrgCode | String | 所属部门编码 | 是 | 是 |
| belongOrgName | String | 所属组织名称 | 是 | 是 |

## 5. ActTaskInstModel（工作流任务实例模型）

| 字段名称 | 字段类型 | 字段含义 | 是否持久化 | 是否可空 |
|---------|---------|---------|-----------|---------|
| id | String | 主键（前缀FAT） | 是 | 否 |
| taskId | String | 任务ID（唯一） | 是 | 是 |
| parentTaskId | String | 父任务ID | 是 | 是 |
| taskDefinitionId | String | 任务定义ID | 是 | 是 |
| revision | Integer | 版本号 | 是 | 是 |
| executionId | String | 执行实例ID | 是 | 是 |
| processInstId | String | 流程实例ID | 是 | 是 |
| taskDefinitionKey | String | 任务定义Key | 是 | 是 |
| processDefinitionId | String | 流程定义ID | 是 | 是 |
| scopeId | String | 作用域ID | 是 | 是 |
| subScopeId | String | 子作用域ID | 是 | 是 |
| scopeType | String | 作用域类型 | 是 | 是 |
| name | String | 任务名称 | 是 | 是 |
| description | String | 任务描述 | 是 | 是 |
| owner | String | 任务所有者 | 是 | 是 |
| assignee | String | 任务处理人 | 是 | 是 |
| assigneeName | String | 完成人中文名称 | 是 | 是 |
| delegationState | String | 委托状态 | 是 | 是 |
| priority | Integer | 优先级 | 是 | 是 |
| taskCreateTime | LocalDateTime | 任务创建时间 | 是 | 是 |
| dueDate | LocalDateTime | 任务到期时间 | 是 | 是 |
| category | String | 任务类别 | 是 | 是 |
| suspensionState | Integer | 挂起状态 | 是 | 是 |
| tenantId | String | 租户ID | 是 | 是 |
| formKey | String | 表单标识 | 是 | 是 |
| claimTime | LocalDateTime | 任务认领时间 | 是 | 是 |
| endTime | LocalDateTime | 任务结束时间 | 是 | 是 |
| participantIdentity | String | 当前工单办理人身份标识（userId#orgCode#postId） | 是 | 是 |
| fromTaskId | String | 源任务ID（起草环节该ID为-1） | 是 | 是 |
| businessKey | String | 主单据ID（pmInstId） | 是 | 是 |
| spare01 | String | 扩展字段01 | 是 | 是 |
| spare02 | String | 扩展字段02 | 是 | 是 |
| spare03 | String | 扩展字段03 | 是 | 是 |
| orgFullName | String | 组织全路径 | 是 | 是 |
| nextActivityName | String | 下一活动名称 | 否（@Transient） | 是 |
| nextActivityAssignee | String | 下一活动处理人 | 否（@Transient） | 是 |
| participantOrgDisplayName | String | 参与者组织显示名称 | 否（@Transient） | 是 |
| approvalComments | String | 审批意见 | 否（@Transient） | 是 |
| isPushTodo | Boolean | 是否推送待办 | 否（@Transient） | 是 |
| isPushMsg | Boolean | 是否推送短信 | 否（@Transient） | 是 |

## 继承关系更新

- LogicModel 继承自 SystemModel
- SystemModel 继承自 GenericModel
- WfFormModel 继承自 LogicModel，是一个抽象类，包含工作流表单的基础字段
- ActTaskInstModel 继承自 WfFormModel，实现了Flowable6流程引擎具体的任务实例模型

## 说明

1. GenericModel是所有实体类的基础父类，提供通用功能如序列化、动态排序、日期范围查询等
2. SystemModel在GenericModel基础上增加了创建时间和修改时间的自动维护功能
3. LogicModel在SystemModel基础上增加了逻辑删除、可用性控制和审计跟踪功能
4. WfFormModel继承自LogicModel，提供了工作流表单的通用字段，主要包含组织机构相关信息
5. ActTaskInstModel继承自WfFormModel，是Flowable6流程引擎中一个完整的任务实例实体，包含了任务的所有属性和状态信息，用于记录和跟踪工作流任务的完整生命周期