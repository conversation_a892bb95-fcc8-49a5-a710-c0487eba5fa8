<template>
  <div class="sales-list-container">
    <div class="page-header">
      <h1 class="page-title">销售管理</h1>
      <div class="header-actions">
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>新增销售
        </el-button>
      </div>
    </div>

    <el-card class="filter-container">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="转账时间" prop="dateRange">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="负责人" prop="salesperson">
          <el-input
            v-model="queryParams.salesperson"
            placeholder="请输入"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="转入方账户" prop="customerName">
          <el-input
            v-model="queryParams.customerName"
            placeholder="请输入"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="渠道/销售名称" prop="channelName">
          <el-input
            v-model="queryParams.channelName"
            placeholder="请输入"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="稽核状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="全部"
            clearable
            style="width: 150px"
          >
            <el-option label="未稽核" value="未稽核" />
            <el-option label="已匹配" value="已匹配" />
            <el-option label="未匹配" value="未匹配" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="table-container">
      <el-table
        v-loading="loading"
        :data="salesData"
        border
        stripe
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="transfer_time" label="转账时间" min-width="100">
          <template #default="{ row }">
            {{ formatDate(row.transfer_time) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="recipient_account_name"
          label="转入方账户"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column
          prop="channel_sales_name"
          label="渠道/销售名称"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column
          prop="person_in_charge"
          label="负责人"
          min-width="90"
        />
        <el-table-column
          prop="business_platform"
          label="业务平台"
          min-width="120"
        />
        <el-table-column
          prop="currency"
          label="户币"
          min-width="100"
          align="right"
        >
          <template #default="{ row }">
            {{ formatPrice(row.currency) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="profit"
          label="利润"
          min-width="100"
          align="right"
        >
          <template #default="{ row }">
            {{ formatPrice(row.profit) }}
          </template>
        </el-table-column>
        <el-table-column prop="audit_status" label="稽核状态" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getAuditStatusType(row.audit_status)">{{
              row.audit_status
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          label="备注"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column label="操作" fixed="right" width="180">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)"
              >编辑</el-button
            >
            <el-button type="primary" link @click="handleView(row)"
              >查看</el-button
            >
            <el-button type="danger" link @click="handleDelete(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Search,
  Refresh,
  Plus,
  Upload,
  Download,
} from "@element-plus/icons-vue";
import type { FormInstance } from "element-plus";
import {
  getSalesList,
  deleteSales,
  SalesQueryParams,
  SalesInfo,
} from "@/api/modules/sales";
// 使用console代替ApiLogger
// import { ApiLogger } from "@/api/utils/logger";

console.info("销售列表页面组件已加载");

const router = useRouter();
const queryForm = ref<FormInstance>();

// 查询参数
const queryParams = reactive<SalesQueryParams & { dateRange: string[] }>({
  page: 1,
  size: 10,
  dateRange: [],
  sales_person: "",
  customer_name: "",
  product_name: "",
  audit_status: "",
});

// 表格加载状态
const loading = ref(false);
// 总记录数
const total = ref(0);
// 销售数据列表
const salesData = ref<SalesInfo[]>([]);
// 选中的数据
const selectedRows = ref<SalesInfo[]>([]);

// 初始化数据
const getList = async () => {
  loading.value = true;

  try {
    // 构建查询参数
    const params: SalesQueryParams = {
      page: queryParams.page,
      size: queryParams.size,
    };

    // 添加其他查询条件
    if (queryParams.dateRange && queryParams.dateRange.length === 2) {
      params.start_date = queryParams.dateRange[0];
      params.end_date = queryParams.dateRange[1];
    }
    if (queryParams.sales_person)
      params.sales_person = queryParams.sales_person;
    if (queryParams.customer_name)
      params.customer_name = queryParams.customer_name;
    if (queryParams.product_name)
      params.product_name = queryParams.product_name;
    if (queryParams.audit_status)
      params.audit_status = queryParams.audit_status;

    console.info("获取销售列表", params);

    // 调用API获取数据
    const result = await getSalesList(params);

    // 更新数据
    // 使用标准的分页响应格式
    if (result && result.items && Array.isArray(result.items)) {
      salesData.value = result.items;
      total.value = result.total || result.items.length || 0;
      console.info("获取销售列表成功，使用标准分页格式");
    } else if (Array.isArray(result)) {
      // 直接返回数组的情况
      salesData.value = result;
      total.value = result.length;
      console.info("获取销售列表成功，使用数组格式");
    } else {
      // 其他情况
      console.warn("响应数据格式不符合预期:", result);
      salesData.value = [];
      total.value = 0;
    }

    console.info("获取销售列表成功", {
      total: total.value,
      count: salesData.value.length,
    });
  } catch (error) {
    console.error("获取销售数据失败", error);
    ElMessage.error("获取销售数据失败");
    salesData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  queryParams.page = 1;
  getList();
};

// 重置查询
const resetQuery = () => {
  if (queryForm.value) {
    queryForm.value.resetFields();
  }
  handleSearch();
};

// 选中数据变化
const handleSelectionChange = (selection: SalesInfo[]) => {
  selectedRows.value = selection;
};

// 修改每页数量
const handleSizeChange = (val: number) => {
  queryParams.size = val;
  getList();
};

// 修改页码
const handleCurrentChange = (val: number) => {
  queryParams.page = val;
  getList();
};

// 新增
const handleCreate = () => {
  router.push("/sales/create");
};

// 编辑
const handleEdit = (row: SalesInfo) => {
  router.push(`/sales/edit/${row.id}`);
};

// 查看
const handleView = (row: SalesInfo) => {
  router.push(`/sales/detail/${row.id}`);
};

// 删除
const handleDelete = (row: SalesInfo) => {
  ElMessageBox.confirm("确认删除该销售记录吗？", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      try {
        console.info("删除销售记录", { id: row.id });
        await deleteSales(row.id);
        ElMessage.success("删除成功");
        getList();
      } catch (error) {
        console.error("删除销售记录失败", error);
        ElMessage.error("删除失败");
      }
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
};

// 导入
const handleImport = () => {
  ElMessage.info("导入功能开发中...");
};

// 导出
const handleExport = () => {
  ElMessage.info("导出功能开发中...");
};

// 格式化价格
const formatPrice = (price: number) => {
  return `¥ ${price.toFixed(2)}`;
};

// 注意：此函数暂未使用，保留以备后续需要
// 获取销售员名称
// const getSalespersonName = (code: string) => {
//   const map: Record<string, string> = {
//     zhangsan: "张三",
//     lisi: "李四",
//     wangwu: "王五",
//   };
//   return map[code] || code;
// };

// 获取稽核状态样式
const getAuditStatusType = (status: string) => {
  const map: Record<string, string> = {
    未稽核: "info",
    已匹配: "success",
    未匹配: "danger",
  };
  return map[status] || "info";
};

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN");
};

// 页面加载时获取数据
onMounted(() => {
  getList();
});
</script>

<style scoped>
.sales-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  color: #303133;
  margin: 0;
}

.filter-container {
  margin-bottom: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
