<template>
  <el-card class="search-form-container" :body-style="{ padding: '15px' }">
    <el-form
      ref="formRef"
      :model="modelValue"
      :inline="true"
      :label-width="labelWidth"
      size="default"
    >
      <slot></slot>
      <el-form-item class="search-buttons">
        <el-button type="primary" :loading="loading" @click="handleSearch">
          <el-icon><Search /></el-icon>{{ searchText }}
        </el-button>
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>{{ resetText }}
        </el-button>
        <slot name="extra-buttons"></slot>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { Search, Refresh } from "@element-plus/icons-vue";
import type { FormInstance } from "element-plus";

const props = defineProps({
  /**
   * 表单数据对象
   */
  modelValue: {
    type: Object,
    required: true,
  },
  /**
   * 标签宽度
   */
  labelWidth: {
    type: String,
    default: "80px",
  },
  /**
   * 搜索按钮文本
   */
  searchText: {
    type: String,
    default: "搜索",
  },
  /**
   * 重置按钮文本
   */
  resetText: {
    type: String,
    default: "重置",
  },
  /**
   * 加载状态
   */
  loading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["search", "reset", "update:modelValue"]);

const formRef = ref<FormInstance>();

// 搜索
const handleSearch = () => {
  emit("search");
};

// 重置
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields();

    // 对于日期范围等特殊字段，可能需要手动重置
    const form = props.modelValue as Record<string, any>;
    Object.keys(form).forEach((key) => {
      if (Array.isArray(form[key]) && form[key].length > 0) {
        form[key] = [];
      }
    });

    emit("update:modelValue", { ...props.modelValue });
    emit("reset");
  }
};

// 暴露方法给父组件
defineExpose({
  formRef,
  reset: handleReset,
});
</script>

<style scoped>
.search-form-container {
  margin-bottom: 20px;
}

.search-buttons {
  margin-left: 10px;
}

:deep(.el-form-item) {
  margin-bottom: 10px;
  margin-right: 15px;
}

@media (max-width: 768px) {
  :deep(.el-form-item) {
    margin-right: 0;
    width: 100%;
  }
}
</style>
