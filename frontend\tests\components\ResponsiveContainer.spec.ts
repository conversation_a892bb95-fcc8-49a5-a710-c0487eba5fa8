import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import ResponsiveContainer from '@/components/layout/ResponsiveContainer.vue'
import { useResponsive } from '@/utils/responsive'

// 模拟响应式工具
vi.mock('@/utils/responsive', () => ({
  useResponsive: vi.fn()
}))

describe('ResponsiveContainer.vue', () => {
  // 测试默认渲染
  it('renders with default props', () => {
    // 模拟响应式数据
    const mockUseResponsive = useResponsive as jest.Mock
    mockUseResponsive.mockReturnValue({
      currentBreakpoint: ref('lg'),
      deviceType: ref('desktop'),
      isMobile: ref(false),
      isTablet: ref(false),
      isDesktop: ref(true)
    })
    
    // 挂载组件
    const wrapper = mount(ResponsiveContainer)
    
    // 验证组件渲染
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.classes()).toContain('responsive-container')
    expect(wrapper.classes()).toContain('device-desktop')
    expect(wrapper.classes()).toContain('breakpoint-lg')
    expect(wrapper.classes()).not.toContain('is-mobile')
  })
  
  // 测试流体布局
  it('renders with fluid layout', () => {
    // 模拟响应式数据
    const mockUseResponsive = useResponsive as jest.Mock
    mockUseResponsive.mockReturnValue({
      currentBreakpoint: ref('lg'),
      deviceType: ref('desktop'),
      isMobile: ref(false),
      isTablet: ref(false),
      isDesktop: ref(true)
    })
    
    // 挂载组件
    const wrapper = mount(ResponsiveContainer, {
      props: {
        fluid: true
      }
    })
    
    // 验证组件样式
    expect(wrapper.attributes('style')).toContain('width: 100%')
  })
  
  // 测试自定义最大宽度
  it('renders with custom max width', () => {
    // 模拟响应式数据
    const mockUseResponsive = useResponsive as jest.Mock
    mockUseResponsive.mockReturnValue({
      currentBreakpoint: ref('lg'),
      deviceType: ref('desktop'),
      isMobile: ref(false),
      isTablet: ref(false),
      isDesktop: ref(true)
    })
    
    // 挂载组件
    const wrapper = mount(ResponsiveContainer, {
      props: {
        maxWidth: '800px'
      }
    })
    
    // 验证组件样式
    expect(wrapper.attributes('style')).toContain('max-width: 800px')
  })
  
  // 测试移动设备渲染
  it('renders correctly on mobile device', () => {
    // 模拟响应式数据
    const mockUseResponsive = useResponsive as jest.Mock
    mockUseResponsive.mockReturnValue({
      currentBreakpoint: ref('xs'),
      deviceType: ref('mobile'),
      isMobile: ref(true),
      isTablet: ref(false),
      isDesktop: ref(false)
    })
    
    // 挂载组件
    const wrapper = mount(ResponsiveContainer)
    
    // 验证组件渲染
    expect(wrapper.classes()).toContain('device-mobile')
    expect(wrapper.classes()).toContain('breakpoint-xs')
    expect(wrapper.classes()).toContain('is-mobile')
    
    // 验证组件样式
    expect(wrapper.attributes('style')).toContain('max-width: 100%')
    expect(wrapper.attributes('style')).toContain('padding: 0 15px')
  })
  
  // 测试平板设备渲染
  it('renders correctly on tablet device', () => {
    // 模拟响应式数据
    const mockUseResponsive = useResponsive as jest.Mock
    mockUseResponsive.mockReturnValue({
      currentBreakpoint: ref('md'),
      deviceType: ref('tablet'),
      isMobile: ref(false),
      isTablet: ref(true),
      isDesktop: ref(false)
    })
    
    // 挂载组件
    const wrapper = mount(ResponsiveContainer)
    
    // 验证组件渲染
    expect(wrapper.classes()).toContain('device-tablet')
    expect(wrapper.classes()).toContain('breakpoint-md')
    
    // 验证组件样式
    expect(wrapper.attributes('style')).toContain('max-width: 720px')
    expect(wrapper.attributes('style')).toContain('padding: 0 20px')
  })
  
  // 测试自定义内边距
  it('renders with custom padding', () => {
    // 模拟响应式数据
    const mockUseResponsive = useResponsive as jest.Mock
    mockUseResponsive.mockReturnValue({
      currentBreakpoint: ref('lg'),
      deviceType: ref('desktop'),
      isMobile: ref(false),
      isTablet: ref(false),
      isDesktop: ref(true)
    })
    
    // 挂载组件
    const wrapper = mount(ResponsiveContainer, {
      props: {
        padding: '30px'
      }
    })
    
    // 验证组件样式
    expect(wrapper.attributes('style')).toContain('padding: 30px')
  })
  
  // 测试自定义外边距
  it('renders with custom margin', () => {
    // 模拟响应式数据
    const mockUseResponsive = useResponsive as jest.Mock
    mockUseResponsive.mockReturnValue({
      currentBreakpoint: ref('lg'),
      deviceType: ref('desktop'),
      isMobile: ref(false),
      isTablet: ref(false),
      isDesktop: ref(true)
    })
    
    // 挂载组件
    const wrapper = mount(ResponsiveContainer, {
      props: {
        margin: '20px 0'
      }
    })
    
    // 验证组件样式
    expect(wrapper.attributes('style')).toContain('margin: 20px 0')
  })
  
  // 测试非居中布局
  it('renders with non-centered layout', () => {
    // 模拟响应式数据
    const mockUseResponsive = useResponsive as jest.Mock
    mockUseResponsive.mockReturnValue({
      currentBreakpoint: ref('lg'),
      deviceType: ref('desktop'),
      isMobile: ref(false),
      isTablet: ref(false),
      isDesktop: ref(true)
    })
    
    // 挂载组件
    const wrapper = mount(ResponsiveContainer, {
      props: {
        centered: false,
        margin: '0'
      }
    })
    
    // 验证组件样式
    expect(wrapper.attributes('style')).toContain('margin: 0')
    expect(wrapper.attributes('style')).not.toContain('margin: 0 auto')
  })
  
  // 测试插槽内容
  it('renders slot content', () => {
    // 模拟响应式数据
    const mockUseResponsive = useResponsive as jest.Mock
    mockUseResponsive.mockReturnValue({
      currentBreakpoint: ref('lg'),
      deviceType: ref('desktop'),
      isMobile: ref(false),
      isTablet: ref(false),
      isDesktop: ref(true)
    })
    
    // 挂载组件
    const wrapper = mount(ResponsiveContainer, {
      slots: {
        default: '<div class="test-content">Test Content</div>'
      }
    })
    
    // 验证插槽内容
    expect(wrapper.find('.test-content').exists()).toBe(true)
    expect(wrapper.find('.test-content').text()).toBe('Test Content')
  })
})
