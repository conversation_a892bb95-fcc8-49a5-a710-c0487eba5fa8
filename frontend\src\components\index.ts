// 布局组件
import PageHeader from "./layout/PageHeader.vue";
import ResponsiveContainer from "./layout/ResponsiveContainer.vue";

// 表单组件
import SearchForm from "./form/SearchForm.vue";
import EnhancedForm from "./form/EnhancedForm.vue";
import EnhancedFormItem from "./form/EnhancedFormItem.vue";

// 表格组件
import DataTable from "./table/DataTable.vue";
import VirtualTable from "./table/VirtualTable.vue";

// 通用组件
import StatusTag from "./common/StatusTag.vue";
import ActionButtons from "./common/ActionButtons.vue";
import SkeletonLoader from "./common/SkeletonLoader.vue";
import TableSkeleton from "./common/TableSkeleton.vue";
import CardSkeleton from "./common/CardSkeleton.vue";

// 图表组件
import OptimizedChart from "./chart/OptimizedChart.vue";

// 导出组件
export {
  // 布局组件
  PageHeader,
  ResponsiveContainer,

  // 表单组件
  SearchForm,
  EnhancedForm,
  EnhancedFormItem,

  // 表格组件
  DataTable,
  VirtualTable,

  // 通用组件
  StatusTag,
  ActionButtons,
  SkeletonLoader,
  TableSkeleton,
  CardSkeleton,

  // 图表组件
  OptimizedChart,
};

// 默认导出所有组件
export default {
  PageHeader,
  ResponsiveContainer,
  SearchForm,
  EnhancedForm,
  EnhancedFormItem,
  DataTable,
  VirtualTable,
  StatusTag,
  ActionButtons,
  SkeletonLoader,
  TableSkeleton,
  CardSkeleton,
  OptimizedChart,
};
