---
description: 
globs: 
alwaysApply: true
---
# Project Rules for Cursor AI

# 系统相关数据字典定义
| 数据字典     | 数据字典类型      | 数据字典值      | 中文含义       | 字段类型  |
|--------------|-------------------|-----------------|----------------|-----------|
| 办理类型     | todoType       | specific | 主办      | varchar   |
| 办理类型 | todoType | assist | 协办        | varchar   |
| 反馈类型     | feedbackType      | phase      | 阶段性反馈 | varchar   |
| 反馈类型 | feedbackType | final       | 结项反馈  | varchar   |
| 反馈类型 | feedbackType | request     | 申请延期  | varchar   |
| 反馈类型 | feedbackType | unable | 无法支撑       | varchar   |
| 是否 | yesno | yes       | 是           | varchar   |
| 是否   | yesno | no        | 否           | varchar   |
| 优先级     | priorityLevel | high     | 高           | varchar   |
| 优先级     | priorityLevel | medium     | 中           | varchar   |
| 优先级     | priorityLevel | low     | 低           | varchar   |
| 待办类型 | taskType | task | 待办  | varchar   |
| 待办类型 | taskType | join | 已办       | varchar   |


# 1.“我要支撑”模块

## 1.1 涉及领域对象
| 模块名称 | 对象类型     | 对象名称     |
| ------------ | ------------ | ------------ |
| apply       | 流程级 | 服务支撑信息实体 |
| apply       | 业务级  | 具体办理人员信息实体  |
| apply       | 业务级   | 反馈信息实体   |
| apply       | 业务级  | 出彩项目与主办部门关联信息实体  |

### 1.1.1 服务支撑信息实体

  - 主单据ID 

  - 需求单位 

  - 需求提出人 

  - 需求提出人账号 

  - 联系方式 

  - 归属项目 

  - 归属项目编码 

  - 期望完成时间 日期（格式YYYY-MM-DD）

  - 优先级（数据字典：优先级） 

  - 项目目标 字符串 长度2000

  - 所需资源 字符串 长度2000

  - 响应及时率（1-4分） 数字

  - 解决满意度（1-4分） 数字

  - 方案创新性（1-2分） 数字

  - 评价总分（3-10分） 数字

  - 综合建议 字符串，长度1000

  - 解决方案概述 clob

  - 具体人员名称（非持久化字段）

  - 具体人员账号 （非持久化字段）

  - 协办部门名称 （非持久化字段）

  - 协办部门编码（非持久化字段）

  - 反馈信息列表（非持久化字段，列表）

### 1.1.2 具体办理人员信息实体

  - 服务支撑表主键
  - 办理类型（数据字典：办理类型）
  - 公司名称
  - 公司编码
  - 部门名称
  - 部门编码
  - 具体人员姓名
  - 具体人员账号
### 1.1.3 反馈信息实体

  - 服务支撑表主键
  - 反馈时间
  - 办理类型（数据字典：办理类型）
  - 反馈类型（数据字典：反馈类型）
  - 反馈人（具体经办人）姓名
  - 反馈人（具体经办人）账号
  - 反馈人（具体经办人）所属公司名称
  - 反馈人（具体经办人）所属公司编码
  - 反馈人（具体经办人）所属部门名称
  - 反馈人（具体经办人）所属部门编码
  - 完成率（指标） 数字（正整数）
  - 是否存在延期风险（数据字典：是否）
  - 进展情况 字符串，长度1000
  - 延期/无法支撑原因 字符串，长度1000
  - 附件id，多个以逗号分割
  - 附件数组 无需生成表字段，数据封装需要

### 1.1.4 出彩项目与主办部门关联信息实体

- 出彩项目名称
- 出彩项目编码
- 公司名称
- 公司编码
- 归属主办部门名称
- 归属主办部门编码
- 排序值 数字

## 1.2 流程实例环节信息定义

| 序号 | 环节ID | 环节名称 |
|------|--------|----------|
| 1 | gjcc.start | 分公司起草 |
| 2 | gjcc.branch_dj_approval | 分公司部门领导审批 |
| 3 | gjcc.branch_manage_approval | 分公司管理层审批 |
| 4 | gjcc.province_zb_approval | 省公司主办部门领导办理 |
| 5 | gjcc.province_zb_deal | 省公司主办部门具体人办理 |
| 6 | gjcc.province_xb_approval | 省公司协办部门领导办理 |
| 7 | gjcc.province_xb_deal | 省公司协办部门具体人员办理 |
| 8 | gjcc.province_xb_yq_approval | 省公司协办部门领导延期审批 |
| 9 | gjcc.province_zb_yq_approval | 省公司主办部门领导延期审批 |
| 10 | gjcc.end | 起草人归档 |
| 11 | gjcc.branch_zb_approval | 分公司主办部门领导办理 |
| 12 | gjcc.branch_xb_approval | 分公司协办部门领导办理 |
| 13 | gjcc.branch_zb_deal | 分公司主办部门具体人员办理 |
| 14 | gjcc.branch_zb_yq_approval | 分公司主办部门领导延期审批 |
| 15 | gjcc.branch_xb_deal | 分公司协办部门具体人员办理 |
| 16 | gjcc.branch_xb_yq_approval | 分公司协办部门领导延期审批 |

### 1.3 流程环节更新业务控制

服务支撑信息服务层实现IBusinessFormService的saveAndUpdateFormData接口

- 输入参数

  - pmInsId：主单据ID
  - pmInstType：主单据类型
  - activityDefId：当前活动定义ID
  - formData：服务支撑数据信息实体对象表单数据，使用时需通过json转换工具转为服务支撑对象 

- 实现逻辑

  ```
  1. 获取formData信息并转换为服务支撑实体对象。
  2. 首先，判断当前环节是否为起草环节：
  	如果是，检查服务支撑对象是否存在主键id：
  		2.1 如果存在则执行更新操作；
  		2.2 如果不存在不存在则执行新增操作。
  3. 其次，判断当前环节是否为分公司主办部门领导审批或省公司主办部门领导办理环节：
  	如果是：
  	3.1 从服务支撑对象获取具体人员账号和协办部门编码
  	3.2 根据具体人员账号调用UumsSysUserApi类中获取人员详情接口，并封装为主办类型人员对象（含公司编码、公司名称、部门编号、部门名称、用户账号、真实姓名、服务支撑对象主键）保存
  	3.3 将协办部门编码和按逗号分割成数组，循环调用UumsSysOrgApi获取部门详情，将公司信息和部门信息连同服务支撑对象主键一同保存至具体办理人员信息中
  4. 再次，判断当前环节是否为分公司协办部门领导延期审批或省公司协办部门领导延期审批环节：
  	如果是：
  	4.1 先根据服务支撑对象中的具体人员账号，调用UumsSysUserApi中获取具体人员详情接口
  	4.2 再根据当前登录人部门和服务支撑对象主键，从具体办理人员信息实体中查询协办人员信息
  	4.3 最后，将4.1中获取到的具体人员信息和4.2中获取到协办人员信息，更新至具体办理人员信息实体中
  5. 判断当前环节是否为服务支撑流程环节说明中序号为5、7、13、15的四个环节之一：
  	如果是：
  	根据服务支撑对象的主键id，并且循环遍历服务支撑对象中的反馈信息数组，逐一保存至反馈信息对象。
  6. 判断当前环节是否为起草人归档环节：
  	如果是，更新服务支撑对象信息。
  7. 最终，将服务支撑对象信息转换为Map对象返回
  ```

- 返回结果

  将服务支撑对象转换为Map<String, Object> 对象返回

## 1.4 查询表单详情信息
服务支撑信息服务层实现IBusinessFormService接口，并完成getFormDetail方法的业务实现

  - 输入参数Map对象，key值定义如下
    - pmInsId：主单据ID
    - pmInstType：工单类型
    - activityDefId：环节编码
    - processInstId：流程实例编码
    - type：待办类型（数据字典：待办类型）

  - 实现逻辑：
    ```
     步骤1：根据主单据ID，查询有效的服务支撑对象数据
     步骤2：判断工单待办类型
     步骤2.1：如果是待办类型
     步骤2.1.1：如果当前环节是起草人归档环节，根据服务支撑对象的主键查询有效的反馈信息对象数组信息，并放入服务支撑对象中
     步骤2.2：如果是已办类型，首先，根据服务支撑对象的主键查询反馈信息对象数组信息，并放入服务支撑对象中。其次根据主单据ID和当前登录人账号查询最近流程任务的流程任务信息。然后判断当前环节信息：
     （1）如果环节为分公司主办部门领导审批环节或省公司主办部门领导审批环节，通过服务支撑对象主键关联查询具体办理人员信息，将主办具体人员姓名放入服务支撑对象的具体人员办理字段，将所有协办具体人员信息的部门名称使用逗号拼接成字符串并放入服务支撑对象的协办部门字段中。
     （2）如果环节为分公司协办部门具体人员办理环节或省公司协办部门具体人办理环节，根据当前登录人部门和服务支撑主键id仅查询协办具体负责人信息对象，并将具体人员信息放在服务支撑的具体办理人员字段。
    ```
   - 返回结果

     返回服务支撑对象

## 1.5 查询所有办理工单（不分页）信息
服务支撑信息持久层、服务层、控制器新增接口

  - 输入参数

     - activitDefId：环节名称
     - source:请求来源，默认值为:PC
     - currentUserCode:加密后的当前登录人信息，非必传
  - 业务逻辑
	```
	步骤1：系统自动获取当前登录用户的账号信息。
	步骤2：基于主单据ID与businessKey的对应关系，关联服务支撑对象和流程任务办理信息对象。
	步骤3：筛选流程任务办理信息中endTime为空的未完成任务。
	步骤4：提取服务支撑对象的需求标题、需求组织、提出人、归属出差项目名称、期望完成时间、项目优先级、主单据ID字段。
	步骤5：提取流程任务信息对象的taskId、taskDefinitionId、processInstId、taskDefinitionKey、processDefinitionId、name、assignee、participantIdentity字段。
	步骤6：通过支撑服务对象主单据ID字段pmInsId与流程任务信息对象的businessKey进行关联查询，过滤用户相关任务。
	步骤7：返回符合关联条件且用户权限内的完整查询结果集。
	```
  - 返回结果
    返回结果采用MapStruct映射DTO


## 1.6 根据归属项目编码及公司编码查询出彩项目与主办部门关联信息表中的主办部门信息
	出彩项目与主办部门关联信息实体的持久层、服务层、控制器新增接口
  - 入参说明
		- projectCode:出彩项目编码
		- companyCode:公司编码
	- 业务逻辑
~~~
	1. 根据@Simbest_User_Rules.md中的持久层接口定义规范 ，使用出彩项目编码和公司编码构建查询条件，其中允许出彩项目编码为空
	2. 使用构建的查询条件和不分页方法查询流程并使用时间倒叙查询，将查询结果直接返回。
~~~
  - 返回结果
    直接将查询结果返回。

## 1.7 根据不同的类型和组织信息获取不同的人员信息
	在IProcessService定义接口并在业务层实现该接口
	- 入参说明：
		- selectUserType ：查询人员类型， 1：查询起草人 ， 2：查询主办具体办理人, 3: 查询部门总经理（省公司部门） ， 4：查询部门经理（分公司部门） ， 5：查询分公司总经理（分公司）
		- applyInfoId : 支撑服务信息表主键， 常用与支撑业务表单信息
		- deptCodes: 部门编码或公司编码，多个使用逗号分割。
	- 业务逻辑
~~~
	1. 判断入参类型，根据类型进行以下处理。
		1.1 如果查询人员类型为1，
			1.1.1 判断applyInfoId是否为空，如果为空，则排除异常，并返回空
			1.1.2 如果不为空，
				******* 则根据applyInfoId查询支撑服务信息表信息。
				******* 根据支撑服务信息表的申请人信息使用UumsSysUserApi查询人员信息并封装为数组后返回。
		1.2 如果查询人员类型为2 ，
			1.2.1 根据applyInfoId查询反馈信息表中主办具体人员信息
			1.2.2 根据具体办理人员信息表中的具体人员信息使用UumsSysUserApi查询人员信息并封装为数组后返回。
		1.3 如果查询人员类型为3，
			1.3.1 根据deptCodes和固定值（53,75）两个参数根据UumsSysUserApi查询某部门下某职位接口查询人员信息，并返回。
		1.4 如果查询人员类型为4，
			1.4.1 根据deptCodes和固定值（33,44）两个参数根据UumsSysUserApi查询某部门下某职位接口查询人员信息，并返回。
		1.5 如果查询人员类型为5 ， 
			1.5.1 根据deptCodes和固定值（49）两个参数根据UumsSysUserApi查询某部门下某职位接口查询人员信息，并返回。
~~~

## 1.8 根据支撑服务主键id查询所有反馈信息
  在UsFeedbackInfo的业务层、持久层、控制器新增接口
  - 入参说明
		- applyInfoId:支撑服务主键id
	- 业务逻辑
~~~
	1. 使用断言判断applyInfoId是否为空，如果为空，则抛出异常。
  2. 根据Simbest_User_Rules.md中的持久层接口定义规范 ，使用支撑服务主键id构建查询条件.
	3. 使用构建的查询条件和不分页方法查询流程并使用时间倒叙查询，将查询结果直接返回。
~~~
  - 返回结果
    直接将查询结果返回。

## 1.9 批量办理的工单信息
	在ProcessService的业务层、控制层新增接口
	- 入参说明：使用List<Map<string , object >>类型接受
~~~
		[
			{
				"appCode": "czhydd",//应用编码
				"type": "FLOW", //流转类型， 流程启动时传：START , 流程流转时传：FLOW
				"title": "测试数据请忽略-0001", //标题
				"processDefKey": "Process_1711980352718", //流程名称
				"pmInsType": "", //主单据类型
				"outcome": "Flow_1eaalem", //决策项编码
				"taskDefinitionKey": "Activity_1e6pney,发起部门主管审批,one", //下一环节配置信息
				"activityDefId": "czhydd.start" ,  //当前环节编码
				"pmInsId":"", //主单据id
				"applyInfoId":"123" , //支撑服务主键id
				"projectCode":"111", //所属项目编码
				"projectName":"1231", //所属项目名称
			}
		]
~~~
	- outcome取值如下，请将编码升成在GjccConstants系统常量定义类中，后续直接使用系统常量定义。
		1. Flow_0gq74zb：分公司主办单位领导办理
		2. Flow_12icjv7：分公司总经理审批
		3. Flow_0k86f22_end：不予支撑
		4. Flow_1on1kbs：退回修改
		5. Flow_11fgtrc_end：不予支撑
		6. Flow_0t0nu30：退回修改
		
	- 业务逻辑
~~~
	控制层实现：
  1.验证入参是否符合要求， outcome、applyInfoId、projectCode必传。
  2. 使用当前环节编码 + 当前登录人账号做key值在redis缓存中获取数据
		2.1 如果可以获取到，则直接返回提示，提示信息为：当前存在工单正在批量办理中，请稍后再试。
		2.2 如果获取不到值，则保存一个时间为两小时失效的值，值为：processing。
	业务层使用异步实现：
  3. 循环处理入参信息
	4. 判断决策项编码outcome ，根据不同决策项编码处理一下逻辑
		4.1 如果决策项编码为 Flow_1on1kbs 或者 Flow_1on1kbs
			4.1.1 调用IProcessService 中的getUsersByType方法，查询起草人信息。
			4.1.2 将查询到起草人信息的人员账号、人员姓名、人员组织编码分别使用nextUser ， nextUserName ，nextUserOrgCode作为key放入入参对象中
			4.1.3 入参对象中添加规定参数信息：
				nextUserPostId：空值
				nextActivityParam：固定值（inputUserId）
				message：固定值（退回修改）
		4.2 如果决策项编码为 Flow_0k86f22_end 或者 Flow_11fgtrc_end
			4.2.1 调用IProcessService 中的getUsersByType方法，查询起草人信息。
			4.2.2 将查询到人员信息人员账号、人员姓名、人员组织编码分别使用copyNextUserNames 
			4.2.3 入参对象中添加规定参数信息：
				nextUser：空值
				nextUserName：空值
				nextUserOrgCode：空值
				nextUserPostId：空值
				nextActivityParam：固定值（inputUserId）
				message：固定值（不予支撑）
				copyLocation：如果是Flow_0k86f22_end则是Flow_11fgtrc_end_copy，其他均为Flow_0k86f22_end_copy。
				copyLocationName:固定值（起草人阅知）
				copyMessage：固定值（请阅知）
		4.3 如果决策项编码为 Flow_12icjv7
			4.3.1 使用当前登录人所属公司编码，调用IProcessService 中的getUsersByType方法，查询分公司总经理信息。
			4.3.2 将查询到分公司总经理信息的人员账号、人员姓名、人员组织编码分别使用nextUser ， nextUserName ，nextUserOrgCode作为key放入入参对象中
			4.1.3 入参对象中添加规定参数信息：
				nextUserPostId：空值
				nextActivityParam：固定值（inputUserId）
				message：固定值（请领导审批）
		4.4 如果决策项编码为 Flow_0gq74zb
			4.4.1 使用参数中的所属项目编码和当前登录人的所属公司编码调用IUsProjectDeptInfoService中的findMainDeptByProjectAndCompany方法，查询归属项目信息。
			4.4.2 获取查询数组的第一个对象，并获取主办部门编码
			4.4.3 使用获取主办部门编码，调用IProcessService 中的getUsersByType方法，查询分公司部门经理信息。
			4.4.4 将查询到分公司部门经理信息的人员账号、人员姓名、人员组织编码分别使用nextUser ， nextUserName ，nextUserOrgCode作为key放入入参对象中
			4.4.5 入参对象中添加规定参数信息：
				nextUserPostId：空值
				nextActivityParam：固定值（inputUserId）
				message：固定值（请办理）
		4.5 调用ICommonProcessService中的startProcessAndTaskSubmitCommon，其中source：固定值（PC） , currentUserCode为空，map即封装后为数据。
~~~
	- 返回结果
		直接返回成功或者提示信息。


---
更多详细信息请参考各规范文档。