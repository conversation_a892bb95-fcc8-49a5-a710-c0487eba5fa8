<template>
  <div class="chart-container" ref="chartRef" :style="{ height: height, width: width }"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts/core'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent
} from 'echarts/components'
import { <PERSON><PERSON>hart, LineChart, PieChart } from 'echarts/charts'
import { LabelLayout, UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'

// 注册必要的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  BarChart,
  LineChart,
  PieChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer
])

const props = defineProps({
  /**
   * 图表选项
   */
  options: {
    type: Object,
    required: true
  },
  /**
   * 图表高度
   */
  height: {
    type: String,
    default: '400px'
  },
  /**
   * 图表宽度
   */
  width: {
    type: String,
    default: '100%'
  },
  /**
   * 是否自动调整大小
   */
  autoResize: {
    type: Boolean,
    default: true
  },
  /**
   * 是否启用动画
   */
  animation: {
    type: Boolean,
    default: true
  },
  /**
   * 是否启用渐进式渲染
   */
  progressive: {
    type: Boolean,
    default: true
  },
  /**
   * 渐进式渲染的数据量阈值
   */
  progressiveThreshold: {
    type: Number,
    default: 500
  },
  /**
   * 是否启用延迟渲染
   */
  lazyUpdate: {
    type: Boolean,
    default: true
  },
  /**
   * 是否使用SVG渲染器
   */
  useSvgRenderer: {
    type: Boolean,
    default: false
  },
  /**
   * 是否使用WebGL渲染器（仅适用于大数据量散点图等）
   */
  useWebGLRenderer: {
    type: Boolean,
    default: false
  },
  /**
   * 主题
   */
  theme: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['initialized', 'click', 'rendered'])

// 图表引用
const chartRef = ref<HTMLElement | null>(null)
// 图表实例
let chartInstance: echarts.ECharts | null = null
// 调整大小的处理函数
let resizeObserver: ResizeObserver | null = null

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  // 销毁旧实例
  if (chartInstance) {
    chartInstance.dispose()
  }
  
  // 创建新实例
  chartInstance = echarts.init(chartRef.value, props.theme, {
    renderer: props.useWebGLRenderer ? 'webgl' : (props.useSvgRenderer ? 'svg' : 'canvas'),
    useDirtyRect: true, // 启用脏矩形渲染优化
    devicePixelRatio: window.devicePixelRatio // 适配高DPI屏幕
  })
  
  // 设置选项
  const options = { ...props.options }
  
  // 优化动画
  if (!props.animation) {
    options.animation = false
  }
  
  // 优化大数据渲染
  if (props.progressive) {
    // 为折线图和柱状图启用渐进式渲染
    if (options.series) {
      options.series = options.series.map((series: any) => {
        if (['line', 'bar', 'scatter'].includes(series.type)) {
          return {
            ...series,
            progressive: props.progressive ? 200 : 0, // 每帧渲染的数据点数量
            progressiveThreshold: props.progressiveThreshold
          }
        }
        return series
      })
    }
  }
  
  // 启用延迟更新
  if (props.lazyUpdate) {
    options.lazyUpdate = true
  }
  
  // 设置选项
  chartInstance.setOption(options)
  
  // 注册事件
  chartInstance.on('click', (params) => {
    emit('click', params)
  })
  
  // 发出初始化事件
  emit('initialized', chartInstance)
  
  // 渲染完成事件
  nextTick(() => {
    emit('rendered')
  })
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) return
  
  const options = { ...props.options }
  
  // 优化动画
  if (!props.animation) {
    options.animation = false
  }
  
  // 优化大数据渲染
  if (props.progressive) {
    // 为折线图和柱状图启用渐进式渲染
    if (options.series) {
      options.series = options.series.map((series: any) => {
        if (['line', 'bar', 'scatter'].includes(series.type)) {
          return {
            ...series,
            progressive: props.progressive ? 200 : 0, // 每帧渲染的数据点数量
            progressiveThreshold: props.progressiveThreshold
          }
        }
        return series
      })
    }
  }
  
  // 启用延迟更新
  if (props.lazyUpdate) {
    options.lazyUpdate = true
  }
  
  // 更新选项
  chartInstance.setOption(options)
  
  // 渲染完成事件
  nextTick(() => {
    emit('rendered')
  })
}

// 调整图表大小
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 监听选项变化
watch(() => props.options, () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

// 监听主题变化
watch(() => props.theme, () => {
  nextTick(() => {
    initChart()
  })
})

// 生命周期钩子
onMounted(() => {
  // 初始化图表
  nextTick(() => {
    initChart()
  })
  
  // 监听窗口大小变化
  if (props.autoResize) {
    // 使用ResizeObserver监听容器大小变化
    if (window.ResizeObserver) {
      resizeObserver = new ResizeObserver(() => {
        resizeChart()
      })
      
      if (chartRef.value) {
        resizeObserver.observe(chartRef.value)
      }
    } else {
      // 降级方案：使用window.resize事件
      window.addEventListener('resize', resizeChart)
    }
  }
})

onUnmounted(() => {
  // 销毁图表实例
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  
  // 移除事件监听
  if (props.autoResize) {
    if (resizeObserver) {
      resizeObserver.disconnect()
      resizeObserver = null
    } else {
      window.removeEventListener('resize', resizeChart)
    }
  }
})

// 暴露方法
defineExpose({
  // 获取图表实例
  getChartInstance: () => chartInstance,
  // 调整大小
  resize: resizeChart,
  // 销毁图表
  dispose: () => {
    if (chartInstance) {
      chartInstance.dispose()
      chartInstance = null
    }
  }
})
</script>

<style scoped>
.chart-container {
  position: relative;
}
</style>
