<template>
  <div class="error-container">
    <div class="error-content">
      <h1 class="error-code">404</h1>
      <div class="error-message">页面未找到</div>
      <div class="error-desc">抱歉，您访问的页面不存在或已被移除</div>
      <el-button type="primary" @click="goHome">返回首页</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}
</script>

<style scoped>
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f0f2f5;
}

.error-content {
  text-align: center;
}

.error-code {
  font-size: 120px;
  font-weight: bold;
  color: #1890ff;
  margin: 0;
  animation: error-code-animation 1.5s ease-in-out infinite alternate;
}

.error-message {
  font-size: 24px;
  color: #303133;
  margin: 20px 0;
}

.error-desc {
  font-size: 16px;
  color: #606266;
  margin-bottom: 30px;
}

@keyframes error-code-animation {
  from {
    transform: translateY(0);
    text-shadow: 0 0 10px rgba(24, 144, 255, 0.2);
  }
  to {
    transform: translateY(-20px);
    text-shadow: 0 20px 20px rgba(24, 144, 255, 0.1);
  }
}
</style>