import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 主题类型
export type ThemeType = 'light' | 'dark' | 'system'

// 布局类型
export type LayoutType = 'vertical' | 'horizontal'

// 应用设置接口
export interface AppSettings {
  theme: ThemeType
  layout: LayoutType
  sidebarCollapsed: boolean
  showBreadcrumb: boolean
  showTags: boolean
  fixedHeader: boolean
  showFooter: boolean
  colorPrimary: string
  fontSize: number
  developerMode: boolean
}

// 默认设置
const defaultSettings: AppSettings = {
  theme: 'light',
  layout: 'vertical',
  sidebarCollapsed: false,
  showBreadcrumb: true,
  showTags: true,
  fixedHeader: true,
  showFooter: true,
  colorPrimary: '#409EFF',
  fontSize: 14,
  developerMode: false
}

// 从本地存储加载设置
const loadSettings = (): AppSettings => {
  const settingsStr = localStorage.getItem('appSettings')
  if (settingsStr) {
    try {
      return JSON.parse(settingsStr)
    } catch (e) {
      console.error('解析应用设置失败', e)
    }
  }
  return { ...defaultSettings }
}

// 保存设置到本地存储
const saveSettings = (settings: AppSettings): void => {
  localStorage.setItem('appSettings', JSON.stringify(settings))
}

// 使用组合式API风格定义存储
export const useSettingsStore = defineStore('settings', () => {
  // 状态
  const settings = ref<AppSettings>(loadSettings())

  // 计算属性
  const isDarkMode = computed(() => {
    if (settings.value.theme === 'system') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    return settings.value.theme === 'dark'
  })

  // 更新主题
  function setTheme(theme: ThemeType) {
    settings.value.theme = theme
    applyTheme()
    saveSettings(settings.value)
  }

  // 更新布局
  function setLayout(layout: LayoutType) {
    settings.value.layout = layout
    saveSettings(settings.value)
  }

  // 切换侧边栏折叠状态
  function toggleSidebar() {
    settings.value.sidebarCollapsed = !settings.value.sidebarCollapsed
    saveSettings(settings.value)
  }

  // 设置侧边栏折叠状态
  function setSidebarCollapsed(collapsed: boolean) {
    console.log('[Settings Store] 设置侧边栏折叠状态:', collapsed)
    settings.value.sidebarCollapsed = collapsed
    saveSettings(settings.value)
  }

  // 更新主色调
  function setPrimaryColor(color: string) {
    settings.value.colorPrimary = color
    document.documentElement.style.setProperty('--el-color-primary', color)
    saveSettings(settings.value)
  }

  // 更新字体大小
  function setFontSize(size: number) {
    settings.value.fontSize = size
    document.documentElement.style.setProperty('--el-font-size-base', `${size}px`)
    saveSettings(settings.value)
  }

  // 重置设置
  function resetSettings() {
    settings.value = { ...defaultSettings }
    applyTheme()
    saveSettings(settings.value)
  }

  // 应用主题
  function applyTheme() {
    const darkMode = isDarkMode.value
    document.documentElement.classList.toggle('dark', darkMode)

    // 设置Element Plus主题
    const htmlEl = document.documentElement
    if (darkMode) {
      htmlEl.setAttribute('data-theme', 'dark')
    } else {
      htmlEl.setAttribute('data-theme', 'light')
    }

    // 设置主色调
    htmlEl.style.setProperty('--el-color-primary', settings.value.colorPrimary)

    // 设置字体大小
    htmlEl.style.setProperty('--el-font-size-base', `${settings.value.fontSize}px`)
  }

  // 初始化
  function init() {
    applyTheme()

    // 监听系统主题变化
    if (settings.value.theme === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      mediaQuery.addEventListener('change', applyTheme)
    }
  }

  // 切换开发者模式
  function toggleDeveloperMode() {
    settings.value.developerMode = !settings.value.developerMode
    saveSettings(settings.value)
  }

  // 设置开发者模式
  function setDeveloperMode(enabled: boolean) {
    settings.value.developerMode = enabled
    saveSettings(settings.value)
  }

  // 返回状态和方法
  return {
    settings,
    isDarkMode,
    setTheme,
    setLayout,
    toggleSidebar,
    setSidebarCollapsed,
    setPrimaryColor,
    setFontSize,
    resetSettings,
    applyTheme,
    init,
    toggleDeveloperMode,
    setDeveloperMode
  }
})
