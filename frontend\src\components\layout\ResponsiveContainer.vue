<template>
  <div
    class="responsive-container"
    :class="[
      `device-${deviceType}`,
      `breakpoint-${currentBreakpoint}`,
      { 'is-mobile': isMobile }
    ]"
    :style="containerStyle"
  >
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useResponsive } from '@/utils/responsive'

const props = defineProps({
  /**
   * 是否使用流体布局（100%宽度）
   */
  fluid: {
    type: Boolean,
    default: false
  },
  /**
   * 最大宽度
   */
  maxWidth: {
    type: [String, Number],
    default: ''
  },
  /**
   * 内边距
   */
  padding: {
    type: [String, Number],
    default: ''
  },
  /**
   * 外边距
   */
  margin: {
    type: [String, Number],
    default: '0 auto'
  },
  /**
   * 是否居中
   */
  centered: {
    type: Boolean,
    default: true
  }
})

// 使用响应式布局
const {
  currentBreakpoint,
  deviceType,
  isMobile,
  isTablet,
  isDesktop
} = useResponsive()

// 计算容器样式
const containerStyle = computed(() => {
  const style: Record<string, string> = {}
  
  // 设置最大宽度
  if (props.maxWidth) {
    style.maxWidth = typeof props.maxWidth === 'number' ? `${props.maxWidth}px` : props.maxWidth
  } else if (!props.fluid) {
    // 根据断点设置最大宽度
    switch (currentBreakpoint.value) {
      case 'xs':
        style.maxWidth = '100%'
        break
      case 'sm':
        style.maxWidth = '540px'
        break
      case 'md':
        style.maxWidth = '720px'
        break
      case 'lg':
        style.maxWidth = '960px'
        break
      case 'xl':
        style.maxWidth = '1140px'
        break
      case 'xxl':
        style.maxWidth = '1320px'
        break
    }
  }
  
  // 设置内边距
  if (props.padding) {
    style.padding = typeof props.padding === 'number' ? `${props.padding}px` : props.padding
  } else {
    // 根据设备类型设置内边距
    if (isMobile.value) {
      style.padding = '0 15px'
    } else if (isTablet.value) {
      style.padding = '0 20px'
    } else {
      style.padding = '0 24px'
    }
  }
  
  // 设置外边距
  if (props.margin) {
    style.margin = typeof props.margin === 'number' ? `${props.margin}px` : props.margin
  } else if (props.centered) {
    style.margin = '0 auto'
  }
  
  // 设置宽度
  if (props.fluid) {
    style.width = '100%'
  }
  
  return style
})
</script>

<style scoped>
.responsive-container {
  box-sizing: border-box;
  width: 100%;
}

/* 移动设备样式 */
.device-mobile {
  font-size: 14px;
}

/* 平板设备样式 */
.device-tablet {
  font-size: 15px;
}

/* 桌面设备样式 */
.device-desktop {
  font-size: 16px;
}
</style>
