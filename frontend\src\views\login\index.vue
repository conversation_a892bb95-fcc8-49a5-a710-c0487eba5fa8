<template>
  <div
    class="login-container"
    :style="{ backgroundImage: 'url(' + backgroundImage + ')' }"
  >
    <div class="login-card">
      <div class="login-header">
        <div class="logo">
          <img src="../../assets/logo.svg" alt="Logo" class="logo-img" />
          <h1 class="title">妍大网络科技</h1>
        </div>
        <div class="description">欢迎登录，请输入您的账号密码</div>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        autocomplete="on"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="用户名"
            type="text"
            :prefix-icon="User"
            autocomplete="on"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            placeholder="密码"
            :type="passwordVisible ? 'text' : 'password'"
            :prefix-icon="Lock"
            autocomplete="on"
          >
            <template #suffix>
              <el-icon
                class="password-icon"
                @click="passwordVisible = !passwordVisible"
              >
                <component :is="passwordVisible ? View : Hide" />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="loginForm.rememberMe">记住我</el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-button
            :loading="loading"
            type="primary"
            class="login-button"
            @click="handleLogin"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>

      <div class="login-footer">
        <p>© {{ new Date().getFullYear() }} 妍大网络科技</p>
        <p>推荐使用Chrome浏览器访问</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { User, Lock, View, Hide } from "@element-plus/icons-vue";
import type { FormInstance } from "element-plus";
import { login } from "@/api/modules/auth";
// 使用console代替ApiLogger
// import { ApiLogger } from "@/api/utils/logger";
// import { formatApiError } from "@/api/utils/response-handler";

// 背景图片 - 可以替换为实际的背景图URL
const backgroundImage = ref("./background.jpg");

// 表单引用
const loginFormRef = ref<FormInstance>();

// 路由
const router = useRouter();

// 表单数据
const loginForm = reactive({
  username: "",
  password: "",
  rememberMe: false,
});

// 密码可见性
const passwordVisible = ref(false);

// 加载状态
const loading = ref(false);

// 表单校验规则
const loginRules = {
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    { min: 3, message: "用户名不能少于3个字符", trigger: "blur" },
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, message: "密码不能少于6个字符", trigger: "blur" },
  ],
};

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return;

  loginFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true;

      try {
        // 准备登录数据
        const loginData = {
          username: loginForm.username.trim(),
          password: loginForm.password,
          remember: loginForm.rememberMe,
        };

        console.info("开始登录请求");

        // 调用登录API
        const response = await login(loginData);

        console.info("登录成功");

        // 如果"记住我"被选中，设置记住的用户名
        if (loginForm.rememberMe) {
          localStorage.setItem("rememberedUsername", loginForm.username.trim());
        } else {
          localStorage.removeItem("rememberedUsername");
        }

        // 登录成功，跳转到首页
        ElMessage.success("登录成功");
        router.push("/dashboard");
      } catch (error: any) {
        console.error("登录失败:", error);

        // 简单处理错误消息
        const errorMessage = error.message || "未知错误";
        ElMessage.error(errorMessage || "登录失败，请检查用户名和密码");
      } finally {
        loading.value = false;
      }
    } else {
      console.warn("登录表单验证失败");
      return false;
    }
  });
};

// 在组件挂载时检查是否有记住的用户名
onMounted(() => {
  const rememberedUsername = localStorage.getItem("rememberedUsername");
  if (rememberedUsername) {
    loginForm.username = rememberedUsername;
    loginForm.rememberMe = true;
  }
});
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-size: cover;
  background-position: center;
  background-color: #f5f7fa;
}

.login-card {
  width: 400px;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.login-header {
  margin-bottom: 30px;
  text-align: center;
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.logo-img {
  width: 80px;
  height: 80px;
  margin-bottom: 15px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.description {
  font-size: 14px;
  color: #606266;
}

.login-form {
  margin-bottom: 20px;
}

.login-button {
  width: 100%;
  padding: 12px 0;
  font-size: 16px;
}

.password-icon {
  cursor: pointer;
}

.login-footer {
  text-align: center;
  font-size: 12px;
  color: #909399;
  margin-top: 30px;

  p {
    margin: 5px 0;
  }
}

@media (max-width: 480px) {
  .login-card {
    width: 90%;
    padding: 30px;
  }
}
</style>
