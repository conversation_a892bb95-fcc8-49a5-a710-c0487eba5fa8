<template>
  <div class="page-header">
    <div class="title-section">
      <h1 class="page-title">{{ title }}</h1>
      <slot name="title-extra"></slot>
    </div>
    <div class="action-section">
      <slot name="actions"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  /**
   * 页面标题
   */
  title: {
    type: String,
    required: true
  }
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-section {
  display: flex;
  align-items: center;
}

.page-title {
  font-size: 24px;
  color: var(--el-text-color-primary);
  margin: 0;
  margin-right: 12px;
}
</style>
