<template>
  <div class="sales-edit-container">
    <div class="page-header">
      <h1 class="page-title">{{ isEdit ? '编辑销售数据' : '新增销售数据' }}</h1>
      <el-button @click="goBack">
        <el-icon><Back /></el-icon>返回列表
      </el-button>
    </div>

    <el-card class="form-card">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-position="right"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="销售日期" prop="date">
              <el-date-picker
                v-model="form.date"
                type="date"
                placeholder="选择日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="销售员" prop="salesperson">
              <el-select v-model="form.salesperson" placeholder="选择销售员" style="width: 100%">
                <el-option v-for="item in salespersonOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="客户名称" prop="customerName">
              <el-input v-model="form.customerName" placeholder="请输入客户名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户联系人" prop="contactPerson">
              <el-input v-model="form.contactPerson" placeholder="请输入联系人姓名" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户类型" prop="customerType">
              <el-select v-model="form.customerType" placeholder="选择客户类型" style="width: 100%">
                <el-option label="新客户" value="new" />
                <el-option label="老客户" value="returning" />
                <el-option label="VIP客户" value="vip" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="center">产品信息</el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="form.productName" placeholder="请输入产品名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品类别" prop="productCategory">
              <el-select v-model="form.productCategory" placeholder="选择产品类别" style="width: 100%">
                <el-option label="会员服务" value="membership" />
                <el-option label="技术支持" value="technical" />
                <el-option label="广告服务" value="advertising" />
                <el-option label="数据服务" value="data" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="数量" prop="quantity">
              <el-input-number v-model="form.quantity" :min="1" :precision="0" @change="calculateTotal" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单价(元)" prop="unitPrice">
              <el-input-number v-model="form.unitPrice" :min="0" :precision="2" @change="calculateTotal" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="总金额(元)" prop="totalAmount">
              <el-input-number v-model="form.totalAmount" :min="0" :precision="2" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="付款方式" prop="paymentMethod">
              <el-select v-model="form.paymentMethod" placeholder="选择付款方式" style="width: 100%">
                <el-option label="现金" value="cash" />
                <el-option label="银行转账" value="bank" />
                <el-option label="微信支付" value="wechat" />
                <el-option label="支付宝" value="alipay" />
                <el-option label="信用卡" value="credit" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="付款状态" prop="paymentStatus">
              <el-select v-model="form.paymentStatus" placeholder="选择付款状态" style="width: 100%">
                <el-option label="未支付" value="unpaid" />
                <el-option label="部分支付" value="partial" />
                <el-option label="已支付" value="paid" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitting">保存</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Back } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'

// 定义销售表单数据接口
interface SalesForm {
  id: number | undefined
  date: string
  salesperson: string
  customerName: string
  contactPerson: string
  contactPhone: string
  customerType: string
  productName: string
  productCategory: string
  quantity: number
  unitPrice: number
  totalAmount: number
  paymentMethod: string
  paymentStatus: string
  remark: string
}

// 定义销售员选项接口
interface SalespersonOption {
  value: string
  label: string
}

const router = useRouter()
const route = useRoute()
const formRef = ref<FormInstance>()
const submitting = ref(false)

// 判断是新增还是编辑
const isEdit = computed(() => {
  return route.params.id !== undefined
})

// 销售员选项
const salespersonOptions: SalespersonOption[] = [
  { value: 'zhangsan', label: '张三' },
  { value: 'lisi', label: '李四' },
  { value: 'wangwu', label: '王五' }
]

// 表单数据
const form = reactive<SalesForm>({
  id: undefined,
  date: '',
  salesperson: '',
  customerName: '',
  contactPerson: '',
  contactPhone: '',
  customerType: '',
  productName: '',
  productCategory: '',
  quantity: 1,
  unitPrice: 0,
  totalAmount: 0,
  paymentMethod: '',
  paymentStatus: '',
  remark: ''
})

// 表单验证规则
const rules = reactive<FormRules>({
  date: [
    { required: true, message: '请选择销售日期', trigger: 'blur' }
  ],
  salesperson: [
    { required: true, message: '请选择销售员', trigger: 'change' }
  ],
  customerName: [
    { required: true, message: '请输入客户名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  contactPerson: [
    { required: true, message: '请输入联系人', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  customerType: [
    { required: true, message: '请选择客户类型', trigger: 'change' }
  ],
  productName: [
    { required: true, message: '请输入产品名称', trigger: 'blur' }
  ],
  productCategory: [
    { required: true, message: '请选择产品类别', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '数量必须大于0', trigger: 'blur' }
  ],
  unitPrice: [
    { required: true, message: '请输入单价', trigger: 'blur' },
    { type: 'number', min: 0, message: '单价必须大于等于0', trigger: 'blur' }
  ],
  paymentMethod: [
    { required: true, message: '请选择付款方式', trigger: 'change' }
  ],
  paymentStatus: [
    { required: true, message: '请选择付款状态', trigger: 'change' }
  ]
})

// 计算总金额
const calculateTotal = () => {
  form.totalAmount = form.quantity * form.unitPrice
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate((valid, fields) => {
    if (valid) {
      submitting.value = true

      // 这里应该调用API保存数据
      setTimeout(() => {
        ElMessage.success('保存成功')
        submitting.value = false
        goBack()
      }, 1000)
    } else {
      console.error('表单验证失败:', fields)
    }
  })
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 返回列表
const goBack = () => {
  router.push('/sales/list')
}

// 获取编辑数据
const getEditData = () => {
  if (isEdit.value) {
    const id = route.params.id
    // 这里应该调用API获取数据，目前使用模拟数据
    if (id === '1') {
      Object.assign(form, {
        id: 1,
        date: '2023-04-01',
        salesperson: 'zhangsan',
        customerName: '北京科技有限公司',
        contactPerson: '张经理',
        contactPhone: '***********',
        customerType: 'returning',
        productName: '高级会员服务',
        productCategory: 'membership',
        quantity: 1,
        unitPrice: 9800.00,
        totalAmount: 9800.00,
        paymentMethod: 'bank',
        paymentStatus: 'paid',
        remark: '年度服务续费'
      })
    }
  }
}

// 页面加载时获取数据
onMounted(() => {
  if (isEdit.value) {
    getEditData()
  }
})
</script>

<style scoped>
.sales-edit-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  color: #303133;
  margin: 0;
}

.form-card {
  margin-bottom: 20px;
}
</style>