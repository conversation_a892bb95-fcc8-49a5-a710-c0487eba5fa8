/**
 * 角色管理API
 * 提供角色相关的API接口
 */

import { get, post, put, del } from "../core/http";
import type { PaginationParams, PaginatedResponse } from "../types";

/**
 * 角色查询参数
 */
export interface RoleQueryParams extends PaginationParams {
  name?: string;
  code?: string;
  status?: string;
}

/**
 * 角色信息
 */
export interface RoleInfo {
  id: number;
  name: string;
  code: string;
  description?: string;
  status: string;
  permissions?: number[];
  created_at: string;
  updated_at: string;
}

/**
 * 角色列表响应
 */
export interface RoleListResponse {
  items: RoleInfo[];
  total: number;
}

/**
 * 角色详情响应
 */
export interface RoleDetailResponse extends RoleInfo {
  // 可以添加详情特有的字段
  permission_details?: PermissionInfo[];
}

/**
 * 权限信息
 */
export interface PermissionInfo {
  id: number;
  name: string;
  code: string;
  description?: string;
}

/**
 * 创建角色参数
 */
export interface CreateRoleParams {
  name: string;
  code: string;
  description?: string;
  status: string;
  permissions?: number[];
}

/**
 * 更新角色参数
 */
export interface UpdateRoleParams {
  name?: string;
  code?: string;
  description?: string;
  status?: string;
  permissions?: number[];
}

/**
 * 获取角色列表
 * @param params 查询参数
 * @returns 角色列表
 */
export const getRoleList = (params?: RoleQueryParams): Promise<RoleListResponse> => {
  return get("/role", params);
};

/**
 * 获取角色详情
 * @param id 角色ID
 * @returns 角色详情
 */
export const getRoleDetail = (id: number): Promise<RoleDetailResponse> => {
  return get(`/role/${id}`);
};

/**
 * 创建角色
 * @param data 角色数据
 * @returns 创建结果
 */
export const createRole = (data: CreateRoleParams): Promise<RoleInfo> => {
  return post("/role", data);
};

/**
 * 更新角色
 * @param id 角色ID
 * @param data 角色数据
 * @returns 更新结果
 */
export const updateRole = (id: number, data: UpdateRoleParams): Promise<RoleInfo> => {
  return put(`/role/${id}`, data);
};

/**
 * 删除角色
 * @param id 角色ID
 * @returns 删除结果
 */
export const deleteRole = (id: number): Promise<void> => {
  return del(`/role/${id}`);
};

/**
 * 获取所有权限列表
 * @returns 权限列表
 */
export const getAllPermissions = (): Promise<PermissionInfo[]> => {
  return get("/permissions");
};
