<template>
  <div class="dashboard-container">
    <div class="page-header">
      <h1 class="page-title">仪表盘</h1>
      <div class="date-selector">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :shortcuts="dateShortcuts"
          @change="handleDateChange"
        />
      </div>
    </div>

    <el-row :gutter="20">
      <el-col :xs="24" :sm="12" :md="6" v-for="(card, index) in dataCards" :key="index">
        <el-card class="data-card" :class="card.type">
          <div class="card-title">{{ card.title }}</div>
          <div class="card-content">
            <span class="value">{{ card.value }}</span>
            <span class="unit">{{ card.unit }}</span>
          </div>
          <div class="card-footer">
            <span :class="['trend', card.trendType]">
              <el-icon v-if="card.trendType === 'up'"><ArrowUp /></el-icon>
              <el-icon v-else-if="card.trendType === 'down'"><ArrowDown /></el-icon>
              {{ card.trend }}
            </span>
            <span class="compare">较上期</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :lg="16">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>销售趋势</span>
              <el-radio-group v-model="salesTrendType" size="small">
                <el-radio-button value="week">本周</el-radio-button>
                <el-radio-button value="month">本月</el-radio-button>
                <el-radio-button value="quarter">本季度</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container" id="salesTrendChart"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="8">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>销售分类</span>
              <el-select v-model="categoryChartType" size="small" style="width: 120px">
                <el-option label="按产品分类" value="product" />
                <el-option label="按客户类型" value="customer" />
                <el-option label="按销售员" value="salesperson" />
              </el-select>
            </div>
          </template>
          <div class="chart-container" id="categoryChart"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="table-row">
      <el-col :xs="24" :lg="12">
        <el-card class="table-card">
          <template #header>
            <div class="card-header">
              <span>待处理任务</span>
              <el-button type="primary" link>查看全部</el-button>
            </div>
          </template>
          <el-table :data="pendingTasks" style="width: 100%">
            <el-table-column label="任务类型" width="100">
              <template #default="{ row }">
                <el-tag :type="getTaskTypeTag(row.type)">{{ getTaskTypeText(row.type) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="title" label="任务内容" show-overflow-tooltip />
            <el-table-column prop="deadline" label="截止日期" width="100" />
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button type="primary" link size="small">处理</el-button>
                <el-button type="info" link size="small">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="12">
        <el-card class="table-card">
          <template #header>
            <div class="card-header">
              <span>最近登录</span>
              <el-button type="primary" link>查看日志</el-button>
            </div>
          </template>
          <el-table :data="recentLogins" style="width: 100%">
            <el-table-column width="50">
              <template #default="{ row }">
                <el-avatar :size="32" :src="row.avatar">{{ row.username.substring(0, 1) }}</el-avatar>
              </template>
            </el-table-column>
            <el-table-column prop="username" label="用户" width="100" />
            <el-table-column prop="role" label="角色" width="100" />
            <el-table-column prop="loginTime" label="登录时间" />
            <el-table-column prop="ip" label="IP地址" width="120" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'

// 日期范围选择器
const dateRange = ref([])
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]

// 数据卡片
const dataCards = ref([
  {
    title: '销售总额',
    value: '89,762',
    unit: '元',
    trend: '12.5%',
    trendType: 'up',
    type: 'primary'
  },
  {
    title: '订单总数',
    value: '235',
    unit: '笔',
    trend: '5.8%',
    trendType: 'up',
    type: 'success'
  },
  {
    title: '新增客户',
    value: '48',
    unit: '位',
    trend: '8.2%',
    trendType: 'up',
    type: 'warning'
  },
  {
    title: '充值金额',
    value: '56,325',
    unit: '元',
    trend: '3.6%',
    trendType: 'down',
    type: 'danger'
  }
])

// 销售趋势图表类型
const salesTrendType = ref('month')
// 分类图表类型
const categoryChartType = ref('product')

// 待处理任务
const pendingTasks = ref([
  {
    id: 1,
    type: 'approval',
    title: '销售订单审批 - 上海电子科技有限公司',
    deadline: '2023-04-10'
  },
  {
    id: 2,
    type: 'payment',
    title: '待确认付款 - 北京科技有限公司',
    deadline: '2023-04-12'
  },
  {
    id: 3,
    type: 'follow',
    title: '客户跟进 - 广州信息技术有限公司',
    deadline: '2023-04-15'
  },
  {
    id: 4,
    type: 'report',
    title: '月度销售报表提交',
    deadline: '2023-04-30'
  }
])

// 最近登录记录
const recentLogins = ref([
  {
    id: 1,
    username: '张三',
    role: '销售员',
    avatar: '',
    loginTime: '2023-04-09 08:45',
    ip: '*************'
  },
  {
    id: 2,
    username: '李四',
    role: '销售员',
    avatar: '',
    loginTime: '2023-04-09 09:12',
    ip: '*************'
  },
  {
    id: 3,
    username: '王五',
    role: '销售员',
    avatar: '',
    loginTime: '2023-04-09 08:30',
    ip: '*************'
  },
  {
    id: 4,
    username: '赵经理',
    role: '销售经理',
    avatar: '',
    loginTime: '2023-04-09 10:05',
    ip: '*************'
  },
  {
    id: 5,
    username: '王财务',
    role: '财务',
    avatar: '',
    loginTime: '2023-04-09 11:20',
    ip: '*************'
  }
])

// 任务类型标签样式
const getTaskTypeTag = (type: string) => {
  const map: Record<string, string> = {
    'approval': 'warning',
    'payment': 'success',
    'follow': 'info',
    'report': 'primary'
  }
  return map[type] || ''
}

// 任务类型文本
const getTaskTypeText = (type: string) => {
  const map: Record<string, string> = {
    'approval': '审批',
    'payment': '付款',
    'follow': '跟进',
    'report': '报表'
  }
  return map[type] || '其他'
}

// 日期变化处理
const handleDateChange = (val: any) => {
  console.log('日期范围变化:', val)
  // 这里应该调用API重新获取数据
}

// 初始化销售趋势图表
let salesTrendChart: echarts.ECharts | null = null
const initSalesTrendChart = () => {
  const chartDom = document.getElementById('salesTrendChart')
  if (!chartDom) return

  salesTrendChart = echarts.init(chartDom)

  // 这里应该通过API获取数据，目前使用模拟数据
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['销售额', '订单数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: [
      {
        type: 'value',
        name: '销售额',
        axisLabel: {
          formatter: '{value} 元'
        }
      },
      {
        type: 'value',
        name: '订单数',
        axisLabel: {
          formatter: '{value} 笔'
        }
      }
    ],
    series: [
      {
        name: '销售额',
        type: 'bar',
        data: [12500, 8700, 10200, 13400, 9800, 11300, 14500]
      },
      {
        name: '订单数',
        type: 'line',
        yAxisIndex: 1,
        data: [32, 25, 28, 35, 29, 33, 42]
      }
    ]
  }

  salesTrendChart.setOption(option)
}

// 初始化分类图表
let categoryChart: echarts.ECharts | null = null
const initCategoryChart = () => {
  const chartDom = document.getElementById('categoryChart')
  if (!chartDom) return

  categoryChart = echarts.init(chartDom)

  // 这里应该通过API获取数据，目前使用模拟数据
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: ['会员服务', '技术支持', '广告服务', '数据服务', '其他']
    },
    series: [
      {
        name: '销售分类',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 42000, name: '会员服务' },
          { value: 18000, name: '技术支持' },
          { value: 12000, name: '广告服务' },
          { value: 15000, name: '数据服务' },
          { value: 2762, name: '其他' }
        ]
      }
    ]
  }

  categoryChart.setOption(option)
}

// 监听窗口大小变化，重新调整图表大小
const handleResize = () => {
  salesTrendChart?.resize()
  categoryChart?.resize()
}

// 页面加载完成后初始化图表
onMounted(() => {
  nextTick(() => {
    initSalesTrendChart()
    initCategoryChart()

    window.addEventListener('resize', handleResize)
  })
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  color: #303133;
  margin: 0;
}

.data-card {
  height: 120px;
  margin-bottom: 20px;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.data-card.primary {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: white;
}

.data-card.success {
  background: linear-gradient(135deg, #52c41a, #389e0d);
  color: white;
}

.data-card.warning {
  background: linear-gradient(135deg, #faad14, #d48806);
  color: white;
}

.data-card.danger {
  background: linear-gradient(135deg, #f5222d, #cf1322);
  color: white;
}

.card-title {
  font-size: 16px;
  margin-bottom: 8px;
}

.card-content {
  margin-bottom: 12px;
}

.card-content .value {
  font-size: 28px;
  font-weight: bold;
  margin-right: 4px;
}

.card-content .unit {
  font-size: 14px;
}

.card-footer {
  font-size: 12px;
}

.trend {
  display: inline-flex;
  align-items: center;
  margin-right: 8px;
}

.trend.up {
  color: #52c41a;
}

.trend.down {
  color: #ff4d4f;
}

.chart-row, .table-row {
  margin-bottom: 20px;
}

.chart-card, .table-card {
  margin-bottom: 20px;
  height: 100%;
}

.chart-header, .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 350px;
}
</style>