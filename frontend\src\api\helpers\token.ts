/**
 * 令牌管理
 * 提供令牌的存储、获取和刷新功能
 */

import axios from 'axios';
import { API_CONFIG } from '../config';

// 存储键
const TOKEN_KEY = 'token';
const REFRESH_TOKEN_KEY = 'refresh_token';
const TOKEN_EXPIRY_KEY = 'token_expiry';
const USER_INFO_KEY = 'user_info';

// 令牌刷新阈值（毫秒）
const REFRESH_THRESHOLD_MS = 5 * 60 * 1000; // 5分钟

/**
 * 安全地存储数据到localStorage
 * @param key 存储键
 * @param value 存储值
 */
const secureStore = (key: string, value: string): void => {
  try {
    localStorage.setItem(key, value);
  } catch (error) {
    console.error(`存储数据失败: ${key}`, error);
  }
};

/**
 * 安全地从localStorage获取数据
 * @param key 存储键
 * @returns 存储的值或null
 */
const secureRetrieve = (key: string): string | null => {
  try {
    return localStorage.getItem(key);
  } catch (error) {
    console.error(`获取数据失败: ${key}`, error);
    return null;
  }
};

/**
 * 安全地从localStorage移除数据
 * @param key 存储键
 */
const secureRemove = (key: string): void => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error(`移除数据失败: ${key}`, error);
  }
};

/**
 * 存储认证令牌
 * @param token 访问令牌
 * @param refreshToken 刷新令牌
 * @param expiresIn 过期时间（秒）
 */
export const storeTokens = (
  token: string,
  refreshToken?: string,
  expiresIn?: number
): void => {
  if (!token) {
    console.error('存储令牌失败: 令牌为空');
    return;
  }

  // 存储访问令牌
  secureStore(TOKEN_KEY, token);

  // 存储刷新令牌（如果有）
  if (refreshToken) {
    secureStore(REFRESH_TOKEN_KEY, refreshToken);
  }

  // 计算并存储过期时间
  if (expiresIn) {
    const expiryTime = Date.now() + expiresIn * 1000;
    secureStore(TOKEN_EXPIRY_KEY, expiryTime.toString());
  }
};

/**
 * 存储用户信息
 * @param userInfo 用户信息
 */
export const storeUserInfo = (userInfo: any): void => {
  if (!userInfo) {
    console.error('存储用户信息失败: 用户信息为空');
    return;
  }

  try {
    secureStore(USER_INFO_KEY, JSON.stringify(userInfo));
  } catch (error) {
    console.error('存储用户信息失败', error);
  }
};

/**
 * 获取用户信息
 * @returns 用户信息或null
 */
export const getUserInfo = (): any | null => {
  const userInfoStr = secureRetrieve(USER_INFO_KEY);
  if (!userInfoStr) {
    return null;
  }

  try {
    return JSON.parse(userInfoStr);
  } catch (error) {
    console.error('解析用户信息失败', error);
    return null;
  }
};

/**
 * 获取认证令牌
 * @returns 访问令牌或null
 */
export const getToken = (): string | null => {
  return secureRetrieve(TOKEN_KEY);
};

/**
 * 获取刷新令牌
 * @returns 刷新令牌或null
 */
export const getRefreshToken = (): string | null => {
  return secureRetrieve(REFRESH_TOKEN_KEY);
};

/**
 * 清除所有认证信息
 */
export const clearTokens = (): void => {
  secureRemove(TOKEN_KEY);
  secureRemove(REFRESH_TOKEN_KEY);
  secureRemove(TOKEN_EXPIRY_KEY);
  secureRemove(USER_INFO_KEY);
};

/**
 * 检查令牌是否即将过期
 * @returns 是否即将过期
 */
export const isTokenExpiringSoon = (): boolean => {
  const expiryTimeStr = secureRetrieve(TOKEN_EXPIRY_KEY);
  if (!expiryTimeStr) {
    return false;
  }

  try {
    const expiryTime = parseInt(expiryTimeStr, 10);
    const now = Date.now();
    const timeUntilExpiry = expiryTime - now;

    // 如果过期时间小于阈值，则认为即将过期
    return timeUntilExpiry < REFRESH_THRESHOLD_MS;
  } catch (error) {
    console.error('解析令牌过期时间失败', error);
    return false;
  }
};

/**
 * 检查令牌是否已过期
 * @returns 是否已过期
 */
export const isTokenExpired = (): boolean => {
  const expiryTimeStr = secureRetrieve(TOKEN_EXPIRY_KEY);
  if (!expiryTimeStr) {
    return false;
  }

  try {
    const expiryTime = parseInt(expiryTimeStr, 10);
    const now = Date.now();
    return now >= expiryTime;
  } catch (error) {
    console.error('解析令牌过期时间失败', error);
    return false;
  }
};

/**
 * 刷新访问令牌
 * @returns Promise<boolean> 是否刷新成功
 */
export const refreshToken = async (): Promise<boolean> => {
  const refreshToken = getRefreshToken();
  if (!refreshToken) {
    console.warn('无法刷新令牌: 刷新令牌不存在');
    return false;
  }

  try {
    console.info('开始刷新令牌');

    const response = await axios.post(`${API_CONFIG.baseURL}/auth/refresh-token/`, {
      refresh_token: refreshToken
    });

    if (response.data && response.data.access_token) {
      const { access_token, refresh_token, expires_in } = response.data;

      // 存储新的令牌
      storeTokens(access_token, refresh_token, expires_in);

      console.info('令牌刷新成功');
      return true;
    } else {
      console.error('令牌刷新失败: 响应格式错误', response.data);
      return false;
    }
  } catch (error) {
    console.error('令牌刷新失败', error);
    return false;
  }
};

export default {
  storeTokens,
  storeUserInfo,
  getUserInfo,
  getToken,
  getRefreshToken,
  clearTokens,
  isTokenExpiringSoon,
  isTokenExpired,
  refreshToken
};
