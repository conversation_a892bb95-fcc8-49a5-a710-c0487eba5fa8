# 10-框架统一流程引擎接口规范

## 1. 概述

本文档定义了SIMBEST框架统一流程引擎的核心接口规范，包括工作项操作、流程实例控制、流程通知和审批意见等四个主要接口。这些接口为流程引擎提供了标准化的操作规范，支持多种工作流引擎的实现。

## 2. 核心接口定义

### 2.1 工作项操作接口 (IWorkItemService)

工作项操作接口定义了流程任务相关的核心操作，包括任务完成、任务分配、任务查询等功能。

#### 2.1.1 任务完成相关
- `finishWorkItemWithRelativeData`: 完成指定工作项并携带流程相关数据
- `finishWorkTaskWithRelativeData`: 完成工作任务并携带流程相关数据
- `finshTaskWithComplete`: 根据环节配置属性进行流转下一步
- `finishWorkItem`: 完成工作项

#### 2.1.2 任务分配相关
- `reassignWorkItem`: 将工作项改派给指定参与者
- `addWorkItemParticipant`: 新增工作项的参与者
- `addWorkItem`: 动态增加活动实例的工作项
- `reassignWorkByActivity`: 将工作项改派给指定参与者(Activity引擎)

#### 2.1.3 任务查询相关
- `getByProInstIdAAndAInstId`: 根据流程实例ID和活动实例ID查询工作项
- `queryWorkTtemDataByProInsId`: 根据流程实例ID查询工作项信息
- `queryWorkItems`: 根据流程实例ID查询流程跟踪
- `queryWorkITtemDataMap`: 根据参数查询工作项信息
- `queryTaskDataMap`: 根据参数查询任务数据

#### 2.1.4 任务状态管理
- `updateWorkItemStatusById`: 更新工作项状态
- `updateWorkItemInfo`: 更新流程跟踪信息
- `endProcess`: 结束流程
- `endTask`: 结束工作项(多实例)

### 2.2 流程实例控制接口 (IProcessInstanceService)

流程实例控制接口定义了流程实例的生命周期管理，包括启动、终止、查询等功能。

#### 2.2.1 流程启动相关
- `startProcessAndSetRelativeDataNormal`: 启动流程并设置相关数据(标准方式)
- `startProcessAndSetRelativeData`: 启动流程并设置相关数据
- `startProcessAndTran`: 启动流程(带事务分割)
- `startProcess`: 启动流程(基础方式)
- `startProcessAndDeployProcessAndNoSetRelativeData`: 启动流程并部署(无数据)
- `startProcessAndDeployProcessAndSetRelativeData`: 启动流程并部署(带数据)

#### 2.2.2 流程终止相关
- `deleteProcessInstance`: 删除流程实例
- `cancelProcessInst`: 注销流程实例
- `terminateProcessInst`: 终止流程实例

#### 2.2.3 流程查询相关
- `getProInstDataByProInstIdLocal`: 查询流程跟踪信息
- `queryProcessInstacesDataByProInsIdApi`: 查询流程实例信息
- `queryProcessInstacesDataByProDefKeyApi`: 根据流程定义KEY查询实例
- `queryProcessInstacesDataByActiveApi`: 查询活动状态流程实例
- `getDiagram`: 获取流程图片

### 2.3 流程通知接口 (IWFNotificationService)

流程通知接口定义了流程相关的通知功能，包括待阅、已阅等通知管理。

#### 2.3.1 通知发送相关
- `sendProcessInstNotification`: 发送流程实例通知
- `sendActivityInstNotification`: 发送活动实例通知
- `sendTaskNotificationToPerson`: 发送任务通知

#### 2.3.2 通知查询相关
- `queryUnViewedProcessInstNotifications`: 查询未确认的流程实例通知
- `queryUnViewedActivityInstNotifications`: 查询未确认的活动实例通知
- `getWFNotificationInstListByProcessInstId`: 查询流程实例通知列表

#### 2.3.3 通知状态管理
- `confirmNotification`: 确认(阅读)通知
- `updateNotificationStatus`: 更新通知状态
- `deleteNotification`: 删除通知

### 2.4 审批意见接口 (IWfOptMsgService)

审批意见接口定义了流程审批过程中的意见管理功能。

#### 2.4.1 审批意见查询
- `getByProInsIdOptMsgs`: 查询流程审批意见
- `queryProcessOptMsgDataMap`: 查询流程审批意见(含子流程)
- `getByProInsIdOptMsgsSubFlow`: 查询子流程审批意见
- `queryComments`: 查询流程审批意见

#### 2.4.2 审批意见管理
- `submitApprovalMsg`: 提交审批意见
- `updateWorkOptMstInfo`: 更新审批意见
- `updateOptMsgByProInsIdWorkItemId`: 更新审批意见状态

## 3. 接口实现规范

### 3.1 通用规范
- 所有接口方法必须提供完整的参数说明和返回值说明
- 接口方法命名应遵循驼峰命名规范
- 接口方法应提供必要的参数校验
- 接口方法应处理异常情况并返回适当的错误信息

### 3.2 事务管理
- 涉及数据修改的操作必须支持事务管理
- 提供事务分割选项，支持灵活的事务控制
- 事务回滚时应保证数据一致性

### 3.3 数据一致性
- 流程状态变更时应保证相关数据同步更新
- 子流程操作时应保证与主流程数据的一致性
- 审批意见应与流程节点状态保持同步

## 4. 扩展性说明

### 4.1 接口扩展
- 接口设计支持后续功能扩展
- 新增方法应保持与现有方法的命名风格一致
- 扩展方法应提供完整的文档说明

### 4.2 实现扩展
- 支持多种工作流引擎的实现
- 提供统一的接口规范，便于切换不同的实现
- 实现类应遵循接口定义的标准

## 5. 安全规范

### 5.1 访问控制
- 接口调用需要进行权限验证
- 敏感操作需要记录操作日志
- 数据访问需要进行安全控制

### 5.2 数据安全
- 传输数据需要进行加密处理
- 敏感信息需要进行脱敏处理
- 数据存储需要符合安全规范

## 6. 版本控制

### 6.1 版本管理
- 接口版本变更需要保持向后兼容
- 重大变更需要提供迁移方案
- 版本号遵循语义化版本规范

### 6.2 兼容性
- 新版本应兼容旧版本的主要功能
- 废弃的方法需要提供替代方案
- 接口变更需要提供详细的说明文档 