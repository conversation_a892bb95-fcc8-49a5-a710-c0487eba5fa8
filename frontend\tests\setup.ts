// Vitest 测试环境设置

import { config } from '@vue/test-utils'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { beforeEach, afterEach } from 'vitest'

// 全局挂载 Element Plus
config.global.plugins = [ElementPlus]

// 全局挂载图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  config.global.components[key] = component
}

// 模拟本地存储
const localStorageMock = {
  store: {} as Record<string, string>,
  getItem(key: string) {
    return this.store[key] || null
  },
  setItem(key: string, value: string) {
    this.store[key] = value.toString()
  },
  removeItem(key: string) {
    delete this.store[key]
  },
  clear() {
    this.store = {}
  },
  key(index: number): string | null {
    return Object.keys(this.store)[index] || null
  },
  get length(): number {
    return Object.keys(this.store).length
  }
}

// 替换全局对象
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// 添加自定义匹配器或其他测试辅助函数
// ...

// 必要的测试前准备
beforeEach(() => {
  // 每个测试前清空本地存储
  localStorage.clear()
})

// 必要的测试后清理
afterEach(() => {
  // 测试后的清理工作
}) 