/**
 * 报表查询参数接口
 */
export interface ReportParams {
  start_date?: string
  end_date?: string
  channel?: string
  platform?: string
  page?: number
  page_size?: number
}

/**
 * 销售报表数据接口
 */
export interface SalesReportData {
  items: SalesReportItem[]
  total: number
  summary: {
    totalSales: number
    totalProfit: number
    totalOrders: number
    avgOrderAmount: number
  }
  trend_data: {
    dates: string[]
    sales: number[]
    profits: number[]
  }
  channel_data: {
    channels: string[]
    data: Array<{value: number, name: string}>
  }
  platform_data: {
    platforms: string[]
    sales: number[]
    profits: number[]
  }
  person_data: {
    persons: string[]
    max: number
    sales: number[]
    profits: number[]
  }
}

/**
 * 销售报表项目接口
 */
export interface SalesReportItem {
  id: number
  transfer_time: string
  channel_sales_name: string
  person_in_charge: string
  business_platform: string
  currency: number
  port_policy: number
  customer_policy: number
  port_recharge: number
  receivable_amount: number
  profit: number
}

/**
 * 稽核报表数据接口
 */
export interface AuditReportData {
  items: AuditReportItem[]
  total: number
  summary: {
    matchRate: number
    matchRateTrend: string
    matchRateChange: number
    matchedCount: number
    matchedRate: number
    unmatchedCount: number
    unmatchedRate: number
    exceptionAmount: number
    exceptionRate: number
  }
  overview_data: {
    matched: number
    sales_no_match: number
    recharge_no_match: number
    amount_inconsistent: number
  }
  exception_data: Array<[string, number, number, number]>
  trend_data: {
    dates: string[]
    rates: number[]
  }
}

/**
 * 稽核报表项目接口
 */
export interface AuditReportItem {
  id: number
  date: string
  audit_id: string
  exception_type: string
  account_id: string
  sales_amount: number
  recharge_amount: number
  difference: number
  status: string
  handler: string
  handle_time: string
  remarks: string
}

/**
 * 财务报表数据接口
 */
export interface FinanceReportData {
  items: FinanceReportItem[]
  total: number
  summary: {
    totalSales: number
    salesTrend: string
    salesChange: number
    receivableAmount: number
    receivableRate: number
    actualAmount: number
    actualRate: number
    outstandingAmount: number
    outstandingRate: number
    totalProfit: number
    profitTrend: string
    profitChange: number
    avgProfitRate: number
    avgProfitYoY: number
    topProfitChannel: string
    topProfitRate: number
    lowProfitChannel: string
    lowProfitRate: number
  }
  income_data: {
    dates: string[]
    sales: number[]
    receivable: number[]
    actual: number[]
    rates: number[]
  }
  profit_data: {
    channels: string[]
    sales: number[]
    profits: number[]
    rates: number[]
  }
  debt_data: {
    settled: number
    partial_settled: number
    unsettled: number
    partial_within_30: number
    partial_30_to_90: number
    partial_over_90: number
    unsettled_within_30: number
    unsettled_30_to_90: number
    unsettled_over_90: number
  }
}

/**
 * 财务报表项目接口
 */
export interface FinanceReportItem {
  id: number
  date: string
  channel: string
  platform: string
  sales_amount: number
  receivable_amount: number
  actual_amount: number
  profit: number
  profit_rate: number
  outstanding: number
  status: string
}

/**
 * 导出Excel表头配置
 */
export interface ExcelHeader {
  header: string
  key: string
  width?: number
} 