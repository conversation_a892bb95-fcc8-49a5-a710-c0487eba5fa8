/**
 * 缓存工具
 * 提供API请求缓存功能
 */

import type { InternalAxiosRequestConfig } from "axios";

/**
 * 缓存项
 */
interface CacheItem {
  data: any;
  timestamp: number;
  url: string;
  method: string;
}

// 内存缓存
const memoryCache = new Map<string, CacheItem>();

/**
 * 生成缓存键
 * @param config 请求配置
 * @returns 缓存键
 */
export const generateCacheKey = (
  config: InternalAxiosRequestConfig
): string => {
  const { url, method, params, data } = config;
  const paramsString = params ? JSON.stringify(params) : "";
  const dataString = data ? JSON.stringify(data) : "";
  return `${method}_${url}_${paramsString}_${dataString}`;
};

/**
 * 检查缓存是否有效
 * @param cacheItem 缓存项
 * @param maxAge 最大缓存时间（毫秒）
 * @returns 是否有效
 */
export const isCacheValid = (cacheItem: CacheItem, maxAge: number): boolean => {
  const now = Date.now();
  return now - cacheItem.timestamp < maxAge;
};

/**
 * 从缓存获取响应
 * @param config 请求配置
 * @param maxAge 最大缓存时间（毫秒）
 * @returns 缓存的响应或null
 */
export const getFromCache = (
  config: InternalAxiosRequestConfig,
  maxAge: number = 5 * 60 * 1000
): any | null => {
  // 只缓存GET请求
  const method = config.method?.toLowerCase() || "";
  if (method !== "get") {
    return null;
  }

  const url = config.url || "";

  // 排除不应缓存的URL
  if (url.includes("/auth/") || url.includes("/login")) {
    return null;
  }

  const cacheKey = generateCacheKey(config);
  const cachedItem = memoryCache.get(cacheKey);

  if (cachedItem && isCacheValid(cachedItem, maxAge)) {
    console.debug(`从缓存获取: ${url}`);
    return cachedItem.data;
  }

  return null;
};

/**
 * 将响应存入缓存
 * @param config 请求配置
 * @param data 响应数据
 * @param maxAge 最大缓存时间（毫秒）
 */
export const saveToCache = (
  config: InternalAxiosRequestConfig,
  data: any,
  maxAge: number = 5 * 60 * 1000
): void => {
  // 只缓存GET请求
  const method = config.method?.toLowerCase() || "";
  if (method !== "get") {
    return;
  }

  const url = config.url || "";

  // 排除不应缓存的URL
  if (url.includes("/auth/") || url.includes("/login")) {
    return;
  }

  const cacheKey = generateCacheKey(config);
  memoryCache.set(cacheKey, {
    data,
    timestamp: Date.now(),
    url,
    method,
  });

  console.debug(`缓存响应: ${url}`);
};

/**
 * 清除缓存
 * @param pattern 匹配模式，清除匹配的URL
 */
export const clearCache = (pattern?: RegExp): void => {
  if (pattern) {
    // 清除匹配的缓存
    for (const [key, item] of memoryCache.entries()) {
      if (pattern.test(item.url)) {
        memoryCache.delete(key);
        console.debug(`清除缓存: ${item.url}`);
      }
    }
  } else {
    // 清除所有缓存
    memoryCache.clear();
    console.debug("清除所有缓存");
  }
};

export default {
  getFromCache,
  saveToCache,
  clearCache,
  generateCacheKey,
  isCacheValid,
};
