/**
 * 认证API模块
 * 提供用户认证相关的API请求功能
 */

import { post } from "../core/http";
import { storeTokens, storeUserInfo, clearTokens } from "../helpers/token";
import type {
  LoginParams,
  LoginResponse,
  RefreshTokenResponse,
} from "../types";

/**
 * 用户登录
 * @param params 登录参数
 * @returns Promise<LoginResponse>
 */
export async function login(params: LoginParams): Promise<LoginResponse> {
  const response = await post<LoginResponse>("/auth/login", {
    username: params.username,
    password: params.password,
  });

  // 存储令牌和用户信息
  if (response.access_token) {
    storeTokens(
      response.access_token,
      response.refresh_token,
      response.expires_in
    );

    if (response.user) {
      storeUserInfo(response.user);
    }
  }

  return response;
}

/**
 * 刷新令牌
 * @param refreshToken 刷新令牌
 * @returns Promise<RefreshTokenResponse>
 */
export async function refreshToken(
  refreshToken: string
): Promise<RefreshTokenResponse> {
  const response = await post<RefreshTokenResponse>("/auth/refresh-token/", {
    refresh_token: refreshToken,
  });

  // 存储新令牌
  if (response.access_token) {
    storeTokens(
      response.access_token,
      response.refresh_token,
      response.expires_in
    );
  }

  return response;
}

/**
 * 用户登出
 * @returns Promise<void>
 */
export async function logout(): Promise<void> {
  try {
    await post<void>("/auth/logout/");
  } finally {
    // 无论请求是否成功，都清除本地认证信息
    clearTokens();
  }
}

export default {
  login,
  refreshToken,
  logout,
};
