/**
 * 错误处理
 * 提供API错误处理功能
 */

import { AxiosError } from 'axios';
import { ElMessage, ElMessageBox } from 'element-plus';
import router from '@/router';
import { clearTokens } from './token';

/**
 * 错误类型
 */
export enum ErrorType {
  NETWORK = 'network',
  AUTH = 'auth',
  PERMISSION = 'permission',
  VALIDATION = 'validation',
  SERVER = 'server',
  CLIENT = 'client',
  UNKNOWN = 'unknown'
}

/**
 * 错误处理配置
 */
export interface ErrorHandlerConfig {
  // 是否显示错误消息
  showErrorMessage: boolean;
  // 是否在控制台输出错误
  logError: boolean;
  // 认证错误时是否重定向到登录页
  redirectOnAuthError: boolean;
}

// 默认错误处理配置
export const defaultErrorHandlerConfig: ErrorHandlerConfig = {
  showErrorMessage: true,
  logError: true,
  redirectOnAuthError: true
};

/**
 * 确定错误类型
 * @param error Axios错误
 * @returns 错误类型
 */
export const determineErrorType = (error: AxiosError): ErrorType => {
  if (!error.response) {
    // 网络错误或请求被取消
    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      return ErrorType.NETWORK;
    }
    if (error.message.includes('Network Error')) {
      return ErrorType.NETWORK;
    }
    return ErrorType.CLIENT;
  }

  // 根据HTTP状态码确定错误类型
  const status = error.response.status;

  if (status === 401) {
    return ErrorType.AUTH;
  }

  if (status === 403) {
    return ErrorType.PERMISSION;
  }

  if (status === 400 || status === 422) {
    return ErrorType.VALIDATION;
  }

  if (status >= 500) {
    return ErrorType.SERVER;
  }

  return ErrorType.UNKNOWN;
};

/**
 * 获取错误消息
 * @param error Axios错误
 * @returns 错误消息
 */
export const getErrorMessage = (error: AxiosError): string => {
  // 从响应中提取错误消息
  if (error.response?.data) {
    const data = error.response.data as any;
    
    if (typeof data === 'string') return data;
    if (data.message) return data.message;
    if (data.error) return data.error;
    if (data.detail) return data.detail;
    
    // 处理验证错误
    if (data.errors && Array.isArray(data.errors) && data.errors.length > 0) {
      return data.errors[0];
    }
  }
  
  // 默认错误消息
  const errorType = determineErrorType(error);
  
  switch (errorType) {
    case ErrorType.NETWORK:
      return '网络错误，请检查您的网络连接';
    case ErrorType.AUTH:
      return '认证失败，请重新登录';
    case ErrorType.PERMISSION:
      return '权限不足，无法访问该资源';
    case ErrorType.VALIDATION:
      return '请求数据验证失败，请检查输入';
    case ErrorType.SERVER:
      return '服务器错误，请稍后重试';
    case ErrorType.CLIENT:
      return '客户端错误，请刷新页面重试';
    default:
      return '未知错误，请稍后重试';
  }
};

/**
 * 处理认证错误
 * @param config 错误处理配置
 */
export const handleAuthError = (config: ErrorHandlerConfig = defaultErrorHandlerConfig): void => {
  // 清除认证信息
  clearTokens();

  if (config.redirectOnAuthError) {
    // 显示确认对话框
    ElMessageBox.confirm(
      '您的登录已过期，请重新登录',
      '登录超时',
      {
        confirmButtonText: '重新登录',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      // 重定向到登录页
      router.push('/login');
    }).catch(() => {
      console.info('用户取消了重新登录');
    });
  }
};

/**
 * 处理API错误
 * @param error Axios错误
 * @param config 错误处理配置
 */
export const handleApiError = (
  error: AxiosError,
  config: ErrorHandlerConfig = defaultErrorHandlerConfig
): void => {
  // 确定错误类型
  const errorType = determineErrorType(error);

  // 记录错误
  if (config.logError) {
    console.error(`API错误 [${errorType}]:`, error);
    
    if (error.config) {
      console.error(`请求URL: ${error.config.method?.toUpperCase()} ${error.config.url}`);
    }
    
    if (error.response) {
      console.error(`响应状态: ${error.response.status}`);
      console.error('响应数据:', error.response.data);
    }
  }

  // 处理认证错误
  if (errorType === ErrorType.AUTH) {
    handleAuthError(config);
  }

  // 显示错误消息
  if (config.showErrorMessage) {
    const errorMessage = getErrorMessage(error);
    ElMessage.error(errorMessage);
  }
};

/**
 * 创建自定义错误
 * @param message 错误消息
 * @param code 错误代码
 * @param data 错误数据
 * @returns 自定义错误
 */
export const createApiError = (
  message: string,
  code?: string,
  data?: any
): Error => {
  const error = new Error(message) as any;
  error.isApiError = true;
  error.code = code;
  error.data = data;
  return error;
};

export default {
  ErrorType,
  determineErrorType,
  getErrorMessage,
  handleAuthError,
  handleApiError,
  createApiError,
  defaultErrorHandlerConfig
};
