<template>
  <div class="role-detail-container">
    <PageHeader title="角色详情">
      <template #actions>
        <el-button @click="goBack">返回</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </template>
    </PageHeader>

    <el-card class="detail-card" v-loading="loading">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="角色名称">
          {{ roleDetail.name }}
        </el-descriptions-item>
        <el-descriptions-item label="角色编码">
          {{ roleDetail.code }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="roleDetail.status === 'active' ? 'success' : 'danger'">
            {{ roleDetail.status === 'active' ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatDate(roleDetail.created_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ formatDate(roleDetail.updated_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">
          {{ roleDetail.description || '暂无描述' }}
        </el-descriptions-item>
      </el-descriptions>

      <div class="permission-section">
        <h3>权限列表</h3>
        <el-divider />
        <div v-if="roleDetail.permission_details && roleDetail.permission_details.length">
          <el-tag
            v-for="permission in roleDetail.permission_details"
            :key="permission.id"
            class="permission-tag"
          >
            {{ permission.name }}
          </el-tag>
        </div>
        <el-empty v-else description="暂无权限" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { PageHeader } from '@/components';
import { getRoleDetail } from '@/api/modules/role';
import type { RoleDetailResponse } from '@/api/modules/role';

const router = useRouter();
const route = useRoute();

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleString();
};

// 角色ID
const roleId = ref<number>(Number(route.params.id) || 0);
// 角色详情
const roleDetail = reactive<RoleDetailResponse>({
  id: 0,
  name: '',
  code: '',
  status: '',
  description: '',
  created_at: '',
  updated_at: '',
  permission_details: []
});
// 加载状态
const loading = ref(false);

// 获取角色详情
const getDetail = async () => {
  if (!roleId.value) {
    ElMessage.error('角色ID不能为空');
    return;
  }

  loading.value = true;
  try {
    const result = await getRoleDetail(roleId.value);
    if (result) {
      Object.assign(roleDetail, result);
    }
  } catch (error) {
    console.error('获取角色详情失败', error);
    ElMessage.error('获取角色详情失败');
  } finally {
    loading.value = false;
  }
};

// 返回列表页
const goBack = () => {
  router.push('/role/list');
};

// 编辑角色
const handleEdit = () => {
  router.push(`/role/edit/${roleId.value}`);
};

// 页面加载时获取角色详情
onMounted(() => {
  getDetail();
});
</script>

<style scoped>
.role-detail-container {
  padding: 20px;
}

.detail-card {
  margin-top: 20px;
}

.permission-section {
  margin-top: 30px;
}

.permission-tag {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
