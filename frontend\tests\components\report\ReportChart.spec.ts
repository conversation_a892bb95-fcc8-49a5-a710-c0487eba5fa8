import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import * as echarts from 'echarts'

// 模拟 echarts
vi.mock('echarts', () => {
  return {
    init: vi.fn(() => ({
      setOption: vi.fn(),
      resize: vi.fn(),
      dispose: vi.fn()
    })),
    getInstanceByDom: vi.fn(() => null)
  }
})

// 创建一个简单的图表组件用于测试
// 这里我们直接在测试文件里定义，实际项目中应该测试真实的组件
const TrendChart = {
  template: `
    <div>
      <div ref="chartRef" class="chart"></div>
    </div>
  `,
  props: {
    chartData: {
      type: Object,
      required: true
    }
  },
  setup(props: any) {
    const chartRef = ref(null)
    let chartInstance: any = null

    onMounted(() => {
      if (chartRef.value) {
        chartInstance = echarts.init(chartRef.value)
        updateChart()
      }
    })

    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.dispose()
      }
    })

    watch(() => props.chartData, () => {
      updateChart()
    }, { deep: true })

    function updateChart() {
      if (!chartInstance) return

      const option = {
        title: {
          text: '销售趋势'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: props.chartData.dates || []
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '销售额',
            type: 'line',
            data: props.chartData.values || []
          }
        ]
      }

      chartInstance.setOption(option)
    }

    return {
      chartRef
    }
  }
}

// 导入测试中需要的Vue组件
import { ref, onMounted, onUnmounted, watch } from 'vue'

describe('Chart Components', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('TrendChart', () => {
    it('should initialize chart when mounted', async () => {
      const wrapper = mount(TrendChart, {
        props: {
          chartData: {
            dates: ['2023-01-01', '2023-01-02'],
            values: [100, 200]
          }
        }
      })

      await nextTick()

      // 验证echarts.init是否被调用
      expect(echarts.init).toHaveBeenCalled()
    })

    it('should update chart when data changes', async () => {
      const wrapper = mount(TrendChart, {
        props: {
          chartData: {
            dates: ['2023-01-01', '2023-01-02'],
            values: [100, 200]
          }
        }
      })

      await nextTick()

      // 获取更新后的setOption调用
      const mockChartInstance = (echarts.init as any).mock.results[0].value
      const initialSetOptionCalls = mockChartInstance.setOption.mock.calls.length

      // 更新props
      await wrapper.setProps({
        chartData: {
          dates: ['2023-01-01', '2023-01-02', '2023-01-03'],
          values: [100, 200, 300]
        }
      })

      await nextTick()

      // 验证setOption是否再次被调用
      expect(mockChartInstance.setOption.mock.calls.length).toBeGreaterThan(initialSetOptionCalls)
    })

    it('should dispose chart when unmounted', async () => {
      const wrapper = mount(TrendChart, {
        props: {
          chartData: {
            dates: ['2023-01-01', '2023-01-02'],
            values: [100, 200]
          }
        }
      })

      await nextTick()

      // 获取chartInstance
      const mockChartInstance = (echarts.init as any).mock.results[0].value

      // 卸载组件
      wrapper.unmount()

      // 验证dispose是否被调用
      expect(mockChartInstance.dispose).toHaveBeenCalled()
    })
  })
})
