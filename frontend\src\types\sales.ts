/**
 * 销售数据类型定义
 * 从store/sales.ts中提取的类型定义
 */

// 销售数据接口
export interface SalesData {
  id: number;
  transfer_time: string;
  recipient_account_name: string;
  recipient_account_id: string;
  channel_sales_name: string;
  person_in_charge: string;
  business_platform: string;
  transfer_type: string;
  currency: number;
  port_policy: number;
  customer_policy: number;
  port_recharge: number;
  receivable_amount: number;
  reserve_fund: number;
  self_advance: number;
  capital_advance: number;
  profit: number;
  arrival_time?: string;
  arrival_amount?: number;
  arrival_name?: string;
  collector?: string;
  outstanding_amount: number;
  capital_flow?: string;
  remark?: string;
  audit_status: string;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  created_by: number;
  updated_by: number;
  sale_amount: number;
  cost_amount: number;
  product_name: string;
  product_quantity: number;
  sales_person_id?: number;
  sale_time: string;
  creator?: {
    id: number;
    username: string;
    name: string;
  };
  updater?: {
    id: number;
    username: string;
    name: string;
  };
}

// 创建销售数据接口
export interface CreateSalesData {
  transfer_time: string;
  recipient_account_name: string;
  recipient_account_id: string;
  channel_sales_name: string;
  person_in_charge: string;
  business_platform: string;
  transfer_type: string;
  currency: number;
  port_policy: number;
  customer_policy: number;
  port_recharge: number;
  receivable_amount: number;
  reserve_fund: number;
  self_advance: number;
  capital_advance: number;
  profit: number;
  arrival_time?: string;
  arrival_amount?: number;
  arrival_name?: string;
  collector?: string;
  outstanding_amount?: number;
  capital_flow?: string;
  remark?: string;
  sale_amount: number;
  cost_amount: number;
  product_name: string;
  product_quantity: number;
  sales_person_id?: number;
  sale_time: string;
}

// 更新销售数据接口
export interface UpdateSalesData {
  transfer_time?: string;
  recipient_account_name?: string;
  recipient_account_id?: string;
  channel_sales_name?: string;
  person_in_charge?: string;
  business_platform?: string;
  transfer_type?: string;
  currency?: number;
  port_policy?: number;
  customer_policy?: number;
  port_recharge?: number;
  receivable_amount?: number;
  reserve_fund?: number;
  self_advance?: number;
  capital_advance?: number;
  profit?: number;
  arrival_time?: string;
  arrival_amount?: number;
  arrival_name?: string;
  collector?: string;
  outstanding_amount?: number;
  capital_flow?: string;
  remark?: string;
  sale_amount?: number;
  cost_amount?: number;
  product_name?: string;
  product_quantity?: number;
  sales_person_id?: number;
  sale_time?: string;
}

// 查询参数接口
export interface SalesQueryParams {
  skip?: number;
  limit?: number;
  transfer_time_start?: string;
  transfer_time_end?: string;
  channel_sales_name?: string;
  person_in_charge?: string;
  business_platform?: string;
  audit_status?: string;
}
