<template>
  <div class="user-edit-container">
    <PageHeader title="编辑用户">
      <template #actions>
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>返回列表
        </el-button>
      </template>
    </PageHeader>

    <el-card class="form-card" v-loading="loading">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        status-icon
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.username" placeholder="请输入用户名" :disabled="true" />
        </el-form-item>

        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱" />
        </el-form-item>

        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号" />
        </el-form-item>

        <el-form-item label="角色" prop="role">
          <el-select v-model="form.role" placeholder="请选择角色">
            <el-option label="管理员" value="admin" />
            <el-option label="普通用户" value="user" />
            <el-option label="访客" value="guest" />
          </el-select>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="头像" prop="avatar">
          <el-upload
            class="avatar-uploader"
            action="/api/upload"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="form.avatar" :src="form.avatar" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>

        <el-form-item label="重置密码">
          <el-button type="warning" @click="showResetPasswordDialog">
            <el-icon><Key /></el-icon>重置密码
          </el-button>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            保存
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 重置密码对话框 -->
    <el-dialog
      v-model="resetPasswordVisible"
      title="重置密码"
      width="500px"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="新密码" prop="password">
          <el-input
            v-model="passwordForm.password"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetPasswordVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmResetPassword" :loading="resettingPassword">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Back, Plus, Key } from '@element-plus/icons-vue';
import type { FormInstance, FormRules } from 'element-plus';
import { PageHeader } from '@/components';
import { getUserDetail, updateUser, resetUserPassword } from '@/api/modules/user';
import type { UserInfo, UserUpdateData } from '@/api/modules/user';

const router = useRouter();
const route = useRoute();
const formRef = ref<FormInstance>();
const passwordFormRef = ref<FormInstance>();
const loading = ref(false);
const submitting = ref(false);
const resetPasswordVisible = ref(false);
const resettingPassword = ref(false);

// 表单数据
const form = reactive({
  id: 0,
  username: '',
  name: '',
  email: '',
  phone: '',
  role: '',
  status: '',
  avatar: '',
  permissions: [] as string[]
});

// 密码表单
const passwordForm = reactive({
  password: '',
  confirmPassword: ''
});

// 表单验证规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
});

// 密码表单验证规则
const passwordRules = reactive<FormRules>({
  password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.password) {
          callback(new Error('两次输入密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ]
});

// 获取用户详情
const getUserData = async () => {
  const id = Number(route.params.id);
  if (!id) {
    ElMessage.error('用户ID无效');
    goBack();
    return;
  }

  loading.value = true;
  try {
    const data = await getUserDetail(id);
    form.id = data.id;
    form.username = data.username;
    form.name = data.name;
    form.email = data.email;
    form.phone = data.phone || '';
    form.role = data.role;
    form.status = data.status;
    form.avatar = data.avatar || '';
    form.permissions = data.permissions || [];
  } catch (error) {
    console.error('获取用户详情失败', error);
    ElMessage.error('获取用户详情失败');
  } finally {
    loading.value = false;
  }
};

// 头像上传成功
const handleAvatarSuccess = (response: any) => {
  form.avatar = response.data.url;
};

// 头像上传前验证
const beforeAvatarUpload = (file: File) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isJPG) {
    ElMessage.error('上传头像图片只能是 JPG 或 PNG 格式!');
  }
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 2MB!');
  }
  return isJPG && isLt2M;
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true;
      try {
        // 构建提交数据
        const userData: UserUpdateData = {
          name: form.name,
          email: form.email,
          phone: form.phone,
          role: form.role,
          status: form.status,
          avatar: form.avatar,
          permissions: form.permissions
        };
        
        await updateUser(form.id, userData);
        ElMessage.success('用户更新成功');
        router.push('/user/list');
      } catch (error) {
        console.error('更新用户失败', error);
        ElMessage.error('更新用户失败');
      } finally {
        submitting.value = false;
      }
    } else {
      ElMessage.error('请完善表单信息');
    }
  });
};

// 重置表单
const resetForm = () => {
  getUserData();
};

// 显示重置密码对话框
const showResetPasswordDialog = () => {
  passwordForm.password = '';
  passwordForm.confirmPassword = '';
  resetPasswordVisible.value = true;
};

// 确认重置密码
const confirmResetPassword = async () => {
  if (!passwordFormRef.value) return;
  
  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      resettingPassword.value = true;
      try {
        await resetUserPassword(form.id, passwordForm.password);
        ElMessage.success('密码重置成功');
        resetPasswordVisible.value = false;
      } catch (error) {
        console.error('密码重置失败', error);
        ElMessage.error('密码重置失败');
      } finally {
        resettingPassword.value = false;
      }
    }
  });
};

// 返回列表
const goBack = () => {
  router.push('/user/list');
};

// 页面加载时获取数据
onMounted(() => {
  getUserData();
});
</script>

<style scoped>
.user-edit-container {
  padding: 20px;
}

.form-card {
  margin-top: 20px;
}

.avatar-uploader {
  width: 178px;
  height: 178px;
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
