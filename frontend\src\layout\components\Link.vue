<template>
  <component :is="type" v-bind="linkProps">
    <slot />
  </component>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { isExternal } from '../../utils/validate'

const props = defineProps({
  to: {
    type: String,
    required: true
  }
})

// 判断链接类型：外部链接使用a标签，内部链接使用router-link
const type = computed(() => {
  return isExternal(props.to) ? 'a' : 'router-link'
})

// 规范化路径 - 核心问题修复
const normalizePath = (path: string): string => {
  // 处理空路径
  if (!path || path === '') return '/'
  
  // 外部链接直接返回
  if (isExternal(path)) return path
  
  // 规范化内部路径
  // 1. 处理多个连续斜杠
  let normalizedPath = path.replace(/\/+/g, '/')
  
  // 2. 确保路径以单个斜杠开头
  if (!normalizedPath.startsWith('/')) {
    normalizedPath = '/' + normalizedPath
  }
  
  // 3. 移除尾部斜杠(除了根路径)
  if (normalizedPath.length > 1 && normalizedPath.endsWith('/')) {
    normalizedPath = normalizedPath.slice(0, -1)
  }
  
  return normalizedPath
}

// 链接属性
const linkProps = computed(() => {
  if (isExternal(props.to)) {
    return {
      href: props.to,
      target: '_blank',
      rel: 'noopener'
    }
  }
  
  // 确保路径格式正确
  const path = normalizePath(props.to)
  
  return {
    to: path,
    replace: false
  }
})
</script>

<style scoped>
a {
  text-decoration: none;
  color: inherit;
}
</style>