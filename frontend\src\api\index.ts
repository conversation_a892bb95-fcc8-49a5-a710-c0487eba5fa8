/**
 * API模块入口
 * 提供API请求功能和类型定义
 */

// 导出配置
export { API_CONFIG } from "./config";

// 导出核心功能
export { http, get, post, put, del, patch } from "./core/http";

// 导出辅助功能
export { getToken, storeTokens, clearTokens } from "./helpers/token";
export { handleApiError, getErrorMessage } from "./helpers/error";
export { getFromCache, saveToCache, clearCache } from "./helpers/cache";

// 导出业务模块
export * from "./modules/auth";
export * from "./modules/sales";

// 导出类型
export * from "./types";

/**
 * 获取认证令牌
 * 从token管理器中获取令牌
 */
export const getAuthToken = (): string | null => {
  return getToken();
};

/**
 * 转换分页参数
 * 将前端分页参数转换为后端API所需格式
 * @param params 分页参数
 * @returns 转换后的参数
 */
export const transformPaginationParams = (params: { page?: number, size?: number, [key: string]: any }): Record<string, any> => {
  const { page = 1, size = 10, ...rest } = params;

  // 转换为后端API所需的格式
  return {
    skip: (page - 1) * size,
    limit: size,
    ...rest
  };
};

// 导出默认API服务
export default {
  get,
  post,
  put,
  delete: del,
  patch,
  clearCache,
  getAuthToken,
  transformPaginationParams
};
