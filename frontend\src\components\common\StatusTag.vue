<template>
  <el-tag
    :type="tagType"
    :effect="effect"
    :size="size"
    :color="color"
    :hit="hit"
    :disable-transitions="disableTransitions"
  >
    <slot>{{ displayText }}</slot>
  </el-tag>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface StatusMap {
  [key: string]: {
    text: string
    type: '' | 'success' | 'warning' | 'info' | 'danger'
  }
}

const props = defineProps({
  /**
   * 状态值
   */
  status: {
    type: [String, Number],
    required: true
  },
  /**
   * 状态映射表
   */
  statusMap: {
    type: Object as () => StatusMap,
    default: () => ({})
  },
  /**
   * 默认文本（当状态不在映射表中时使用）
   */
  defaultText: {
    type: String,
    default: '未知状态'
  },
  /**
   * 默认类型（当状态不在映射表中时使用）
   */
  defaultType: {
    type: String as () => '' | 'success' | 'warning' | 'info' | 'danger',
    default: 'info'
  },
  /**
   * 标签效果
   */
  effect: {
    type: String as () => 'light' | 'dark' | 'plain',
    default: 'light'
  },
  /**
   * 标签大小
   */
  size: {
    type: String as () => 'large' | 'default' | 'small',
    default: 'default'
  },
  /**
   * 标签颜色
   */
  color: {
    type: String,
    default: ''
  },
  /**
   * 是否有边框描边
   */
  hit: {
    type: Boolean,
    default: false
  },
  /**
   * 是否禁用渐变动画
   */
  disableTransitions: {
    type: Boolean,
    default: false
  }
})

// 计算显示文本
const displayText = computed(() => {
  const status = String(props.status)
  return props.statusMap[status]?.text || props.defaultText
})

// 计算标签类型
const tagType = computed(() => {
  const status = String(props.status)
  return props.statusMap[status]?.type || props.defaultType
})
</script>
