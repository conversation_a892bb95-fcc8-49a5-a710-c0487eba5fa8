<template>
  <div class="virtual-table-container" ref="containerRef">
    <div
      class="virtual-table-header"
      :style="{ paddingRight: scrollbarWidth + 'px' }"
    >
      <table>
        <colgroup>
          <col v-for="(column, index) in columns" :key="'col-' + index" :width="column.width" />
        </colgroup>
        <thead>
          <tr>
            <th
              v-for="(column, index) in columns"
              :key="'header-' + index"
              :class="[column.align ? 'text-' + column.align : '']"
            >
              {{ column.title }}
            </th>
          </tr>
        </thead>
      </table>
    </div>
    
    <div
      class="virtual-table-body"
      ref="scrollRef"
      @scroll="onScroll"
      :style="{ height: height + 'px' }"
    >
      <div class="virtual-table-phantom" :style="{ height: totalHeight + 'px' }"></div>
      <table
        :style="{ transform: `translateY(${offsetY}px)` }"
      >
        <colgroup>
          <col v-for="(column, index) in columns" :key="'col-' + index" :width="column.width" />
        </colgroup>
        <tbody>
          <tr
            v-for="(item, index) in visibleData"
            :key="rowKey ? item[rowKey] : index"
            @click="$emit('row-click', item, startIndex + index)"
          >
            <td
              v-for="(column, colIndex) in columns"
              :key="'cell-' + colIndex"
              :class="[column.align ? 'text-' + column.align : '']"
            >
              <template v-if="column.slot">
                <slot :name="column.slot" :row="item" :index="startIndex + index"></slot>
              </template>
              <template v-else-if="column.render">
                <component :is="column.render(item, startIndex + index)" />
              </template>
              <template v-else>
                {{ item[column.dataIndex] }}
              </template>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    
    <div v-if="loading" class="virtual-table-loading">
      <el-icon class="loading-icon"><Loading /></el-icon>
    </div>
    
    <div v-if="data.length === 0 && !loading" class="virtual-table-empty">
      <slot name="empty">
        <el-empty description="暂无数据" />
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { Loading } from '@element-plus/icons-vue'

export interface TableColumn {
  title: string
  dataIndex: string
  width?: string | number
  align?: 'left' | 'center' | 'right'
  slot?: string
  render?: (row: any, index: number) => any
}

const props = defineProps({
  /**
   * 表格数据
   */
  data: {
    type: Array,
    required: true,
    default: () => []
  },
  /**
   * 表格列配置
   */
  columns: {
    type: Array as () => TableColumn[],
    required: true
  },
  /**
   * 行数据的键值
   */
  rowKey: {
    type: String,
    default: 'id'
  },
  /**
   * 表格高度
   */
  height: {
    type: Number,
    default: 400
  },
  /**
   * 行高
   */
  rowHeight: {
    type: Number,
    default: 48
  },
  /**
   * 缓冲区行数
   */
  bufferSize: {
    type: Number,
    default: 5
  },
  /**
   * 加载状态
   */
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['row-click', 'scroll'])

// 滚动容器引用
const containerRef = ref<HTMLElement | null>(null)
const scrollRef = ref<HTMLElement | null>(null)

// 滚动状态
const scrollTop = ref(0)
const scrollbarWidth = ref(0)

// 计算总高度
const totalHeight = computed(() => props.data.length * props.rowHeight)

// 计算可见区域的起始索引
const startIndex = computed(() => {
  const start = Math.floor(scrollTop.value / props.rowHeight) - props.bufferSize
  return Math.max(0, start)
})

// 计算可见区域的结束索引
const endIndex = computed(() => {
  const visibleCount = Math.ceil(props.height / props.rowHeight)
  const end = Math.floor(scrollTop.value / props.rowHeight) + visibleCount + props.bufferSize
  return Math.min(props.data.length, end)
})

// 计算可见数据
const visibleData = computed(() => {
  return props.data.slice(startIndex.value, endIndex.value)
})

// 计算偏移量
const offsetY = computed(() => startIndex.value * props.rowHeight)

// 滚动处理
const onScroll = (e: Event) => {
  const target = e.target as HTMLElement
  scrollTop.value = target.scrollTop
  emit('scroll', { scrollTop: target.scrollTop, scrollLeft: target.scrollLeft })
}

// 计算滚动条宽度
const calculateScrollbarWidth = () => {
  if (scrollRef.value) {
    const { offsetWidth, clientWidth } = scrollRef.value
    scrollbarWidth.value = offsetWidth - clientWidth
  }
}

// 滚动到指定索引
const scrollToIndex = (index: number) => {
  if (scrollRef.value) {
    scrollRef.value.scrollTop = index * props.rowHeight
  }
}

// 滚动到指定行
const scrollToRow = (rowKey: string | number) => {
  const index = props.data.findIndex((item: any) => item[props.rowKey] === rowKey)
  if (index !== -1) {
    scrollToIndex(index)
  }
}

// 监听数据变化
watch(() => props.data, () => {
  // 数据变化时重置滚动位置
  if (scrollRef.value) {
    scrollRef.value.scrollTop = 0
  }
}, { deep: true })

// 生命周期钩子
onMounted(() => {
  calculateScrollbarWidth()
  
  // 监听窗口大小变化
  window.addEventListener('resize', calculateScrollbarWidth)
  
  // 初始滚动到顶部
  if (scrollRef.value) {
    scrollRef.value.scrollTop = 0
  }
})

// 暴露方法
defineExpose({
  scrollToIndex,
  scrollToRow
})
</script>

<style scoped>
.virtual-table-container {
  position: relative;
  width: 100%;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  overflow: hidden;
}

.virtual-table-header {
  overflow: hidden;
  background-color: var(--el-fill-color-light);
}

.virtual-table-body {
  overflow: auto;
  position: relative;
}

.virtual-table-phantom {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  z-index: -1;
}

table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
}

th, td {
  padding: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

th {
  font-weight: bold;
  color: var(--el-text-color-primary);
  background-color: var(--el-fill-color-light);
  height: 48px;
}

tr:hover {
  background-color: var(--el-fill-color);
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.virtual-table-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.loading-icon {
  font-size: 24px;
  animation: rotating 2s linear infinite;
}

.virtual-table-empty {
  position: absolute;
  top: 48px;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
