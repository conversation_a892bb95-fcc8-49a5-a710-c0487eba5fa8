# Camunda微服务架构设计方案

## 一、设计背景

根据《流程引擎选型分析.md》中对Camunda和自定义流程引擎的比较，项目最初建议采用自定义流程引擎实现审批流程管理。然而，随着业务需求的变化和系统规模的扩大，需要考虑引入专业的工作流引擎以提高系统的扩展性、规范性和可维护性。本文档提出基于Camunda的微服务架构设计方案，以满足系统日益增长的复杂性需求。

## 二、Camunda介绍

Camunda是一个开源的工作流和决策自动化平台，提供了强大的流程设计、执行和监控能力。其核心优势包括：

1. **完全支持BPMN 2.0标准**：使用标准化的流程定义语言，便于业务人员理解和参与流程设计
2. **灵活的集成能力**：提供REST API、Java API以及与Spring Boot的无缝集成
3. **强大的流程监控和管理**：内置的Cockpit工具提供实时流程监控和管理能力
4. **支持微服务架构**：可以作为独立的微服务部署，与其他业务服务解耦
5. **可扩展性**：支持自定义监听器、表达式、服务任务等扩展点

## 三、微服务架构设计

### 3.1 整体架构图

```
┌─────────────────────────────────────────────────────────────────────┐
│                            API网关层                                 │
│                                                                     │
│                         API Gateway (Nginx/Spring Cloud Gateway)      │
└───────────────────────────────┬─────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────────┐
│                            微服务集群                                │
│                                                                     │
│  ┌────────────┐  ┌────────────┐  ┌────────────┐  ┌────────────┐     │
│  │            │  │            │  │            │  │            │     │
│  │  用户服务   │  │  审批服务   │  │  通知服务   │  │  系统服务   │     │
│  │            │  │            │  │            │  │            │     │
│  └────────────┘  └──────┬─────┘  └────────────┘  └────────────┘     │
│                         │                                            │
│                         ▼                                            │
│  ┌─────────────────────────────────────────┐                        │
│  │                                         │                        │
│  │          Camunda工作流引擎服务            │                        │
│  │                                         │                        │
│  └─────────────────────────────────────────┘                        │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────────┐
│                            数据持久层                                │
│                                                                     │
│  ┌────────────┐  ┌────────────┐  ┌────────────┐  ┌────────────┐     │
│  │            │  │            │  │            │  │            │     │
│  │   MySQL    │  │   Redis    │  │ 文件存储    │  │ 消息队列    │     │
│  │            │  │            │  │            │  │            │     │
│  └────────────┘  └────────────┘  └────────────┘  └────────────┘     │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

### 3.2 核心服务组件

1. **Camunda工作流引擎服务**
   - 提供流程定义、部署、执行和监控能力
   - 对外暴露REST API供其他服务调用
   - 管理流程实例的生命周期

2. **审批服务**
   - 处理各类审批业务逻辑
   - 与Camunda服务交互，启动流程、查询任务、完成任务等
   - 管理业务表单数据

3. **用户服务**
   - 用户认证与授权
   - 用户、角色、权限管理
   - 组织机构管理

4. **通知服务**
   - 处理系统通知和消息提醒
   - 支持多种通知渠道（站内信、邮件等）

5. **系统服务**
   - 系统配置管理
   - 日志审计
   - 数据字典维护

## 四、Camunda与微服务集成方案

### 4.1 部署模式

采用**独立部署模式**，将Camunda作为独立的微服务，提供以下优势：

1. **服务解耦**：工作流逻辑与业务逻辑分离，降低系统复杂度
2. **独立扩展**：可以根据流程引擎的负载情况单独进行扩展
3. **版本独立**：Camunda版本升级不影响其他业务服务
4. **资源隔离**：工作流引擎资源消耗不影响其他业务服务

### 4.2 集成方式

1. **REST API集成**
   - 业务服务通过REST API与Camunda服务交互
   - 使用统一的API网关进行路由和安全控制
   - 定义标准化的接口约定

2. **事件驱动集成**
   - 使用消息队列（如RabbitMQ）实现服务间的异步通信
   - Camunda的外部任务（External Task）模式实现任务的异步处理
   - 业务事件触发流程，流程事件触发业务动作

### 4.3 认证与授权

1. **统一认证**
   - 使用JWT进行身份认证
   - API网关层统一处理认证逻辑
   - Camunda集成Spring Security实现认证

2. **授权控制**
   - 基于RBAC模型实现权限控制
   - 定义任务分配规则（Assignment Rules）
   - 实现动态审批人分配

## 五、流程设计与实现

### 5.1 五大审批流程BPMN设计

#### 5.1.1 出差申请流程BPMN

```xml
<bpmn:process id="travel_application_process" name="出差申请流程">
  <bpmn:startEvent id="start_event" name="发起申请">
    <bpmn:outgoing>flow_to_dept_leader</bpmn:outgoing>
  </bpmn:startEvent>
  
  <bpmn:userTask id="dept_leader_task" name="部门负责人审批">
    <bpmn:incoming>flow_to_dept_leader</bpmn:incoming>
    <bpmn:outgoing>flow_to_condition</bpmn:outgoing>
  </bpmn:userTask>
  
  <bpmn:exclusiveGateway id="days_gateway" name="出差天数判断">
    <bpmn:incoming>flow_to_condition</bpmn:incoming>
    <bpmn:outgoing>flow_to_office_leader</bpmn:outgoing>
    <bpmn:outgoing>flow_to_vice_leader</bpmn:outgoing>
  </bpmn:exclusiveGateway>
  
  <bpmn:userTask id="office_leader_task" name="办公室主任审批">
    <bpmn:incoming>flow_to_office_leader</bpmn:incoming>
    <bpmn:outgoing>flow_to_secretary</bpmn:outgoing>
  </bpmn:userTask>
  
  <bpmn:userTask id="vice_leader_task" name="分管副局长审批">
    <bpmn:incoming>flow_to_vice_leader</bpmn:incoming>
    <bpmn:outgoing>flow_to_secretary</bpmn:outgoing>
  </bpmn:userTask>
  
  <bpmn:userTask id="secretary_task" name="党组书记审批">
    <bpmn:incoming>flow_to_secretary</bpmn:incoming>
    <bpmn:outgoing>flow_to_end</bpmn:outgoing>
  </bpmn:userTask>
  
  <bpmn:endEvent id="end_event" name="结束">
    <bpmn:incoming>flow_to_end</bpmn:incoming>
  </bpmn:endEvent>
  
  <bpmn:sequenceFlow id="flow_to_dept_leader" sourceRef="start_event" targetRef="dept_leader_task" />
  <bpmn:sequenceFlow id="flow_to_condition" sourceRef="dept_leader_task" targetRef="days_gateway" />
  <bpmn:sequenceFlow id="flow_to_office_leader" sourceRef="days_gateway" targetRef="office_leader_task">
    <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${travelDays &lt; 3}</bpmn:conditionExpression>
  </bpmn:sequenceFlow>
  <bpmn:sequenceFlow id="flow_to_vice_leader" sourceRef="days_gateway" targetRef="vice_leader_task">
    <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${travelDays &gt;= 3}</bpmn:conditionExpression>
  </bpmn:sequenceFlow>
  <bpmn:sequenceFlow id="flow_to_secretary" sourceRef="office_leader_task" targetRef="secretary_task" />
  <bpmn:sequenceFlow id="flow_to_secretary" sourceRef="vice_leader_task" targetRef="secretary_task" />
  <bpmn:sequenceFlow id="flow_to_end" sourceRef="secretary_task" targetRef="end_event" />
</bpmn:process>
```

其他四个流程（请假申请、车辆申请、会议申请、办公用品申请）也将采用类似结构的BPMN定义，根据《流程设计.md》中的流程定义进行实现。

### 5.2 任务分配策略

1. **基于部门的动态分配**
   - 部门负责人根据申请人所属部门动态确定
   - 使用Camunda的任务监听器实现

   ```java
   public class DepartmentLeaderAssignmentHandler implements TaskListener {
       @Override
       public void notify(DelegateTask delegateTask) {
           // 获取流程变量中的申请人ID和部门ID
           String applicantId = (String) delegateTask.getVariable("applicantId");
           String departmentId = (String) delegateTask.getVariable("departmentId");
           
           // 调用用户服务查询部门负责人
           String departmentLeaderId = userService.getDepartmentLeader(departmentId);
           
           // 设置任务处理人
           delegateTask.setAssignee(departmentLeaderId);
       }
   }
   ```

2. **固定角色分配**
   - 办公室主任、党组书记等固定角色的任务分配
   - 通过Camunda的候选组（Candidate Groups）实现

3. **条件分支动态路由**
   - 根据表单数据（如出差天数、请假天数）动态决定流程路径
   - 使用Camunda的条件表达式实现

### 5.3 表单集成

1. **外部表单模式**
   - 表单在前端独立实现，不依赖Camunda的表单机制
   - 表单数据通过API传递给流程引擎

2. **表单数据映射**
   - 定义业务表单与流程变量的映射关系
   - 在流程启动和任务完成时进行数据转换

   ```java
   // 流程启动时的表单数据映射
   Map<String, Object> variables = new HashMap<>();
   variables.put("applicantId", travelForm.getApplicantId());
   variables.put("departmentId", travelForm.getDepartmentId());
   variables.put("travelDays", travelForm.getTravelDays());
   variables.put("destination", travelForm.getDestination());
   variables.put("reason", travelForm.getReason());
   
   runtimeService.startProcessInstanceByKey("travel_application_process", variables);
   ```

## 六、数据库设计

### 6.1 Camunda数据库

Camunda自带的数据库表将用于存储流程定义、流程实例、任务等工作流相关数据。主要表包括：

- ACT_RE_*: 流程定义相关表
- ACT_RU_*: 运行时数据表
- ACT_HI_*: 历史数据表
- ACT_ID_*: 身份认证相关表

### 6.2 业务数据库

业务数据将存储在独立的数据库中，与原有系统保持一致：

```
+------------------+           +------------------+
| biz_travel       |           | wf_instance      |
+------------------+           +------------------+
| id               |<--+       | id               |
| applicant_id     |   |       | process_def_id   |
| dept_id          |   |       | business_key     |
| travel_days      |   +------>| status           |
| destination      |           | start_time       |
| reason           |           | end_time         |
| status           |           | form_data_json   |
| create_time      |           +------------------+
+------------------+
```

### 6.3 关联方式

1. **业务键关联**
   - 使用业务表的ID作为流程实例的业务键(businessKey)
   - 通过业务键可以在业务系统和工作流系统之间建立关联

2. **流程变量存储**
   - 关键的业务数据作为流程变量存储在Camunda中
   - 完整的业务数据存储在业务数据库中

## 七、API设计

### 7.1 流程管理API

```
# 流程定义管理
GET    /api/workflow/process-definitions                  # 获取所有流程定义
POST   /api/workflow/process-definitions                  # 部署新流程定义
GET    /api/workflow/process-definitions/{id}             # 获取流程定义详情
DELETE /api/workflow/process-definitions/{id}             # 删除流程定义

# 流程实例管理
POST   /api/workflow/process-instances                    # 启动流程实例
GET    /api/workflow/process-instances                    # 查询流程实例列表
GET    /api/workflow/process-instances/{id}               # 获取流程实例详情
POST   /api/workflow/process-instances/{id}/suspend       # 挂起流程实例
POST   /api/workflow/process-instances/{id}/activate      # 激活流程实例
DELETE /api/workflow/process-instances/{id}               # 删除流程实例
```

### 7.2 任务管理API

```
# 任务管理
GET    /api/workflow/tasks                                # 查询任务列表
GET    /api/workflow/tasks/my                             # 查询我的任务
GET    /api/workflow/tasks/{id}                           # 获取任务详情
POST   /api/workflow/tasks/{id}/complete                  # 完成任务
POST   /api/workflow/tasks/{id}/claim                     # 认领任务
POST   /api/workflow/tasks/{id}/unclaim                   # 取消认领
POST   /api/workflow/tasks/{id}/delegate                  # 委派任务
POST   /api/workflow/tasks/{id}/transfer                  # 转交任务
```

### 7.3 业务集成API

```
# 出差申请
POST   /api/travel/applications                           # 提交出差申请
GET    /api/travel/applications                           # 查询出差申请列表
GET    /api/travel/applications/{id}                      # 获取出差申请详情
PUT    /api/travel/applications/{id}                      # 更新出差申请
DELETE /api/travel/applications/{id}                      # 删除出差申请
POST   /api/travel/applications/{id}/approve              # 审批通过
POST   /api/travel/applications/{id}/reject               # 审批拒绝
POST   /api/travel/applications/{id}/withdraw             # 撤回申请
```

## 八、安全性设计

### 8.1 认证与授权

1. **JWT认证**
   - 用户登录后获取JWT令牌
   - 所有API请求携带JWT令牌
   - API网关验证令牌有效性

2. **权限控制**
   - 基于角色的权限控制
   - 任务级别的权限检查
   - 数据权限过滤

### 8.2 数据安全

1. **数据隔离**
   - 不同部门的数据进行隔离
   - 敏感数据加密存储

2. **审计日志**
   - 记录所有关键操作
   - 流程变更记录
   - 审批操作跟踪

## 九、部署方案

### 9.1 容器化部署

使用Docker容器部署各微服务：

```yaml
# docker-compose.yml示例
version: '3'
services:
  # API网关
  gateway:
    image: nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    
  # Camunda工作流引擎服务
  camunda:
    image: camunda/camunda-bpm-platform:latest
    ports:
      - "8080:8080"
    environment:
      - DB_DRIVER=org.mysql.Driver
      - DB_URL=*******************************
      - DB_USERNAME=root
      - DB_PASSWORD=password
    depends_on:
      - mysql
  
  # 审批服务
  approval-service:
    build: ./approval-service
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - CAMUNDA_API_URL=http://camunda:8080/engine-rest
    depends_on:
      - camunda
      - mysql
      
  # 其他微服务...
  
  # 数据库
  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=camunda
    volumes:
      - mysql-data:/var/lib/mysql
      
  # Redis
  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data

volumes:
  mysql-data:
  redis-data:
```

### 9.2 扩展性考虑

1. **水平扩展**
   - 所有微服务支持多实例部署
   - 使用负载均衡实现请求分发

2. **服务发现**
   - 使用服务注册中心（如Eureka或Consul）
   - 服务间基于服务名称而非IP地址通信

## 十、迁移策略

从自定义流程引擎迁移到Camunda的策略：

### 10.1 分阶段迁移

1. **第一阶段：环境准备**
   - 部署Camunda服务
   - 开发基础集成接口
   - 准备测试环境

2. **第二阶段：流程迁移**
   - 将五个审批流程转换为BPMN格式
   - 部署流程定义到Camunda
   - 开发流程管理接口

3. **第三阶段：业务集成**
   - 开发业务服务与Camunda的集成代码
   - 实现表单数据映射
   - 开发任务处理接口

4. **第四阶段：并行运行**
   - 新申请使用Camunda处理
   - 已有流程继续使用原系统处理
   - 收集反馈并优化

5. **第五阶段：完全切换**
   - 将所有流程迁移到Camunda
   - 停用原有流程引擎
   - 监控系统运行情况

### 10.2 数据迁移

1. **历史数据处理**
   - 历史流程数据保留在原系统
   - 提供历史数据查询接口
   - 可选项：将关键历史数据导入Camunda

2. **在途流程处理**
   - 在途流程继续在原系统完成
   - 或通过特殊处理迁移到Camunda中

## 十一、风险与应对策略

1. **学习成本风险**
   - 风险：团队对Camunda不熟悉，学习曲线较陡
   - 应对：分批培训、引入有经验的顾问、构建知识库

2. **集成复杂度风险**
   - 风险：微服务架构增加了系统复杂度
   - 应对：良好的接口设计、全面的测试、分阶段实施

3. **性能风险**
   - 风险：Camunda可能带来性能开销
   - 应对：性能测试、优化配置、合理的资源分配

4. **业务连续性风险**
   - 风险：迁移过程可能影响正常业务
   - 应对：并行运行策略、充分测试、灰度发布

## 十二、结论与建议

1. **总体评估**
   - Camunda提供了标准化、可视化的流程管理能力
   - 微服务架构提高了系统的可扩展性和维护性
   - 迁移虽有一定成本，但长期收益显著

2. **实施建议**
   - 采用渐进式迁移策略，降低风险
   - 加强团队培训，提升Camunda技术能力
   - 建立完善的监控体系，及时发现并解决问题

3. **未来展望**
   - 随着业务发展，可进一步利用Camunda的高级特性
   - 考虑引入DMN决策表，增强业务规则管理能力
   - 探索使用CMMN案例管理，处理非结构化流程 