<template>
  <div
    class="app-wrapper"
    :class="{ mobile: isMobile, 'hide-sidebar': !sidebarVisible }"
  >
    <!-- 侧边栏 -->
    <div class="sidebar-container">
      <div class="logo-container">
        <img class="logo-img" src="../assets/logo.svg" alt="Logo" />
        <h1 class="logo-title">妍大网络科技</h1>
      </div>
      <el-scrollbar>
        <el-menu
          :default-active="activeMenu"
          background-color="#001529"
          text-color="#fff"
          active-text-color="#409EFF"
          :collapse="!sidebarVisible"
          :unique-opened="true"
          :router="false"
          @select="handleSelect"
        >
          <sidebar-item
            v-for="route in permissionRoutes"
            :key="route.path"
            :item="route"
            :base-path="route.path"
          />
        </el-menu>
      </el-scrollbar>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
      <!-- 顶部导航 -->
      <div class="navbar">
        <div class="left-area">
          <el-icon class="toggle-sidebar" @click="toggleSidebar">
            <Expand v-if="!sidebarVisible" />
            <Fold v-else />
          </el-icon>
          <breadcrumb />
        </div>
        <div class="right-menu">
          <!-- 开发者模式开关 -->
          <!-- <div class="dev-mode-toggle" v-if="isAdmin">
            <el-tooltip
              :content="
                isDeveloperMode ? '开发者模式: 已启用' : '开发者模式: 未启用'
              "
              placement="bottom"
            >
              <el-button
                :type="isDeveloperMode ? 'success' : 'info'"
                circle
                size="small"
                @click="toggleDeveloperMode"
              >
                <el-icon><Monitor /></el-icon>
              </el-button>
            </el-tooltip>
          </div> -->

          <el-dropdown trigger="click">
            <div class="user-dropdown-link">
              <el-avatar :size="32" icon="UserFilled" />
              <span class="user-name">{{ username }}</span>
              <el-icon><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <!-- <el-dropdown-item @click="goToProfile">
                  <el-icon><User /></el-icon> 个人中心
                </el-dropdown-item> -->
                <!-- <el-dropdown-item v-if="isAdmin" @click="toggleDeveloperMode">
                  <el-icon><Setting /></el-icon>
                  {{ isDeveloperMode ? "关闭开发者模式" : "开启开发者模式" }}
                </el-dropdown-item> -->
                <el-dropdown-item divided @click="handleLogout">
                  <el-icon><SwitchButton /></el-icon> 退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 主要内容 -->
      <div class="app-main">
        <el-scrollbar>
          <router-view v-slot="{ Component }">
            <transition name="fade-transform" mode="out-in">
              <keep-alive>
                <component :is="Component" />
              </keep-alive>
            </transition>
          </router-view>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  onBeforeUnmount,
  defineAsyncComponent,
  watch,
} from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
import {
  Fold,
  Expand,
  User,
  SwitchButton,
  ArrowDown,
  Setting,
  Monitor,
} from "@element-plus/icons-vue";
import { useSettingsStore } from "../store/settings";

// 异步加载组件
const SidebarItem = defineAsyncComponent(
  () => import("./components/SidebarItem.vue")
);
const Breadcrumb = defineAsyncComponent(
  () => import("./components/Breadcrumb.vue")
);

const router = useRouter();
const route = useRoute();

// 使用设置存储
const settingsStore = useSettingsStore();

// 侧边栏可见状态 - 从存储中获取初始值
const sidebarVisible = ref(!settingsStore.settings.sidebarCollapsed);
const isMobile = ref(false);

// 标记是否正在进行状态更新
const isUpdating = ref(false);

// 获取路由
const permissionRoutes = computed(() => {
  return router.options.routes.filter((route) => {
    // 过滤隐藏的路由和空路由
    return (
      !route.meta?.hidden &&
      // 确保路由有标题和路径
      route.meta?.title &&
      route.path &&
      // 确保路由有子路由或组件
      ((Array.isArray(route.children) && route.children.length > 0) ||
        route.component)
    );
  });
});

// 当前活动菜单
const activeMenu = computed(() => {
  const { meta, path } = route;
  if (meta.activeMenu) {
    return meta.activeMenu;
  }
  return path;
});

// 用户信息
const username = computed(() => {
  // 尝试从用户信息JSON中获取用户名
  const userInfoStr = localStorage.getItem("user_info");
  if (userInfoStr) {
    try {
      const userInfo = JSON.parse(userInfoStr);
      return userInfo.name || userInfo.username || "未登录";
    } catch (e) {
      console.error("解析用户信息失败", e);
    }
  }

  // 兼容旧的存储方式
  return localStorage.getItem("userName") || "未登录";
});

// 判断是否管理员
const isAdmin = computed(() => {
  // 先尝试从用户信息JSON中获取角色ID
  const userInfoStr = localStorage.getItem("user_info");
  if (userInfoStr) {
    try {
      const userInfo = JSON.parse(userInfoStr);
      // 角色ID 1和2分别对应超级管理员和管理员
      return userInfo.role_id === 1 || userInfo.role_id === 2;
    } catch (e) {
      console.error("解析用户信息失败", e);
    }
  }

  // 兼容旧的存储方式
  const role = localStorage.getItem("userRole");
  return role === "admin" || role === "superadmin";
});

// 开发者模式状态
const isDeveloperMode = computed(() => settingsStore.settings.developerMode);

// 切换开发者模式
const toggleDeveloperMode = () => {
  settingsStore.toggleDeveloperMode();
  ElMessage.success(`开发者模式已${isDeveloperMode.value ? "启用" : "禁用"}`);
};

// 切换侧边栏
const toggleSidebar = () => {
  console.log("[Layout] 切换侧边栏，当前状态:", sidebarVisible.value);

  // 更新组件状态
  sidebarVisible.value = !sidebarVisible.value;
  console.log("[Layout] 切换后状态:", sidebarVisible.value);

  // 同步到存储
  settingsStore.setSidebarCollapsed(!sidebarVisible.value);

  // 添加一个短暂的延迟，确保DOM更新后再应用过渡效果
  setTimeout(() => {
    // 触发窗口resize事件，让组件重新计算尺寸
    window.dispatchEvent(new Event("resize"));
    console.log("[Layout] 延迟后状态检查:", sidebarVisible.value);
  }, 300);
};

// 个人中心
const goToProfile = () => {
  router.push("/profile");
};

// 退出登录
const handleLogout = () => {
  ElMessageBox.confirm("确定要退出登录吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // 清除本地存储的token和用户信息
      localStorage.removeItem("token");
      localStorage.removeItem("userName");
      localStorage.removeItem("userId");

      // 跳转到登录页
      router.push("/login");
      ElMessage.success("退出登录成功");
    })
    .catch(() => {
      // 取消退出
    });
};

// 响应式布局处理
const handleResize = () => {
  console.log("[Layout] handleResize 当前状态:", {
    isMobile: isMobile.value,
    sidebarVisible: sidebarVisible.value,
  });

  const oldIsMobile = isMobile.value;
  isMobile.value = window.innerWidth < 992;

  // 只在移动状态变化时才自动调整侧边栏
  if (oldIsMobile !== isMobile.value) {
    console.log("[Layout] 移动状态变化:", {
      oldIsMobile,
      newIsMobile: isMobile.value,
    });

    if (isMobile.value) {
      // 切换到移动模式，收起侧边栏
      console.log("[Layout] 切换到移动模式，收起侧边栏");
      sidebarVisible.value = false;
      settingsStore.setSidebarCollapsed(true);
    }
    // 注意：从移动模式切换到桌面模式时，保持当前侧边栏状态
  } else {
    // 如果不是移动状态变化，不要重置侧边栏状态
    console.log("[Layout] 移动状态未变化，保持当前侧边栏状态");
  }
};

// 处理菜单选择事件 - 核心修复
const handleSelect = (key: string, keyPath: string[]) => {
  console.log("菜单被选择:", key, keyPath);

  // 验证key是有效路径
  if (!key || typeof key !== "string") {
    console.warn("无效的路径:", key);
    return;
  }

  // 过滤掉keyPath中的空字符串元素
  const filteredKeyPath = keyPath.filter((path) => path !== "");
  console.log("处理后的keyPath:", filteredKeyPath);

  // 获取当前路由路径
  const currentPath = router.currentRoute.value.path;

  // 防止重复导航到当前路径
  if (key === currentPath) {
    console.log("已在当前路径，不需要导航:", key);
    return;
  }

  try {
    console.log("准备导航到路径:", key);
    // 执行路由导航
    router
      .push(key)
      .then(() => {
        console.log("导航成功:", key);
      })
      .catch((err) => {
        console.error("路由导航错误:", err);
      });
  } catch (error) {
    console.error("导航异常:", error);
  }
};

onMounted(() => {
  // 初始化侧边栏状态 - 从存储中获取
  sidebarVisible.value = !settingsStore.settings.sidebarCollapsed;

  // 初始化响应式布局
  handleResize();
  window.addEventListener("resize", handleResize);

  // 触发一次resize事件，确保组件正确渲染
  setTimeout(() => {
    window.dispatchEvent(new Event("resize"));
  }, 300);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
});
</script>

<style lang="scss">
/* 全局样式，确保在折叠状态下菜单项的文字完全隐藏 */
.el-menu--collapse {
  width: 64px !important;
}

.el-menu--collapse .el-menu-item,
.el-menu--collapse .el-sub-menu__title {
  width: 64px !important;
  min-width: 64px !important;
  max-width: 64px !important;
  padding: 0 !important;
  text-align: center !important;
  justify-content: center !important;
  overflow: hidden !important;
}

.el-menu--collapse .el-menu-item .el-icon,
.el-menu--collapse .el-sub-menu__title .el-icon {
  margin: 0 !important;
  width: 64px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}
.el-menu--collapse .el-sub-menu .el-sub-menu__icon-arrow {
  top: 40%;
}

.el-menu--collapse .el-menu-item span,
.el-menu--collapse .el-sub-menu__title span {
  display: none !important;
  width: 0 !important;
  max-width: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  opacity: 0 !important;
  visibility: hidden !important;
  position: absolute !important;
  left: -9999px !important;
  pointer-events: none !important;
  font-size: 0 !important;
  line-height: 0 !important;
  clip: rect(0, 0, 0, 0) !important;
  clip-path: inset(50%) !important;
}
</style>

<style lang="scss" scoped>
.app-wrapper {
  display: flex;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;
  background-color: #f0f2f5; /* 与主容器背景色一致 */
}

.sidebar-container {
  width: 220px;
  height: 100%;
  background: #001529;
  transition: width 0.3s ease-in-out;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  z-index: 1001;
  position: relative;
  /* 移除边框，使用阴影代替 */
  box-shadow: 1px 0 0 0 rgba(0, 0, 0, 0.2);
}

.hide-sidebar .sidebar-container {
  width: 64px;
  /* 确保折叠状态下也有相同的阴影效果 */
  box-shadow: 1px 0 0 0 rgba(0, 0, 0, 0.2);
  /* 确保折叠时文字不会溢出 */
  overflow: hidden !important;
}

.mobile .sidebar-container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  height: 100%;
  transition: transform 0.28s;
}

.mobile.hide-sidebar .sidebar-container {
  transform: translateX(-220px);
}

.logo-container {
  height: 60px;
  padding: 10px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: #002140;
  overflow: hidden;
  transition: all 0.3s;
}

.logo-img {
  width: 32px;
  height: 32px;
  margin-right: 12px;
  transition: all 0.3s;
}

.hide-sidebar .logo-img {
  margin-right: 0;
}

.logo-title {
  font-size: 16px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  transition: opacity 0.3s;
}

.hide-sidebar .logo-title {
  opacity: 0;
  width: 0;
  display: none;
  position: absolute;
  left: -9999px;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #f0f2f5;
  transition: margin-left 0.3s ease-in-out;
  position: relative;
  margin-left: -1px; /* 消除可能的间隙 */
}

.navbar {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.left-area {
  display: flex;
  align-items: center;
}

.toggle-sidebar {
  font-size: 20px;
  margin-right: 15px;
  cursor: pointer;
}

.right-menu {
  display: flex;
  align-items: center;
  gap: 15px;
}

.dev-mode-toggle {
  margin-right: 5px;
}

.user-dropdown-link {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 8px;
}

.user-name {
  margin: 0 8px;
}

.app-main {
  flex: 1;
  padding: 15px;
  overflow: hidden;
}

/* 过渡动画 */
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

/* 添加侧边栏折叠时的过渡效果 */
.el-menu--collapse {
  width: 64px !important;
  border-right: none !important; /* 移除右边框 */
}

/* 折叠状态下的菜单项样式 */
.hide-sidebar .el-menu-item,
.hide-sidebar .el-sub-menu__title,
.el-menu--collapse .el-menu-item,
.el-menu--collapse .el-sub-menu__title {
  padding: 0 !important;
  text-align: center !important;
  justify-content: center !important;
  width: 64px !important;
  min-width: 64px !important;
  max-width: 64px !important;
  overflow: hidden !important;
}

/* 折叠状态下的图标样式 */
.hide-sidebar .el-menu-item .el-icon,
.hide-sidebar .el-sub-menu__title .el-icon,
.el-menu--collapse .el-menu-item .el-icon,
.el-menu--collapse .el-sub-menu__title .el-icon {
  margin: 0 !important;
  width: 64px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

/* 强制隐藏折叠状态下的文字 - 更强力的选择器 */
.hide-sidebar .el-menu-item span,
.hide-sidebar .el-sub-menu__title span,
.hide-sidebar .el-menu--collapse .el-menu-item span,
.hide-sidebar .el-menu--collapse .el-sub-menu__title span,
.hide-sidebar .el-menu:not(.el-menu--collapse) .el-menu-item span,
.hide-sidebar .el-menu:not(.el-menu--collapse) .el-sub-menu__title span,
.el-menu--collapse .el-menu-item span,
.el-menu--collapse .el-sub-menu__title span {
  display: none !important;
  width: 0 !important;
  max-width: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  opacity: 0 !important;
  visibility: hidden !important;
  position: absolute !important;
  left: -9999px !important;
  pointer-events: none !important;
  font-size: 0 !important;
  line-height: 0 !important;
  clip: rect(0, 0, 0, 0) !important;
  clip-path: inset(50%) !important;
}

.el-menu {
  border-right: none !important; /* 移除默认的右边框 */
}

.el-menu--collapse .el-sub-menu__title span,
.el-menu--collapse .el-menu-item span {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: none !important; /* 强制隐藏 */
  opacity: 0;
}

.el-menu--collapse .el-sub-menu__icon-arrow {
  display: none;
}
:deep(.el-menu-item .el-menu-tooltip__trigger) {
  padding: 0;
}
</style>
