<template>
  <div class="sales-detail-container">
    <div class="page-header">
      <h1 class="page-title">销售详情</h1>
      <el-button @click="goBack">
        <el-icon><Back /></el-icon>返回列表
      </el-button>
    </div>
    
    <el-card class="detail-card">
      <el-descriptions
        title="基本信息"
        :column="2"
        border
      >
        <el-descriptions-item label="销售编号">{{ details.id }}</el-descriptions-item>
        <el-descriptions-item label="销售日期">{{ details.date }}</el-descriptions-item>
        <el-descriptions-item label="销售员">{{ getSalespersonName(details.salesperson) }}</el-descriptions-item>
        <el-descriptions-item label="销售状态">
          <el-tag :type="getStatusType(details.status)">{{ getStatusText(details.status) }}</el-tag>
        </el-descriptions-item>
      </el-descriptions>
      
      <el-divider />
      
      <el-descriptions
        title="客户信息"
        :column="2"
        border
      >
        <el-descriptions-item label="客户名称">{{ details.customerName }}</el-descriptions-item>
        <el-descriptions-item label="客户类型">
          <el-tag size="small" :type="getCustomerTypeTag(details.customerType)">
            {{ getCustomerTypeText(details.customerType) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="联系人">{{ details.contactPerson }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ details.contactPhone }}</el-descriptions-item>
      </el-descriptions>
      
      <el-divider />
      
      <h3 class="section-title">产品信息</h3>
      <el-table :data="[details]" style="width: 100%" border>
        <el-table-column prop="productName" label="产品名称" min-width="180" />
        <el-table-column prop="productCategory" label="产品类别" min-width="120">
          <template #default="{ row }">
            {{ getCategoryText(row.productCategory) }}
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" min-width="80" />
        <el-table-column prop="unitPrice" label="单价" min-width="120">
          <template #default="{ row }">
            {{ formatPrice(row.unitPrice) }}
          </template>
        </el-table-column>
        <el-table-column prop="totalAmount" label="总金额" min-width="120">
          <template #default="{ row }">
            {{ formatPrice(row.totalAmount) }}
          </template>
        </el-table-column>
      </el-table>
      
      <el-divider />
      
      <el-descriptions
        title="支付信息"
        :column="2"
        border
      >
        <el-descriptions-item label="付款方式">{{ getPaymentMethodText(details.paymentMethod) }}</el-descriptions-item>
        <el-descriptions-item label="付款状态">
          <el-tag :type="getPaymentStatusType(details.paymentStatus)">
            {{ getPaymentStatusText(details.paymentStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ details.createdAt }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ details.updatedAt }}</el-descriptions-item>
      </el-descriptions>
      
      <el-divider />
      
      <h3 class="section-title">备注信息</h3>
      <div class="remark-content">
        {{ details.remark || '暂无备注' }}
      </div>
      
      <el-divider />
      
      <h3 class="section-title">审核信息</h3>
      <el-steps :active="getAuditStep(details.status)" finish-status="success" simple style="margin-top: 20px">
        <el-step title="创建" :icon="Memo" />
        <el-step title="提交审核" :icon="Document" />
        <el-step title="财务审核" :icon="Money" />
        <el-step title="完成" :icon="SuccessFilled" />
      </el-steps>
      
      <el-timeline class="timeline" v-if="auditLogs.length > 0">
        <el-timeline-item
          v-for="(activity, index) in auditLogs"
          :key="index"
          :timestamp="activity.time"
          :type="activity.type"
          :hollow="activity.hollow"
          :color="activity.color"
        >
          {{ activity.content }}
        </el-timeline-item>
      </el-timeline>
      <el-empty v-else description="暂无审核记录" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Back } from '@element-plus/icons-vue'
import { Memo, Document, Money, SuccessFilled } from '@element-plus/icons-vue'

interface SalesDetail {
  id: number
  date: string
  salesperson: string
  customerName: string
  contactPerson: string
  contactPhone: string
  customerType: string
  productName: string
  productCategory: string
  quantity: number
  unitPrice: number
  totalAmount: number
  paymentMethod: string
  paymentStatus: string
  status: string
  remark: string
  createdAt: string
  updatedAt: string
}

interface AuditLog {
  time: string
  content: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  hollow?: boolean
  color?: string
}

const router = useRouter()
const route = useRoute()

// 详情数据
const details = ref<SalesDetail>({
  id: 0,
  date: '',
  salesperson: '',
  customerName: '',
  contactPerson: '',
  contactPhone: '',
  customerType: '',
  productName: '',
  productCategory: '',
  quantity: 0,
  unitPrice: 0,
  totalAmount: 0,
  paymentMethod: '',
  paymentStatus: '',
  status: '',
  remark: '',
  createdAt: '',
  updatedAt: ''
})

// 审核日志
const auditLogs = ref<AuditLog[]>([])

// 获取详情数据
const getDetails = () => {
  const id = route.params.id
  
  // 这里应该调用API获取数据，目前使用模拟数据
  if (id === '1') {
    details.value = {
      id: 1,
      date: '2023-04-01',
      salesperson: 'zhangsan',
      customerName: '北京科技有限公司',
      contactPerson: '张经理',
      contactPhone: '***********',
      customerType: 'returning',
      productName: '高级会员服务',
      productCategory: 'membership',
      quantity: 1,
      unitPrice: 9800.00,
      totalAmount: 9800.00,
      paymentMethod: 'bank',
      paymentStatus: 'paid',
      status: 'approved',
      remark: '年度服务续费',
      createdAt: '2023-04-01 09:15:30',
      updatedAt: '2023-04-02 14:22:45'
    }
    
    auditLogs.value = [
      {
        time: '2023-04-01 09:15:30',
        content: '销售记录创建 - 张三',
        type: 'info'
      },
      {
        time: '2023-04-01 10:30:15',
        content: '提交审核 - 张三',
        type: 'primary'
      },
      {
        time: '2023-04-02 14:22:45',
        content: '财务审核通过 - 王财务',
        type: 'success'
      }
    ]
  }
}

// 返回列表
const goBack = () => {
  router.push('/sales/list')
}

// 格式化价格
const formatPrice = (price: number) => {
  return `¥ ${price.toFixed(2)}`
}

// 获取销售员名称
const getSalespersonName = (code: string) => {
  const map: Record<string, string> = {
    'zhangsan': '张三',
    'lisi': '李四',
    'wangwu': '王五'
  }
  return map[code] || code
}

// 获取状态样式
const getStatusType = (status: string) => {
  const map: Record<string, string> = {
    'draft': 'info',
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  }
  return map[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    'draft': '草稿',
    'pending': '待审核',
    'approved': '已审核',
    'rejected': '已驳回'
  }
  return map[status] || '未知状态'
}

// 获取客户类型标签样式
const getCustomerTypeTag = (type: string) => {
  const map: Record<string, string> = {
    'new': '',
    'returning': 'success',
    'vip': 'warning'
  }
  return map[type] || ''
}

// 获取客户类型文本
const getCustomerTypeText = (type: string) => {
  const map: Record<string, string> = {
    'new': '新客户',
    'returning': '老客户',
    'vip': 'VIP客户'
  }
  return map[type] || '未知类型'
}

// 获取产品类别文本
const getCategoryText = (category: string) => {
  const map: Record<string, string> = {
    'membership': '会员服务',
    'technical': '技术支持',
    'advertising': '广告服务',
    'data': '数据服务'
  }
  return map[category] || '其他'
}

// 获取付款方式文本
const getPaymentMethodText = (method: string) => {
  const map: Record<string, string> = {
    'cash': '现金',
    'bank': '银行转账',
    'wechat': '微信支付',
    'alipay': '支付宝',
    'credit': '信用卡'
  }
  return map[method] || '其他'
}

// 获取付款状态样式
const getPaymentStatusType = (status: string) => {
  const map: Record<string, string> = {
    'unpaid': 'danger',
    'partial': 'warning',
    'paid': 'success'
  }
  return map[status] || 'info'
}

// 获取付款状态文本
const getPaymentStatusText = (status: string) => {
  const map: Record<string, string> = {
    'unpaid': '未支付',
    'partial': '部分支付',
    'paid': '已支付'
  }
  return map[status] || '未知状态'
}

// 获取审核步骤
const getAuditStep = (status: string) => {
  const steps: Record<string, number> = {
    'draft': 0,
    'pending': 1,
    'approved': 3,
    'rejected': 1
  }
  return steps[status] || 0
}

// 页面加载时获取数据
onMounted(() => {
  getDetails()
})
</script>

<style scoped>
.sales-detail-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  color: #303133;
  margin: 0;
}

.detail-card {
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  color: #303133;
  margin: 16px 0;
  font-weight: 500;
}

.remark-content {
  min-height: 60px;
  padding: 12px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  background-color: #F8F9FA;
}

.timeline {
  margin-top: 20px;
  padding-left: 20px;
}
</style>