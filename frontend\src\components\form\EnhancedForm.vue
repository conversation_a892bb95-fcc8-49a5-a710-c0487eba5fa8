<template>
  <el-form
    ref="formRef"
    :model="modelValue"
    :rules="mergedRules"
    :label-width="labelWidth"
    :label-position="labelPosition"
    :size="size"
    :disabled="disabled"
    :validate-on-rule-change="validateOnRuleChange"
    :hide-required-asterisk="hideRequiredAsterisk"
    :scroll-to-error="scrollToError"
    :status-icon="statusIcon"
    @submit.prevent="handleSubmit"
  >
    <slot></slot>
    
    <div v-if="showActions" class="form-actions">
      <slot name="actions">
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          {{ submitText }}
        </el-button>
        <el-button @click="handleReset">
          {{ resetText }}
        </el-button>
        <slot name="extra-buttons"></slot>
      </slot>
    </div>
    
    <div v-if="formErrors.length > 0" class="form-errors">
      <el-alert
        v-for="(error, index) in formErrors"
        :key="index"
        :title="error"
        type="error"
        :closable="false"
        show-icon
      />
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'

const props = defineProps({
  /**
   * 表单数据对象
   */
  modelValue: {
    type: Object,
    required: true
  },
  /**
   * 表单验证规则
   */
  rules: {
    type: Object as () => FormRules,
    default: () => ({})
  },
  /**
   * 标签宽度
   */
  labelWidth: {
    type: String,
    default: '100px'
  },
  /**
   * 标签位置
   */
  labelPosition: {
    type: String as () => 'left' | 'right' | 'top',
    default: 'right'
  },
  /**
   * 表单尺寸
   */
  size: {
    type: String as () => 'large' | 'default' | 'small',
    default: 'default'
  },
  /**
   * 是否禁用表单
   */
  disabled: {
    type: Boolean,
    default: false
  },
  /**
   * 是否在规则变化时触发验证
   */
  validateOnRuleChange: {
    type: Boolean,
    default: true
  },
  /**
   * 是否隐藏必填字段的星号
   */
  hideRequiredAsterisk: {
    type: Boolean,
    default: false
  },
  /**
   * 是否在提交表单且校验不通过时滚动到错误表单项
   */
  scrollToError: {
    type: Boolean,
    default: true
  },
  /**
   * 是否在输入框中显示校验结果反馈图标
   */
  statusIcon: {
    type: Boolean,
    default: false
  },
  /**
   * 是否显示操作按钮
   */
  showActions: {
    type: Boolean,
    default: true
  },
  /**
   * 提交按钮文本
   */
  submitText: {
    type: String,
    default: '提交'
  },
  /**
   * 重置按钮文本
   */
  resetText: {
    type: String,
    default: '重置'
  },
  /**
   * 提交按钮加载状态
   */
  submitLoading: {
    type: Boolean,
    default: false
  },
  /**
   * 是否在提交前验证表单
   */
  validateBeforeSubmit: {
    type: Boolean,
    default: true
  },
  /**
   * 是否显示表单级别的错误提示
   */
  showFormErrors: {
    type: Boolean,
    default: true
  },
  /**
   * 自定义验证函数
   */
  customValidate: {
    type: Function,
    default: null
  },
  /**
   * 是否在输入时实时验证
   */
  validateOnInput: {
    type: Boolean,
    default: false
  },
  /**
   * 是否在失去焦点时验证
   */
  validateOnBlur: {
    type: Boolean,
    default: true
  },
  /**
   * 是否在变更时验证
   */
  validateOnChange: {
    type: Boolean,
    default: false
  },
  /**
   * 是否使用增强的验证规则
   */
  useEnhancedRules: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits([
  'update:modelValue',
  'submit',
  'reset',
  'validate',
  'error'
])

// 表单引用
const formRef = ref<FormInstance>()
// 表单级别错误
const formErrors = ref<string[]>([])

// 合并验证规则，添加增强功能
const mergedRules = computed(() => {
  if (!props.useEnhancedRules) {
    return props.rules
  }
  
  const enhancedRules: FormRules = {}
  
  // 遍历原始规则
  Object.entries(props.rules).forEach(([field, fieldRules]) => {
    if (!Array.isArray(fieldRules)) {
      enhancedRules[field] = fieldRules
      return
    }
    
    // 增强每个字段的规则
    enhancedRules[field] = fieldRules.map(rule => {
      // 克隆规则对象
      const newRule = { ...rule }
      
      // 增强必填规则的消息
      if (newRule.required && !newRule.message) {
        newRule.message = `请输入${getFieldLabel(field)}`
      }
      
      // 增强验证器
      if (newRule.validator) {
        const originalValidator = newRule.validator
        newRule.validator = (rule, value, callback) => {
          try {
            const result = originalValidator(rule, value, callback)
            if (result instanceof Promise) {
              return result.catch(error => {
                // 捕获并处理异步验证器的错误
                if (typeof error === 'string') {
                  callback(new Error(error))
                } else if (error instanceof Error) {
                  callback(error)
                } else {
                  callback(new Error('验证失败'))
                }
              })
            }
          } catch (error) {
            // 捕获并处理同步验证器的错误
            if (typeof error === 'string') {
              callback(new Error(error))
            } else if (error instanceof Error) {
              callback(error)
            } else {
              callback(new Error('验证失败'))
            }
          }
        }
      }
      
      return newRule
    })
  })
  
  return enhancedRules
})

// 获取字段标签
const getFieldLabel = (field: string): string => {
  // 尝试从表单项中获取标签
  const formItem = document.querySelector(`[data-field="${field}"]`)
  if (formItem) {
    const label = formItem.getAttribute('label')
    if (label) return label
  }
  
  // 使用字段名作为后备
  return field
}

// 验证表单
const validate = async (): Promise<boolean> => {
  if (!formRef.value) return false
  
  // 清除表单级别错误
  formErrors.value = []
  
  try {
    // 执行内置验证
    await formRef.value.validate()
    
    // 执行自定义验证
    if (props.customValidate) {
      await props.customValidate(props.modelValue)
    }
    
    emit('validate', true)
    return true
  } catch (error) {
    // 处理验证错误
    if (error instanceof Error) {
      formErrors.value.push(error.message)
    } else if (typeof error === 'string') {
      formErrors.value.push(error)
    } else if (error && typeof error === 'object') {
      // Element Plus 表单验证错误
      const fields = Object.values(error).flat()
      fields.forEach((field: any) => {
        if (field && field.message) {
          formErrors.value.push(field.message)
        }
      })
    }
    
    emit('validate', false)
    emit('error', formErrors.value)
    return false
  }
}

// 验证特定字段
const validateField = async (field: string | string[]): Promise<boolean> => {
  if (!formRef.value) return false
  
  try {
    await formRef.value.validateField(field)
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
const resetFields = () => {
  if (formRef.value) {
    formRef.value.resetFields()
    formErrors.value = []
  }
}

// 清除验证
const clearValidate = (fields?: string | string[]) => {
  if (formRef.value) {
    formRef.value.clearValidate(fields)
  }
}

// 滚动到指定字段
const scrollToField = (field: string) => {
  if (formRef.value) {
    formRef.value.scrollToField(field)
  }
}

// 处理提交
const handleSubmit = async () => {
  if (props.validateBeforeSubmit) {
    const valid = await validate()
    if (!valid) return
  }
  
  emit('submit', props.modelValue)
}

// 处理重置
const handleReset = () => {
  resetFields()
  emit('reset')
}

// 监听输入事件
if (props.validateOnInput) {
  watch(() => props.modelValue, () => {
    nextTick(() => {
      if (formRef.value) {
        formRef.value.validate().catch(() => {})
      }
    })
  }, { deep: true })
}

// 暴露方法
defineExpose({
  formRef,
  validate,
  validateField,
  resetFields,
  clearValidate,
  scrollToField,
  formErrors
})
</script>

<style scoped>
.form-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-start;
  gap: 10px;
}

.form-errors {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
</style>
