package com.simbest.cloud.pyjyj.car.web;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/car")
public class CarController {

    /**
     * http://localhost:9003/pyjyj/car/anonymous/messages1
     * 可匿名访问
     * 
     * @return
     */
    @GetMapping(value = "/anonymous/messages1")
    public String getMessages1() {
        return " hello Message 1";
    }

}
