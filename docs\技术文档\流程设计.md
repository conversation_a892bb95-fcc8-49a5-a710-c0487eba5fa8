# 流程设计

## 概述

本文档描述教育局日常业务审批平台的流程设计，包括各类审批流程的定义、节点设计、权限控制等内容。系统采用BPMN 2.0标准定义工作流程，并基于Camunda流程引擎实现。

## BPMN 2.0标准简介

BPMN (Business Process Model and Notation) 2.0是一种标准化的业务流程建模符号，用于图形化地表示业务流程。它提供了一套标准化的符号，使业务分析师能够创建业务流程图，而技术开发人员则可以实现这些流程。

### 主要元素

1. **事件 (Events)**
   - 开始事件 (Start Event)：流程的起点
   - 结束事件 (End Event)：流程的终点
   - 中间事件 (Intermediate Event)：流程中发生的事件

2. **活动 (Activities)**
   - 任务 (Task)：流程中的工作单元
     - 用户任务 (User Task)：需要人工完成的工作
     - 服务任务 (Service Task)：由系统自动执行的操作
   - 子流程 (Sub-Process)：包含其他活动的复合活动

3. **网关 (Gateways)**
   - 排他网关 (Exclusive Gateway)：基于条件的分支("XOR")
   - 并行网关 (Parallel Gateway)：创建并行路径("AND")
   - 包容网关 (Inclusive Gateway)：条件路径的组合("OR")

4. **连接对象 (Connecting Objects)**
   - 顺序流 (Sequence Flow)：定义活动的执行顺序
   - 消息流 (Message Flow)：表示消息在参与者之间的流动
   - 关联 (Association)：连接信息和图元

5. **泳道 (Swimlanes)**
   - 池 (Pool)：表示一个参与者
   - 道 (Lane)：组织和分类活动

## 审批权限设计

在系统中，不同角色拥有不同的审批权限：

1. **部门负责人**：审批本部门人员提交的所有类型申请
2. **办公室主任**：审批所有出差申请、车辆使用申请、会议室申请、办公用品申请
3. **分管领导**：审批较高级别的出差、请假、办公用品等申请
4. **局长**：审批特殊级别的申请
5. **主管财务/车辆/会议/物资的专员**：负责相应领域的业务审批

## 流程引擎实现

系统采用Camunda流程引擎实现BPMN流程的执行与管理。主要功能包括：

1. **流程部署**：将BPMN定义的流程部署到引擎中
2. **流程实例管理**：创建、查询、删除流程实例
3. **任务管理**：分配、完成、查询用户任务
4. **变量管理**：设置和获取流程变量
5. **历史数据**：查询历史流程和任务信息

## 通用审批流程模型

系统中所有审批流程都遵循通用的审批操作模型：

1. **审批操作类型**
   - 同意：流程继续向下一个节点流转
   - 拒绝：流程终止，申请被拒绝
   - 退回：将申请退回到发起人，可以修改后重新提交
   - 转交：将当前任务转交给其他人处理

2. **审批数据记录**
   - 审批人信息
   - 审批时间
   - 审批结果
   - 审批意见
   - 附件（如有）

3. **任务分配策略**
   - 用户任务分配：基于组织结构和角色自动分配审批人
   - 角色分配：部门负责人、办公室主任等角色自动分配
   - 候选组分配：多人可见，由一人认领后处理

## 具体流程BPMN设计

### 1. 出差申请流程 BPMN设计

#### BPMN流程图

![出差申请流程BPMN图](../images/travel_application_process.png)

#### BPMN XML定义

```xml
<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xmlns:camunda="http://camunda.org/schema/1.0/bpmn"
                  id="Definitions_travel_application"
                  targetNamespace="http://bpmn.io/schema/bpmn">
  
  <bpmn:process id="travel_application_process" name="出差申请流程" isExecutable="true">
    
    <!-- 开始事件 -->
    <bpmn:startEvent id="start_event" name="发起申请">
      <bpmn:outgoing>flow_to_dept_leader</bpmn:outgoing>
    </bpmn:startEvent>
    
    <!-- 部门负责人任务 -->
    <bpmn:userTask id="dept_leader_task" name="部门负责人审批">
      <bpmn:extensionElements>
        <camunda:taskListener event="create" 
          class="com.pyjyj.workflow.listener.DeptLeaderAssignmentListener" />
      </bpmn:extensionElements>
      <bpmn:incoming>flow_to_dept_leader</bpmn:incoming>
      <bpmn:outgoing>flow_to_days_gateway</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- 出差天数网关 -->
    <bpmn:exclusiveGateway id="days_gateway" name="出差天数判断">
      <bpmn:incoming>flow_to_days_gateway</bpmn:incoming>
      <bpmn:outgoing>flow_lt_3</bpmn:outgoing>
      <bpmn:outgoing>flow_to_vice_leader</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    
    <!-- 办公室主任任务 -->
    <bpmn:userTask id="office_leader_task" name="办公室主任审批">
      <bpmn:incoming>flow_lt_3</bpmn:incoming>
      <bpmn:outgoing>flow_to_secretary</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- 分管领导任务 -->
    <bpmn:userTask id="vice_leader_task" name="分管领导审批">
      <bpmn:incoming>flow_to_vice_leader</bpmn:incoming>
      <bpmn:outgoing>flow_from_vice_to_secretary</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- 局办公室专员任务 -->
    <bpmn:userTask id="secretary_task" name="局办公室专员审批">
      <bpmn:incoming>flow_to_secretary</bpmn:incoming>
      <bpmn:incoming>flow_from_vice_to_secretary</bpmn:incoming>
      <bpmn:outgoing>flow_to_end</bpmn:outgoing>
    </bpmn:userTask>
    
    <!-- 结束事件 -->
    <bpmn:endEvent id="end_event" name="结束">
      <bpmn:incoming>flow_to_end</bpmn:incoming>
    </bpmn:endEvent>
    
    <!-- 顺序流 -->
    <bpmn:sequenceFlow id="flow_to_dept_leader" sourceRef="start_event" targetRef="dept_leader_task" />
    <bpmn:sequenceFlow id="flow_to_days_gateway" sourceRef="dept_leader_task" targetRef="days_gateway" />
    
    <!-- 条件顺序流：小于3天 -->
    <bpmn:sequenceFlow id="flow_lt_3" sourceRef="days_gateway" targetRef="office_leader_task">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${travelDays &lt; 3}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    
    <!-- 条件顺序流：大于等于3天 -->
    <bpmn:sequenceFlow id="flow_to_vice_leader" sourceRef="days_gateway" targetRef="vice_leader_task">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${travelDays &gt;= 3}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    
    <bpmn:sequenceFlow id="flow_to_secretary" sourceRef="office_leader_task" targetRef="secretary_task" />
    <bpmn:sequenceFlow id="flow_from_vice_to_secretary" sourceRef="vice_leader_task" targetRef="secretary_task" />
    <bpmn:sequenceFlow id="flow_to_end" sourceRef="secretary_task" targetRef="end_event" />
  </bpmn:process>
  
</bpmn:definitions>
```

### 2. 请假申请流程 BPMN设计

#### BPMN流程图

![请假申请流程BPMN图](../images/leave_application_process.png)

#### BPMN XML定义 (关键部分)

```xml
<bpmn:process id="leave_application_process" name="请假申请流程" isExecutable="true">
  <!-- 开始事件 -->
  <bpmn:startEvent id="start_event" name="发起申请">
    <bpmn:outgoing>flow_to_dept_leader</bpmn:outgoing>
  </bpmn:startEvent>
  
  <!-- 部门负责人审批 -->
  <bpmn:userTask id="dept_leader_task" name="部门负责人审批">
    <bpmn:extensionElements>
      <camunda:taskListener event="create" 
        class="com.pyjyj.workflow.listener.DeptLeaderAssignmentListener" />
    </bpmn:extensionElements>
    <bpmn:incoming>flow_to_dept_leader</bpmn:incoming>
    <bpmn:outgoing>flow_to_days_gateway</bpmn:outgoing>
  </bpmn:userTask>
  
  <!-- 请假天数判断网关 -->
  <bpmn:exclusiveGateway id="days_gateway" name="请假天数判断">
    <bpmn:incoming>flow_to_days_gateway</bpmn:incoming>
    <bpmn:outgoing>flow_lt_3</bpmn:outgoing>
    <bpmn:outgoing>flow_ge_3_lt_7</bpmn:outgoing>
    <bpmn:outgoing>flow_ge_7</bpmn:outgoing>
  </bpmn:exclusiveGateway>
  
  <!-- 条件顺序流定义 -->
  <bpmn:sequenceFlow id="flow_lt_3" sourceRef="days_gateway" targetRef="office_leader_task">
    <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${leaveDays &lt; 3}</bpmn:conditionExpression>
  </bpmn:sequenceFlow>
  
  <bpmn:sequenceFlow id="flow_ge_3_lt_7" sourceRef="days_gateway" targetRef="vice_leader_task">
    <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${leaveDays &gt;= 3 &amp;&amp; leaveDays &lt; 7}</bpmn:conditionExpression>
  </bpmn:sequenceFlow>
  
  <bpmn:sequenceFlow id="flow_ge_7" sourceRef="days_gateway" targetRef="vice_leader_task">
    <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${leaveDays &gt;= 7}</bpmn:conditionExpression>
  </bpmn:sequenceFlow>
  
  <!-- 其余节点和顺序流定义 -->
  <!-- ... -->
</bpmn:process>
```

### 3. 车辆申请流程 BPMN设计

#### BPMN流程图

![车辆申请流程BPMN图](../images/vehicle_application_process.png)

#### BPMN XML定义 (简化)

```xml
<bpmn:process id="vehicle_application_process" name="车辆申请流程" isExecutable="true">
  <bpmn:startEvent id="start_event" name="发起申请">
    <bpmn:outgoing>flow_to_dept_leader</bpmn:outgoing>
  </bpmn:startEvent>
  
  <bpmn:userTask id="dept_leader_task" name="部门负责人审批">
    <bpmn:extensionElements>
      <camunda:taskListener event="create" 
        class="com.pyjyj.workflow.listener.DeptLeaderAssignmentListener" />
    </bpmn:extensionElements>
    <bpmn:incoming>flow_to_dept_leader</bpmn:incoming>
    <bpmn:outgoing>flow_to_office_leader</bpmn:outgoing>
  </bpmn:userTask>
  
  <bpmn:userTask id="office_leader_task" name="办公室主任审批">
    <bpmn:incoming>flow_to_office_leader</bpmn:incoming>
    <bpmn:outgoing>flow_to_end</bpmn:outgoing>
    <bpmn:humanPerformer>
      <bpmn:resourceAssignmentExpression>
        <bpmn:formalExpression>yupartywei</bpmn:formalExpression>
      </bpmn:resourceAssignmentExpression>
    </bpmn:humanPerformer>
  </bpmn:userTask>
  
  <bpmn:endEvent id="end_event" name="结束">
    <bpmn:incoming>flow_to_end</bpmn:incoming>
  </bpmn:endEvent>
  
  <bpmn:sequenceFlow id="flow_to_dept_leader" sourceRef="start_event" targetRef="dept_leader_task" />
  <bpmn:sequenceFlow id="flow_to_office_leader" sourceRef="dept_leader_task" targetRef="office_leader_task" />
  <bpmn:sequenceFlow id="flow_to_end" sourceRef="office_leader_task" targetRef="end_event" />
</bpmn:process>
```

### 4. 会议申请流程 BPMN设计

#### BPMN流程图

![会议申请流程BPMN图](../images/meeting_application_process.png)

#### BPMN XML定义 (关键部分)

```xml
<bpmn:process id="meeting_application_process" name="会议申请流程" isExecutable="true">
  <!-- 开始事件 -->
  <bpmn:startEvent id="start_event" name="发起申请">
    <bpmn:outgoing>flow_to_dept_leader</bpmn:outgoing>
  </bpmn:startEvent>
  
  <!-- 会议类型网关 -->
  <bpmn:exclusiveGateway id="meeting_type_gateway" name="会议类型判断">
    <bpmn:incoming>flow_from_dept_leader</bpmn:incoming>
    <bpmn:outgoing>flow_normal_meeting</bpmn:outgoing>
    <bpmn:outgoing>flow_important_meeting</bpmn:outgoing>
  </bpmn:exclusiveGateway>
  
  <!-- 条件流 -->
  <bpmn:sequenceFlow id="flow_normal_meeting" sourceRef="meeting_type_gateway" targetRef="office_leader_task">
    <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${meetingType == 'normal'}</bpmn:conditionExpression>
  </bpmn:sequenceFlow>
  
  <bpmn:sequenceFlow id="flow_important_meeting" sourceRef="meeting_type_gateway" targetRef="vice_leader_task">
    <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${meetingType == 'important'}</bpmn:conditionExpression>
  </bpmn:sequenceFlow>
  
  <!-- 其余节点和顺序流定义 -->
  <!-- ... -->
</bpmn:process>
```

### 5. 办公用品申请流程 BPMN设计

#### BPMN流程图

![办公用品申请流程BPMN图](../images/office_supplies_process.png)

#### BPMN XML定义 (关键部分)

```xml
<bpmn:process id="office_supplies_process" name="办公用品申请流程" isExecutable="true">
  <!-- 金额判断网关 -->
  <bpmn:exclusiveGateway id="amount_gateway" name="金额判断">
    <bpmn:incoming>flow_from_dept_leader</bpmn:incoming>
    <bpmn:outgoing>flow_amount_lt_1000</bpmn:outgoing>
    <bpmn:outgoing>flow_amount_ge_1000</bpmn:outgoing>
  </bpmn:exclusiveGateway>
  
  <!-- 条件流 -->
  <bpmn:sequenceFlow id="flow_amount_lt_1000" sourceRef="amount_gateway" targetRef="office_leader_task">
    <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${totalAmount &lt; 1000}</bpmn:conditionExpression>
  </bpmn:sequenceFlow>
  
  <bpmn:sequenceFlow id="flow_amount_ge_1000" sourceRef="amount_gateway" targetRef="vice_leader_task">
    <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${totalAmount &gt;= 1000}</bpmn:conditionExpression>
  </bpmn:sequenceFlow>
  
  <!-- 其余节点和顺序流定义 -->
  <!-- ... -->
</bpmn:process>
```

## 业务与流程集成

### 流程变量设计

系统中使用的主要流程变量包括：

1. **通用变量**
   - `applicantId`：申请人ID
   - `deptId`：部门ID
   - `formData`：表单数据（JSON格式）
   - `approved`：审批结果（布尔值）
   - `comment`：审批意见

2. **特定流程变量**
   - 出差申请：`travelDays`（出差天数）, `destination`（目的地）
   - 请假申请：`leaveDays`（请假天数）, `leaveType`（请假类型）
   - 会议申请：`meetingType`（会议类型）, `attendees`（参会人数）
   - 办公用品申请：`totalAmount`（总金额）, `itemCount`（物品数量）

### 流程事件监听

系统使用Camunda的事件监听机制实现流程的自定义行为：

1. **执行监听器**
   ```java
   @Component
   public class TravelProcessStartListener implements ExecutionListener {
       @Override
       public void notify(DelegateExecution execution) {
           // 流程启动时的处理逻辑
           String businessKey = execution.getBusinessKey();
           // 记录流程开始日志
           // ...
       }
   }
   ```

2. **任务监听器**
   ```java
   @Component
   public class DeptLeaderAssignmentListener implements TaskListener {
       @Autowired
       private UserService userService;
       
       @Override
       public void notify(DelegateTask delegateTask) {
           // 获取申请人部门
           Long deptId = (Long) delegateTask.getExecution().getVariable("deptId");
           // 查询部门负责人
           String deptLeaderId = userService.getDeptLeaderByDeptId(deptId);
           // 设置任务受理人
           delegateTask.setAssignee(deptLeaderId);
       }
   }
   ```

3. **任务完成监听器**
   ```java
   @Component
   public class TaskCompletionListener implements TaskListener {
       @Autowired
       private BusinessService businessService;
       
       @Override
       public void notify(DelegateTask delegateTask) {
           if (delegateTask.getEventName().equals(EVENTNAME_COMPLETE)) {
               String taskDefinitionKey = delegateTask.getTaskDefinitionKey();
               String businessKey = delegateTask.getExecution().getBusinessKey();
               Boolean approved = (Boolean) delegateTask.getVariable("approved");
               
               // 更新业务数据状态
               businessService.updateTaskStatus(businessKey, taskDefinitionKey, approved);
           }
       }
   }
   ```

### 委托表达式和服务任务

系统使用委托表达式和服务任务处理复杂业务逻辑：

1. **委托表达式**
   ```xml
   <bpmn:serviceTask id="send_notification_task" name="发送通知" 
     camunda:delegateExpression="${notificationDelegate}">
     <bpmn:incoming>flow_from_approval</bpmn:incoming>
     <bpmn:outgoing>flow_to_end</bpmn:outgoing>
   </bpmn:serviceTask>
   ```

2. **服务任务实现**
   ```java
   @Component("notificationDelegate")
   public class NotificationDelegate implements JavaDelegate {
       @Autowired
       private NotificationService notificationService;
       
       @Override
       public void execute(DelegateExecution execution) throws Exception {
           String applicantId = (String) execution.getVariable("applicantId");
           Boolean approved = (Boolean) execution.getVariable("approved");
           
           // 发送通知
           notificationService.sendProcessResultNotification(applicantId, approved);
       }
   }
   ```

## 流程监控与分析

系统利用Camunda提供的API实现流程监控和分析功能：

1. **流程实例查询**
   ```java
   List<ProcessInstance> instances = runtimeService.createProcessInstanceQuery()
       .processDefinitionKey("travel_application_process")
       .active()
       .list();
   ```

2. **任务查询**
   ```java
   List<Task> tasks = taskService.createTaskQuery()
       .taskAssignee(userId)
       .active()
       .orderByTaskCreateTime().desc()
       .list();
   ```

3. **历史数据查询**
   ```java
   List<HistoricProcessInstance> historicInstances = 
       historyService.createHistoricProcessInstanceQuery()
           .processDefinitionKey("travel_application_process")
           .finished()
           .orderByProcessInstanceEndTime().desc()
           .list();
   ```

4. **流程统计**
   ```java
   // 按月统计流程数量
   Map<String, Long> monthlyCount = historyService.createHistoricProcessInstanceQuery()
       .processDefinitionKey("travel_application_process")
       .finished()
       .list()
       .stream()
       .collect(Collectors.groupingBy(
           instance -> DateFormatUtils.format(instance.getStartTime(), "yyyy-MM"),
           Collectors.counting()
       ));
   ```

## 结论

基于Camunda流程引擎和BPMN 2.0标准的流程设计，为系统提供了标准化、可视化的工作流管理能力。系统可以灵活响应业务变化，支持流程的动态调整和版本控制，同时提供完善的流程监控和分析功能。 