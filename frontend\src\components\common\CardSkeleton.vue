<template>
  <div class="card-skeleton" :style="{ height: height }">
    <!-- 卡片头部 -->
    <div v-if="showHeader" class="card-header">
      <div class="header-left">
        <SkeletonLoader
          v-if="showAvatar"
          type="circle"
          :width="avatarSize"
          :height="avatarSize"
        />
        <div class="header-content">
          <SkeletonLoader type="text" width="150px" height="18px" />
          <SkeletonLoader v-if="showSubtitle" type="text" width="100px" height="14px" />
        </div>
      </div>
      <div v-if="showHeaderAction" class="header-right">
        <SkeletonLoader type="button" width="60px" height="32px" />
      </div>
    </div>
    
    <!-- 卡片内容 -->
    <div class="card-content">
      <slot>
        <!-- 默认内容 -->
        <div v-if="contentType === 'text'" class="text-content">
          <SkeletonLoader
            v-for="i in contentRows"
            :key="i"
            type="text"
            width="100%"
            height="16px"
          />
        </div>
        
        <div v-else-if="contentType === 'image'" class="image-content">
          <SkeletonLoader type="image" width="100%" :height="imageHeight" />
          <div class="image-caption">
            <SkeletonLoader type="text" width="80%" height="16px" />
          </div>
        </div>
        
        <div v-else-if="contentType === 'list'" class="list-content">
          <div
            v-for="i in listItems"
            :key="i"
            class="list-item"
          >
            <SkeletonLoader
              v-if="showListItemAvatar"
              type="circle"
              width="32px"
              height="32px"
            />
            <div class="list-item-content">
              <SkeletonLoader type="text" width="70%" height="16px" />
              <SkeletonLoader type="text" width="50%" height="14px" />
            </div>
          </div>
        </div>
      </slot>
    </div>
    
    <!-- 卡片底部 -->
    <div v-if="showFooter" class="card-footer">
      <SkeletonLoader
        v-for="i in footerButtons"
        :key="i"
        type="button"
        width="80px"
        height="32px"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import SkeletonLoader from './SkeletonLoader.vue'

const props = defineProps({
  /**
   * 卡片高度
   */
  height: {
    type: String,
    default: 'auto'
  },
  /**
   * 是否显示头部
   */
  showHeader: {
    type: Boolean,
    default: true
  },
  /**
   * 是否显示头部操作按钮
   */
  showHeaderAction: {
    type: Boolean,
    default: true
  },
  /**
   * 是否显示头像
   */
  showAvatar: {
    type: Boolean,
    default: true
  },
  /**
   * 头像大小
   */
  avatarSize: {
    type: String,
    default: '40px'
  },
  /**
   * 是否显示副标题
   */
  showSubtitle: {
    type: Boolean,
    default: true
  },
  /**
   * 内容类型
   */
  contentType: {
    type: String,
    default: 'text',
    validator: (value: string) => {
      return ['text', 'image', 'list'].includes(value)
    }
  },
  /**
   * 文本内容行数
   */
  contentRows: {
    type: Number,
    default: 4
  },
  /**
   * 图片高度
   */
  imageHeight: {
    type: String,
    default: '200px'
  },
  /**
   * 列表项数量
   */
  listItems: {
    type: Number,
    default: 3
  },
  /**
   * 是否显示列表项头像
   */
  showListItemAvatar: {
    type: Boolean,
    default: true
  },
  /**
   * 是否显示底部
   */
  showFooter: {
    type: Boolean,
    default: true
  },
  /**
   * 底部按钮数量
   */
  footerButtons: {
    type: Number,
    default: 2
  }
})
</script>

<style scoped>
.card-skeleton {
  width: 100%;
  border-radius: 8px;
  background-color: var(--el-bg-color);
  box-shadow: var(--el-box-shadow-light);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-content {
  flex: 1;
  padding: 16px;
}

.text-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.image-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.image-caption {
  margin-top: 8px;
}

.list-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.list-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}
</style>
