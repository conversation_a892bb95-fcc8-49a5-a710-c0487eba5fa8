import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'

/**
 * Excel表头配置接口
 */
export interface ExcelHeader {
  /** 表头显示文本 */
  header: string
  /** 对应数据字段名 */
  key: string
  /** 列宽（可选） */
  width?: number
}

/**
 * Excel导出选项接口
 */
export interface ExcelExportOptions {
  /** 文件名 */
  fileName: string
  /** 工作表名称 */
  sheetName?: string
  /** 是否自动调整列宽 */
  autoWidth?: boolean
  /** 是否包含样式 */
  withStyle?: boolean
}

/**
 * 导出数据为Excel文件
 * @param data 要导出的数据
 * @param headers 表头配置
 * @param fileName 文件名称或导出选项
 * @returns 是否导出成功
 */
export function exportToExcel<T extends Record<string, any>>(
  data: T[],
  headers: ExcelHeader[],
  fileNameOrOptions: string | ExcelExportOptions
): boolean {
  // 处理选项
  let fileName: string
  let sheetName = 'Sheet1'
  let autoWidth = false
  let withStyle = false

  if (typeof fileNameOrOptions === 'string') {
    fileName = fileNameOrOptions || 'export'
  } else {
    fileName = fileNameOrOptions.fileName || 'export'
    sheetName = fileNameOrOptions.sheetName || 'Sheet1'
    autoWidth = fileNameOrOptions.autoWidth || false
    withStyle = fileNameOrOptions.withStyle || false
  }

  // 确保文件名有正确的扩展名
  if (!fileName.endsWith('.xlsx')) {
    fileName += '.xlsx'
  }

  try {
    // 构建工作表数据
    const worksheet = XLSX.utils.json_to_sheet(data, {
      header: headers.map(h => h.key)
    })

    // 设置列宽和表头
    const colWidth: { [key: string]: { wch: number } } = {}
    const range = XLSX.utils.decode_range(worksheet['!ref'] as string)

    // 添加表头行
    const headerRow: { [key: string]: string } = {}
    headers.forEach(h => {
      headerRow[h.key] = h.header

      // 设置列宽
      let width = h.width || 15

      // 如果启用了自动列宽，计算最大内容宽度
      if (autoWidth) {
        // 计算表头宽度
        const headerWidth = h.header.toString().length * 2

        // 计算数据宽度
        let maxDataWidth = 0
        data.forEach(row => {
          const cellValue = row[h.key]
          if (cellValue != null) {
            const cellWidth = cellValue.toString().length
            maxDataWidth = Math.max(maxDataWidth, cellWidth)
          }
        })

        // 取表头宽度和数据宽度的最大值，加上一些边距
        width = Math.max(headerWidth, maxDataWidth) + 2
      }

      const col = XLSX.utils.encode_col(headers.findIndex(header => header.key === h.key))
      colWidth[col] = { wch: width }
    })

    // 先保存原数据范围
    const dataRange = { ...range }

    // 将表头插入到工作表的第一行
    XLSX.utils.sheet_add_json(worksheet, [headerRow], {
      skipHeader: true,
      origin: 'A1'
    })

    // 调整范围，将表头行包含进来
    range.s.r = 0
    worksheet['!ref'] = XLSX.utils.encode_range(range)

    // 设置列宽
    worksheet['!cols'] = Object.values(colWidth)

    // 创建工作簿并添加工作表
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)

    // 生成Excel二进制数据
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })

    // 保存为文件
    const blob = new Blob([excelBuffer], { type: 'application/octet-stream' })
    saveAs(blob, fileName)

    return true
  } catch (error) {
    console.error('导出Excel失败:', error)
    return false
  }
}
