# 11-Flowable6工作流审批流转接口规范

## 目录
1. [概述](#概述)
   - [技术架构](#技术架构)
2. [核心接口说明](#核心接口说明)
   - [工作流引擎主要功能](#工作流引擎主要功能)
3. [领域对象实体与流程任务信息实体联合查询](#领域对象实体与流程任务信息实体联合查询)
4. [IBusinessFormService接口详解](#ibusinessformservice接口详解)
   - [核心方法说明](#核心方法说明)
   - [使用注意事项](#使用注意事项)
5. [工作流审批流转最佳实践](#工作流审批流转最佳实践)
   - [标准流程实现步骤](#1-标准流程实现步骤)
   - [推荐的异常处理方式](#2-推荐的异常处理方式)
6. [注意事项与常见问题](#注意事项与常见问题)
   - [关键注意事项](#1-关键注意事项)

## 概述

本文档主要介绍基于`simbest-boot-wf`和`simbest-boot-wfdriver-v0.2`两个项目的工作流审批流转接口使用说明。这两个项目是SIMBEST团队开发的工作流引擎核心组件，用于实现企业级工作流管理系统。

### 技术架构
- 核心引擎：simbest-boot-wf
- 驱动适配层：simbest-boot-wfdriver-v0.2
- 基础支持：Spring Boot
- 数据持久化：JPA
- 接口规范：RESTful API

## 核心接口说明

### 工作流引擎主要功能
1. 流程定义管理

2. 流程实例管理

3. 任务管理

4. 表单数据管理

5. 流程监控与追踪


## 领域对象实体与流程任务信息实体联合查询

基于Flowable流程引擎封装的流程任务信息ActTaskInstModel ，该对象保存了流程任务实例信息数据。所有流程级的领域对象与流程任务信息ActTaskInstModel进行关联查询，流程任务信息ActTaskInstModel重要字段如下：

- ActTaskInstModel对象的businessKey 与领域对象的主单据ID字段pmInsId通过JPQL关联查询
- ActTaskInstModel对象的assignee字段为办理人OA账号
- ActTaskInstModel对象的taskDefinitionKey字段为任务环节信息
- ActTaskInstModel对象的taskCreateTime 字段为任务创建时间

## IBusinessFormService接口详解

`IBusinessFormService`是工作流引擎中最核心的业务表单处理接口，主要用于处理工作流中的表单数据操作。

### 核心方法说明

1. **saveAndUpdateFormData**
```java
Map<String, Object> saveAndUpdateFormData(Map<String, Object> formMap)
```
- 功能：保存或更新表单数据
- 参数说明：
  - formMap：表单数据Map，详见下文《formMap 表单参数说明》
- 返回值：formData实体对象表单数据转Map格式

#### formMap 表单参数说明

| 参数名 | 说明 | 是否标准字段 | 示例值 |
|--------|------|------------|--------|
| appCode | 应用编码 | 是 | czhydd |
| pmInsType | 主单据类型 | 是 | - |
| processDefKey | 流程定义ID键 | 是 | Process_1711980352718 |
| activityDefId | 当前活动定义ID | 是 | czhydd.start |
| type | 流转类型(START:流程启动, 其他值:流程流转) | 是 | START |
| taskDefinitionKey | 下一环节配置信息 | 是 | Activity_1e6pney,发起部门主管审批,one |
| title | 标题 | 是 | 测试数据请忽略-0001 |
| outcome | 决策项编码 | 是 | Flow_1eaalem |
| nextUser | 下一步审批人账号(多人用逗号分隔) | 是 | zhangsan |
| nextUserName | 下一审批人姓名 | 是 | 张三 |
| nextUserOrgCode | 下一审批人组织编码(多个用逗号分隔) | 是 | 1042318568061423616 |
| nextUserPostId | 下一审批人职位编码(非必填,多个用逗号分隔) | 是 | - |
| nextActivityParam | 下一环节接办理人类型 | 是 | inputUserId |
| message | 审批意见 | 是 | 请主管审批 |
| formData | 实体对象表单数据 | 是 | object |

##### formData实体对象表单数据（字段根据实体对象属性变化）

| 参数名 | 说明 | 是否标准字段 | 示例值 |
|--------|------|------------|--------|
| pmInsId | 主单据ID | 是 | - |
| id | 实体对象主键ID | 是 | - |
| assignDept | 分配部门 | 否 | 战略客户拓展中心 |
| sendTime | 发送时间 | 否 | 2025-03-29 09:10:57 |
| urgencyLevel | 紧急程度 | 否 | 0 |
| fileList | 内容描述 | 否 | 附件列表数据 |

###### 文件、附件信息(fileList)参数

| 参数名 | 说明 | 是否标准字段 | 示例值 |
|--------|------|------------|--------|
| id | 文件ID | 否 | F848126405736247296 |
| fileName | 文件名称 | 否 | 接口情况梳理.xlsx |
| fileType | 文件类型 | 否 | xlsx |
| fileSize | 文件大小(字节) | 否 | 9727 |
| downLoadUrl | 下载路径 | 否 | /sys/file/download?id=F848126405736247296 |
| mobileFilePath | 移动端文件路径 | 否 | http://{{host}}:{{port}}/{{appcode}}/sys/file/download/anonymous?id=F848126405736247296 |
| apiFilePath | API文件路径 | 否 | http://{{host}}:{{port}}/{{appcode}}/sys/file/download/api?id=F848126405736247296 |
| storeLocation | 存储方式 | 否 | disk |

2. **deleteFormData**
```java
void deleteFormData(Map<String, Object> formMap)
```
- 功能：删除表单数据
- 参数说明：
  - formMap：包含删除条件的表单数据Map
  - 必须参数：
    - pmInsId：主单据ID
    - pmInstType：主单据类型
    - activityDefId：活动定义ID

3. **getFormDetail**
```java
Map<String, Object> getFormDetail(Map<String, Object> paramMap)
```
- 功能：获取表单详情数据
- 参数说明：
  - paramMap：查询条件Map
  - 必须参数：
    - pmInsId：主单据ID
    - pmInstType：主单据类型
    - activityDefId：活动定义ID
- 返回值：表单详情数据Map

4. **getFormClassType**
```java
List<String> getFormClassType()
```
- 功能：获取表单类型列表
- 返回值：支持的表单类型列表

### 使用注意事项

1. **数据格式规范**
   - 所有输入参数必须使用Map<String, Object>格式
   - 关键字段名称必须严格匹配（pmInsId、pmInstType等）
   - 返回数据统一使用Map格式，便于前端处理

2. **异常处理**
   - 所有接口都应该进行异常捕获
   - 建议使用统一的异常处理机制
   - 关键操作需要添加日志记录

3. **数据校验**
   - 调用接口前必须验证必填参数
   - 参数类型必须符合要求
   - 特殊字符需要进行转义处理

4. **性能考虑**
   - 大量数据处理时建议分页
   - 避免频繁调用getFormDetail方法
   - 合理使用缓存机制

## 工作流审批流转最佳实践

### 1. 标准流程实现步骤
```java
// 1. 初始化表单数据
Map<String, Object> formData = new HashMap<>();
formData.put("pmInsId", "WF2024032500001");
formData.put("pmInstType", "APPROVAL");
formData.put("activityDefId", "ACT001");

// 2. 保存表单数据
businessFormService.saveAndUpdateFormData(formData);

// 3. 获取表单详情
Map<String, Object> detail = businessFormService.getFormDetail(formData);

// 4. 处理业务逻辑
// ... 业务处理代码 ...

// 5. 完成后删除表单（如果需要）
businessFormService.deleteFormData(formData);
```

### 2. 推荐的异常处理方式
```java
try {
    Map<String, Object> result = businessFormService.saveAndUpdateFormData(formMap);
    // 处理正常业务逻辑
} catch (BusinessException be) {
    // 处理业务异常
    log.error("业务处理异常", be);
} catch (Exception e) {
    // 处理系统异常
    log.error("系统异常", e);
}
```

## 注意事项与常见问题

### 1. 关键注意事项
- 所有接口调用必须在事务中进行
- 确保数据一致性和完整性
- 注意并发处理和锁机制
- 重要操作需要记录操作日志

### 2. 常见问题解决方案

#### 问题1：表单数据保存失败
- 检查必填字段是否完整
- 验证数据格式是否正确
- 确认数据库连接是否正常

#### 问题2：获取表单详情返回空
- 确认pmInsId是否正确
- 检查activityDefId是否存在
- 验证权限是否满足要求

#### 问题3：并发处理冲突
- 使用乐观锁机制
- 实现分布式锁
- 添加版本控制字段

### 3. 性能优化建议
- 使用批量处理接口
- 实现缓存机制
- 优化SQL查询
- 合理设置事务边界

---
注：本文档持续更新中，如有问题请及时反馈。 
