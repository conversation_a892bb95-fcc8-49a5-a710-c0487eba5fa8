<template>
  <el-breadcrumb separator="/">
    <el-breadcrumb-item v-for="(item, index) in levelList" :key="item.path" :to="index === 0 ? { path: item.path } : ''">
      <span>{{ item.meta && item.meta.title }}</span>
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import type { RouteLocationMatched } from 'vue-router'

const route = useRoute()
const levelList = ref<RouteLocationMatched[]>([])

/**
 * 获取面包屑导航数据
 */
const getBreadcrumb = () => {
  // 当前路由的匹配数组
  let matched = route.matched.filter(item => item.meta && item.meta.title)
  
  // 如果第一个不是 dashboard，则添加 dashboard 作为第一项
  if (matched.length > 0 && matched[0].path !== '/dashboard') {
    matched = [
      {
        path: '/dashboard',
        meta: { title: '首页' }
      } as unknown as RouteLocationMatched
    ].concat(matched)
  }
  
  // 过滤重复的"首页"项
  const result: RouteLocationMatched[] = []
  let dashboardAdded = false
  
  matched.forEach(item => {
    if (item.meta && item.meta.title === '首页') {
      if (!dashboardAdded) {
        result.push(item)
        dashboardAdded = true
      }
    } else {
      result.push(item)
    }
  })
  
  levelList.value = result
}

// 初始化
getBreadcrumb()

// 路由变化时更新面包屑
watch(
  () => route.path,
  () => getBreadcrumb()
)
</script>

<style scoped>
.el-breadcrumb {
  display: inline-block;
  font-size: 14px;
}
</style>