<template>
  <div class="virtual-list-container">
    <PageHeader title="大数据量销售列表">
      <template #actions>
        <el-button type="primary" @click="generateData">
          <el-icon><Plus /></el-icon>生成测试数据
        </el-button>
        <el-button @click="clearData">
          <el-icon><Delete /></el-icon>清空数据
        </el-button>
      </template>
    </PageHeader>

    <el-card class="data-info-card">
      <div class="data-info">
        <div class="info-item">
          <span class="label">数据总量:</span>
          <span class="value">{{ tableData.length }}</span>
        </div>
        <div class="info-item">
          <span class="label">渲染方式:</span>
          <el-radio-group v-model="renderMode">
            <el-radio-button value="virtual">虚拟滚动</el-radio-button>
            <el-radio-button value="normal">普通表格</el-radio-button>
          </el-radio-group>
        </div>
        <div class="info-item">
          <span class="label">数据量:</span>
          <el-select v-model="dataSize" placeholder="选择数据量">
            <el-option label="1,000 条" :value="1000" />
            <el-option label="10,000 条" :value="10000" />
            <el-option label="50,000 条" :value="50000" />
            <el-option label="100,000 条" :value="100000" />
          </el-select>
        </div>
      </div>
    </el-card>

    <!-- 虚拟滚动表格 -->
    <div v-if="renderMode === 'virtual'" class="table-container">
      <VirtualTable
        :data="tableData"
        :columns="columns"
        :loading="loading"
        :height="600"
        :row-height="48"
        @row-click="handleRowClick"
      >
        <template #status="{ row }">
          <StatusTag
            :status="row.status"
            :status-map="statusMap"
          />
        </template>
        <template #action="{ row, index }">
          <ActionButtons
            :row="row"
            :index="index"
            @view="handleView"
            @edit="handleEdit"
            @delete="handleDelete"
          />
        </template>
      </VirtualTable>
    </div>

    <!-- 普通表格 -->
    <div v-else class="table-container">
      <DataTable
        :data="tableData"
        :loading="loading"
        :total="tableData.length"
        :current-page="1"
        :page-size="tableData.length"
        :show-pagination="false"
        height="600px"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column prop="customerName" label="客户名称" min-width="180" />
        <el-table-column prop="productName" label="产品名称" min-width="150" />
        <el-table-column prop="quantity" label="数量" width="80" align="right" />
        <el-table-column prop="unitPrice" label="单价" width="120" align="right">
          <template #default="{ row }">
            {{ formatCurrency(row.unitPrice) }}
          </template>
        </el-table-column>
        <el-table-column prop="totalAmount" label="总金额" width="120" align="right">
          <template #default="{ row }">
            {{ formatCurrency(row.totalAmount) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <StatusTag
              :status="row.status"
              :status-map="statusMap"
            />
          </template>
        </el-table-column>
        <template #action="{ row, index }">
          <ActionButtons
            :row="row"
            :index="index"
            @view="handleView"
            @edit="handleEdit"
            @delete="handleDelete"
          />
        </template>
      </DataTable>
    </div>

    <!-- 性能指标 -->
    <el-card class="performance-card">
      <template #header>
        <div class="card-header">
          <span>性能指标</span>
          <el-button type="primary" link @click="startPerformanceTest">
            开始性能测试
          </el-button>
        </div>
      </template>
      <div class="performance-metrics">
        <div class="metric-item">
          <span class="label">渲染时间:</span>
          <span class="value">{{ metrics.renderTime }}ms</span>
        </div>
        <div class="metric-item">
          <span class="label">滚动性能:</span>
          <span class="value">{{ metrics.scrollFPS }}FPS</span>
        </div>
        <div class="metric-item">
          <span class="label">内存使用:</span>
          <span class="value">{{ metrics.memoryUsage }}MB</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import { PageHeader, VirtualTable, DataTable, StatusTag, ActionButtons } from '@/components'

// 表格数据
const tableData = ref<any[]>([])
const loading = ref(false)
const renderMode = ref('virtual')
const dataSize = ref(10000)

// 状态映射
const statusMap = {
  approved: { text: '已审核', type: 'success' },
  pending: { text: '待审核', type: 'warning' },
  rejected: { text: '已驳回', type: 'danger' },
  draft: { text: '草稿', type: 'info' }
}

// 虚拟表格列定义
const columns = [
  { title: 'ID', dataIndex: 'id', width: 80 },
  { title: '日期', dataIndex: 'date', width: 120 },
  { title: '客户名称', dataIndex: 'customerName', width: 180 },
  { title: '产品名称', dataIndex: 'productName', width: 150 },
  { title: '数量', dataIndex: 'quantity', width: 80, align: 'right' },
  {
    title: '单价',
    dataIndex: 'unitPrice',
    width: 120,
    align: 'right',
    render: (row: any) => {
      return formatCurrency(row.unitPrice)
    }
  },
  {
    title: '总金额',
    dataIndex: 'totalAmount',
    width: 120,
    align: 'right',
    render: (row: any) => {
      return formatCurrency(row.totalAmount)
    }
  },
  { title: '状态', dataIndex: 'status', width: 100, slot: 'status' },
  { title: '操作', dataIndex: 'action', width: 200, slot: 'action' }
]

// 性能指标
const metrics = reactive({
  renderTime: 0,
  scrollFPS: 0,
  memoryUsage: 0
})

// 格式化货币
const formatCurrency = (value: number) => {
  return `¥ ${value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`
}

// 生成随机数据
const generateData = () => {
  loading.value = true

  // 使用setTimeout避免UI阻塞
  setTimeout(() => {
    const startTime = performance.now()

    const data = []
    const customers = ['北京科技有限公司', '上海电子科技有限公司', '广州信息技术有限公司', '深圳互联网科技有限公司', '杭州软件有限公司']
    const products = ['高级会员服务', '技术支持服务', '广告推广服务', '数据分析服务', '云存储服务']
    const statuses = ['approved', 'pending', 'rejected', 'draft']
    const salesPersons = ['zhangsan', 'lisi', 'wangwu', 'zhaoliu']

    for (let i = 1; i <= dataSize.value; i++) {
      const quantity = Math.floor(Math.random() * 10) + 1
      const unitPrice = Math.floor(Math.random() * 10000) / 100
      const totalAmount = quantity * unitPrice

      data.push({
        id: i,
        date: generateRandomDate(),
        salesperson: salesPersons[Math.floor(Math.random() * salesPersons.length)],
        customerName: customers[Math.floor(Math.random() * customers.length)],
        productName: products[Math.floor(Math.random() * products.length)],
        quantity,
        unitPrice,
        totalAmount,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        remark: `测试数据 ${i}`
      })
    }

    tableData.value = data
    loading.value = false

    const endTime = performance.now()
    metrics.renderTime = Math.round(endTime - startTime)

    ElMessage.success(`成功生成 ${dataSize.value} 条测试数据`)
  }, 0)
}

// 生成随机日期
const generateRandomDate = () => {
  const start = new Date(2023, 0, 1)
  const end = new Date()
  const randomDate = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))

  const year = randomDate.getFullYear()
  const month = String(randomDate.getMonth() + 1).padStart(2, '0')
  const day = String(randomDate.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

// 清空数据
const clearData = () => {
  tableData.value = []
  metrics.renderTime = 0
  metrics.scrollFPS = 0
  metrics.memoryUsage = 0
  ElMessage.info('数据已清空')
}

// 行点击事件
const handleRowClick = (row: any) => {
  console.log('Row clicked:', row)
}

// 查看
const handleView = (row: any) => {
  ElMessage.info(`查看: ${row.customerName}`)
}

// 编辑
const handleEdit = (row: any) => {
  ElMessage.info(`编辑: ${row.customerName}`)
}

// 删除
const handleDelete = (row: any) => {
  ElMessage.success(`删除: ${row.customerName}`)
  // 实际应用中应该调用API删除数据
  tableData.value = tableData.value.filter(item => item.id !== row.id)
}

// 性能测试
let scrollFrames = 0
let scrollStartTime = 0
let scrollTestInterval: number | null = null

const startPerformanceTest = () => {
  // 确保有数据
  if (tableData.value.length === 0) {
    ElMessage.warning('请先生成测试数据')
    return
  }

  // 记录内存使用
  if (window.performance && (performance as any).memory) {
    metrics.memoryUsage = Math.round((performance as any).memory.usedJSHeapSize / (1024 * 1024))
  }

  // 测试滚动性能
  scrollFrames = 0
  scrollStartTime = performance.now()

  // 清除之前的测试
  if (scrollTestInterval) {
    clearInterval(scrollTestInterval)
  }

  // 开始测试
  ElMessage.info('开始性能测试，将在3秒后完成')

  // 模拟滚动
  const scrollElement = document.querySelector('.virtual-table-body') || document.querySelector('.el-table__body-wrapper')
  if (scrollElement) {
    let direction = 1
    let position = 0

    scrollTestInterval = setInterval(() => {
      // 模拟滚动
      position += 10 * direction
      if (position > 5000) {
        direction = -1
      } else if (position < 0) {
        direction = 1
        position = 0
      }

      scrollElement.scrollTop = position
      scrollFrames++
    }, 16) // 约60fps

    // 3秒后结束测试
    setTimeout(() => {
      if (scrollTestInterval) {
        clearInterval(scrollTestInterval)
        scrollTestInterval = null
      }

      const duration = (performance.now() - scrollStartTime) / 1000
      metrics.scrollFPS = Math.round(scrollFrames / duration)

      ElMessage.success('性能测试完成')
    }, 3000)
  }
}

// 生命周期钩子
onMounted(() => {
  // 初始生成一些数据
  generateData()
})

onUnmounted(() => {
  // 清理定时器
  if (scrollTestInterval) {
    clearInterval(scrollTestInterval)
  }
})
</script>

<style scoped>
.virtual-list-container {
  padding: 20px;
}

.data-info-card {
  margin-bottom: 20px;
}

.data-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.info-item {
  display: flex;
  align-items: center;
}

.label {
  margin-right: 10px;
  font-weight: bold;
}

.value {
  font-size: 16px;
  color: var(--el-color-primary);
}

.table-container {
  margin-bottom: 20px;
}

.performance-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.performance-metrics {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
}

.metric-item {
  display: flex;
  align-items: center;
}

.metric-item .value {
  font-size: 18px;
  font-weight: bold;
}
</style>
