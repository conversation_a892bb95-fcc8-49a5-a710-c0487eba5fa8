import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd());
  return {
    plugins: [vue()],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "src"),
      },
    },
    server: {
      port: 3000,
      strictPort: false,
      open: true,
      proxy: {
        "/api": {
          target: env.VITE_APP_BASE_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, "/"),
        },
      },
    },
    build: {
      rollupOptions: {
        output: {
          // 手动分块配置
          manualChunks: {
            "element-plus": ["element-plus"],
            "vue-vendor": ["vue", "vue-router", "pinia"],
            "element-icons": ["@element-plus/icons-vue"],
            chart: ["echarts", "chart.js"],
            utils: ["axios", "lodash-es", "dayjs"],
          },
          // 块文件输出格式
          chunkFileNames: "assets/js/[name]-[hash].js",
          entryFileNames: "assets/js/[name]-[hash].js",
          assetFileNames: "assets/[ext]/[name]-[hash].[ext]",
        },
      },
      // 启用源码映射
      sourcemap: false,
      // CSS代码分割
      cssCodeSplit: true,
      // 启用CSS最小化
      cssMinify: true,
      // 启用构建时压缩
      minify: "terser",
      // Terser配置
      terserOptions: {
        compress: {
          drop_console: true, // 生产环境下移除console
          drop_debugger: true, // 生产环境下移除debugger
        },
      },
    },
    optimizeDeps: {
      // 预构建依赖项
      include: [
        "vue",
        "vue-router",
        "pinia",
        "element-plus",
        "@element-plus/icons-vue",
        "axios",
        "lodash-es",
        "dayjs",
      ],
      // 强制排除的依赖项
      exclude: [],
    },
    base: "./",
  };
});
