<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_3" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.34.0" modeler:executionPlatform="Camunda Cloud" modeler:executionPlatformVersion="8.0.0">
  <bpmn:process id="business-trip-process" name="教育局出差申请流程" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_1" targetRef="dept_leader_approve" />
    <bpmn:userTask id="dept_leader_approve" name="部门负责人审批">
      <bpmn:extensionElements>
        <zeebe:assignmentDefinition assignee="#{assignee}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_2" sourceRef="dept_leader_approve" targetRef="gateway_1" />
    <bpmn:exclusiveGateway id="gateway_1" name="部门审批结果">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
      <bpmn:outgoing>Flow_reject_1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_3" name="通过" sourceRef="gateway_1" targetRef="check_days">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=deptApproved</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_reject_1" name="不通过" sourceRef="gateway_1" targetRef="end_event_fail">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=not(deptApproved)</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="check_days" name="检查出差天数">
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_4" name="大于3天" sourceRef="check_days" targetRef="bureau_leader_approve">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=tripDays &gt; 3</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_5" name="小于等于3天" sourceRef="check_days" targetRef="finance_approve">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=tripDays &lt;= 3</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="bureau_leader_approve" name="分管局长审批">
      <bpmn:extensionElements>
        <zeebe:assignmentDefinition assignee="#{assignee}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_6" sourceRef="bureau_leader_approve" targetRef="gateway_2" />
    <bpmn:exclusiveGateway id="gateway_2" name="局长审批结果">
      <bpmn:incoming>Flow_6</bpmn:incoming>
      <bpmn:outgoing>Flow_7</bpmn:outgoing>
      <bpmn:outgoing>Flow_reject_2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_7" name="通过" sourceRef="gateway_2" targetRef="finance_approve">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=bureauApproved</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_reject_2" name="不通过" sourceRef="gateway_2" targetRef="end_event_fail">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=not(bureauApproved)</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="finance_approve" name="财务审批">
      <bpmn:extensionElements>
        <zeebe:assignmentDefinition assignee="#{assignee}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:incoming>Flow_7</bpmn:incoming>
      <bpmn:outgoing>Flow_8</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_8" sourceRef="finance_approve" targetRef="gateway_3" />
    <bpmn:exclusiveGateway id="gateway_3" name="财务审批结果">
      <bpmn:incoming>Flow_8</bpmn:incoming>
      <bpmn:outgoing>Flow_9</bpmn:outgoing>
      <bpmn:outgoing>Flow_reject_3</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_9" name="通过" sourceRef="gateway_3" targetRef="end_event_success">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=financeApproved</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_reject_3" name="不通过" sourceRef="gateway_3" targetRef="end_event_fail">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">=not(financeApproved)</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="end_event_success" name="申请通过">
      <bpmn:incoming>Flow_9</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="end_event_fail" name="申请未通过">
      <bpmn:incoming>Flow_reject_2</bpmn:incoming>
      <bpmn:incoming>Flow_reject_3</bpmn:incoming>
      <bpmn:incoming>Flow_reject_1</bpmn:incoming>
    </bpmn:endEvent>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="business-trip-process">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_1" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="232" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="275" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1" bpmnElement="dept_leader_approve">
        <dc:Bounds x="240" y="210" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1" bpmnElement="gateway_1" isMarkerVisible="true">
        <dc:Bounds x="395" y="225" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="385" y="195" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_2" bpmnElement="check_days" isMarkerVisible="true">
        <dc:Bounds x="505" y="225" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="495" y="195" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_2" bpmnElement="bureau_leader_approve">
        <dc:Bounds x="620" y="140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_3" bpmnElement="gateway_2" isMarkerVisible="true">
        <dc:Bounds x="785" y="155" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="775" y="125" width="70" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_3" bpmnElement="finance_approve">
        <dc:Bounds x="620" y="300" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_4" bpmnElement="gateway_3" isMarkerVisible="true">
        <dc:Bounds x="785" y="315" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="757" y="305" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_2" bpmnElement="end_event_success">
        <dc:Bounds x="902" y="322" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="898" y="365" width="45" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_3" bpmnElement="end_event_fail">
        <dc:Bounds x="902" y="232" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="892" y="275" width="56" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1_di" bpmnElement="Flow_1">
        <di:waypoint x="188" y="250" />
        <di:waypoint x="240" y="250" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_2_di" bpmnElement="Flow_2">
        <di:waypoint x="340" y="250" />
        <di:waypoint x="395" y="250" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_3_di" bpmnElement="Flow_3">
        <di:waypoint x="445" y="250" />
        <di:waypoint x="505" y="250" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="465" y="232" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_reject_1_di" bpmnElement="Flow_reject_1">
        <di:waypoint x="420" y="275" />
        <di:waypoint x="420" y="410" />
        <di:waypoint x="1000" y="410" />
        <di:waypoint x="1000" y="250" />
        <di:waypoint x="940" y="250" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="416" y="345" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_4_di" bpmnElement="Flow_4">
        <di:waypoint x="530" y="225" />
        <di:waypoint x="530" y="180" />
        <di:waypoint x="620" y="180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="525" y="163" width="40" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_5_di" bpmnElement="Flow_5">
        <di:waypoint x="530" y="275" />
        <di:waypoint x="530" y="340" />
        <di:waypoint x="620" y="340" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="515" y="305" width="60" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_6_di" bpmnElement="Flow_6">
        <di:waypoint x="720" y="180" />
        <di:waypoint x="785" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_7_di" bpmnElement="Flow_7">
        <di:waypoint x="810" y="205" />
        <di:waypoint x="810" y="240" />
        <di:waypoint x="670" y="240" />
        <di:waypoint x="670" y="300" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="724" y="217" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_reject_2_di" bpmnElement="Flow_reject_2">
        <di:waypoint x="835" y="180" />
        <di:waypoint x="920" y="180" />
        <di:waypoint x="920" y="232" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="860" y="162" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_8_di" bpmnElement="Flow_8">
        <di:waypoint x="720" y="340" />
        <di:waypoint x="785" y="340" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_9_di" bpmnElement="Flow_9">
        <di:waypoint x="835" y="340" />
        <di:waypoint x="902" y="340" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="858" y="322" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_reject_3_di" bpmnElement="Flow_reject_3">
        <di:waypoint x="810" y="315" />
        <di:waypoint x="810" y="250" />
        <di:waypoint x="902" y="250" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="815" y="280" width="34" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
