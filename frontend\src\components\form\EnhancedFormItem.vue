<template>
  <el-form-item
    :label="label"
    :prop="prop"
    :required="required"
    :rules="rules"
    :error="error"
    :show-message="showMessage"
    :inline-message="inlineMessage"
    :size="size"
    :data-field="prop"
  >
    <template #label v-if="$slots.label">
      <slot name="label"></slot>
    </template>
    
    <slot></slot>
    
    <template #error v-if="$slots.error">
      <slot name="error" :error="error"></slot>
    </template>
    
    <div v-if="help" class="form-item-help">
      <el-tooltip
        :content="help"
        placement="top"
        :effect="helpTooltipEffect"
      >
        <el-icon class="help-icon"><QuestionFilled /></el-icon>
      </el-tooltip>
    </div>
    
    <div v-if="showCharCount && maxLength" class="char-count" :class="{ 'char-count-exceeded': isExceeded }">
      {{ currentLength }}/{{ maxLength }}
    </div>
  </el-form-item>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { QuestionFilled } from '@element-plus/icons-vue'
import type { FormItemRule } from 'element-plus'

const props = defineProps({
  /**
   * 标签
   */
  label: {
    type: String,
    default: ''
  },
  /**
   * 表单域字段
   */
  prop: {
    type: String,
    default: ''
  },
  /**
   * 是否必填
   */
  required: {
    type: Boolean,
    default: false
  },
  /**
   * 表单验证规则
   */
  rules: {
    type: [Object, Array] as () => FormItemRule | FormItemRule[],
    default: () => []
  },
  /**
   * 表单域验证错误信息
   */
  error: {
    type: String,
    default: ''
  },
  /**
   * 是否显示校验错误信息
   */
  showMessage: {
    type: Boolean,
    default: true
  },
  /**
   * 是否以行内形式展示校验信息
   */
  inlineMessage: {
    type: Boolean,
    default: false
  },
  /**
   * 尺寸
   */
  size: {
    type: String,
    default: ''
  },
  /**
   * 帮助文本
   */
  help: {
    type: String,
    default: ''
  },
  /**
   * 帮助提示效果
   */
  helpTooltipEffect: {
    type: String as () => 'dark' | 'light',
    default: 'dark'
  },
  /**
   * 是否显示字符计数
   */
  showCharCount: {
    type: Boolean,
    default: false
  },
  /**
   * 最大字符数
   */
  maxLength: {
    type: Number,
    default: 0
  },
  /**
   * 当前字符数
   */
  currentLength: {
    type: Number,
    default: 0
  }
})

// 计算是否超出字符限制
const isExceeded = computed(() => {
  return props.maxLength > 0 && props.currentLength > props.maxLength
})
</script>

<style scoped>
.form-item-help {
  display: inline-flex;
  margin-left: 8px;
  color: var(--el-text-color-secondary);
}

.help-icon {
  font-size: 16px;
  cursor: help;
}

.char-count {
  position: absolute;
  right: 0;
  bottom: 0;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-right: 10px;
}

.char-count-exceeded {
  color: var(--el-color-danger);
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-form-item.is-error .el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset;
}

:deep(.el-form-item.is-error .el-textarea__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset;
}

:deep(.el-form-item.is-error .el-input-number__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset;
}

:deep(.el-form-item__error) {
  padding-top: 4px;
  font-size: 13px;
}
</style>
