<template>
  <div class="role-edit-container">
    <PageHeader title="编辑角色">
      <template #actions>
        <el-button @click="goBack">返回</el-button>
      </template>
    </PageHeader>

    <el-card class="form-card" v-loading="loading">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        status-icon
      >
        <el-form-item label="角色名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入角色名称"
            clearable
          />
        </el-form-item>

        <el-form-item label="角色编码" prop="code">
          <el-input
            v-model="formData.code"
            placeholder="请输入角色编码"
            clearable
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            rows="4"
            placeholder="请输入角色描述"
          />
        </el-form-item>

        <el-form-item label="权限" prop="permissions">
          <el-tree
            ref="permissionTreeRef"
            :data="permissionTree"
            show-checkbox
            node-key="id"
            :props="{ label: 'name', children: 'children' }"
            :default-checked-keys="formData.permissions"
            @check="handlePermissionCheck"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            保存
          </el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import type { ElTree } from 'element-plus';
import { PageHeader } from '@/components';
import { getRoleDetail, updateRole, getAllPermissions } from '@/api/modules/role';
import type { UpdateRoleParams, PermissionInfo } from '@/api/modules/role';

const router = useRouter();
const route = useRoute();
const formRef = ref<FormInstance>();
const permissionTreeRef = ref<InstanceType<typeof ElTree>>();

// 角色ID
const roleId = ref<number>(Number(route.params.id) || 0);
// 加载状态
const loading = ref(false);
// 提交状态
const submitting = ref(false);

// 表单数据
const formData = reactive<UpdateRoleParams>({
  name: '',
  code: '',
  status: 'active',
  description: '',
  permissions: [],
});

// 表单验证规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入角色编码', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
});

// 权限树数据
const permissionTree = ref<any[]>([]);

// 获取角色详情
const getDetail = async () => {
  if (!roleId.value) {
    ElMessage.error('角色ID不能为空');
    return;
  }

  loading.value = true;
  try {
    const result = await getRoleDetail(roleId.value);
    if (result) {
      formData.name = result.name;
      formData.code = result.code;
      formData.status = result.status;
      formData.description = result.description;
      formData.permissions = result.permissions || [];
    }
  } catch (error) {
    console.error('获取角色详情失败', error);
    ElMessage.error('获取角色详情失败');
  } finally {
    loading.value = false;
  }
};

// 获取权限列表
const getPermissions = async () => {
  try {
    const permissions = await getAllPermissions();
    // 这里简化处理，实际项目中可能需要将权限列表转换为树形结构
    permissionTree.value = permissions.map(item => ({
      id: item.id,
      name: item.name,
      code: item.code,
      description: item.description
    }));
  } catch (error) {
    console.error('获取权限列表失败', error);
    ElMessage.error('获取权限列表失败');
  }
};

// 处理权限选择
const handlePermissionCheck = (node: any, checkedInfo: any) => {
  formData.permissions = checkedInfo.checkedKeys;
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid, fields) => {
    if (valid) {
      submitting.value = true;
      try {
        await updateRole(roleId.value, formData);
        ElMessage.success('更新成功');
        router.push('/role/list');
      } catch (error) {
        console.error('更新失败', error);
        ElMessage.error('更新失败');
      } finally {
        submitting.value = false;
      }
    } else {
      console.error('表单验证失败', fields);
    }
  });
};

// 重置表单
const resetForm = () => {
  getDetail();
};

// 返回列表页
const goBack = () => {
  router.push('/role/list');
};

// 页面加载时获取数据
onMounted(async () => {
  await Promise.all([getPermissions(), getDetail()]);
});
</script>

<style scoped>
.role-edit-container {
  padding: 20px;
}

.form-card {
  margin-top: 20px;
}
</style>
