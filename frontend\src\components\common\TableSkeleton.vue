<template>
  <div class="table-skeleton">
    <!-- 表头 -->
    <div class="table-header">
      <div
        v-for="(column, index) in columns"
        :key="'header-' + index"
        class="header-cell"
        :style="{ width: column.width || 'auto', flex: column.width ? 'none' : '1' }"
      >
        <SkeletonLoader type="text" :width="column.width || '100%'" height="20px" />
      </div>
    </div>
    
    <!-- 表格内容 -->
    <div class="table-body">
      <div
        v-for="i in rows"
        :key="'row-' + i"
        class="table-row"
      >
        <div
          v-for="(column, j) in columns"
          :key="'cell-' + i + '-' + j"
          class="table-cell"
          :style="{ width: column.width || 'auto', flex: column.width ? 'none' : '1' }"
        >
          <SkeletonLoader
            :type="column.type || 'text'"
            :width="column.cellWidth || '80%'"
            :height="column.cellHeight || '16px'"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SkeletonLoader from './SkeletonLoader.vue'

export interface TableSkeletonColumn {
  width?: string
  cellWidth?: string
  cellHeight?: string
  type?: 'text' | 'circle' | 'rect' | 'button'
}

const props = defineProps({
  /**
   * 列配置
   */
  columns: {
    type: Array as () => TableSkeletonColumn[],
    default: () => [
      { width: '80px' },
      { width: '120px' },
      { width: '150px' },
      { width: '200px' },
      { width: '120px' },
      { width: '150px' }
    ]
  },
  /**
   * 行数
   */
  rows: {
    type: Number,
    default: 5
  }
})
</script>

<style scoped>
.table-skeleton {
  width: 100%;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background-color: var(--el-fill-color-light);
  padding: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.header-cell {
  padding: 0 8px;
}

.table-body {
  padding: 8px 0;
}

.table-row {
  display: flex;
  padding: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 0 8px;
}
</style>
