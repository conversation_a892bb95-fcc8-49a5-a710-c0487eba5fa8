# 5-框架统一控制器层接口规范

本文档描述了SIMBEST框架中三个基础控制器层的说明。

## 1. GenericController（基础实体通用控制器）

| 接口名称 | 接口类型 | 接口含义 | 继承关系 |
|---------|---------|---------|---------|
| GenericController | 基础控制器 | 提供通用实体的基础控制功能，包括：<br>- 提供标准的CRUD（创建、查询、更新、删除）接口<br>- 支持多种查询方式：主键查询、条件查询、分页查询<br>- 支持单条和批量记录操作<br>- 统一的响应格式和异常处理<br>- 支持SSO和API多种访问方式<br>- 完整的Swagger接口文档<br>- 可被其他业务控制器继承复用 | 无 |

### 主要方法说明

| 方法名称 | 方法注释 | 输入参数 | 输出结果 |
|---------|---------|---------|---------|
| findById | 根据主键ID查询单条记录 | id：记录的主键ID | JsonResponse：包含实体对象数据的查询结果 |
| findOne | 根据实体对象的非空属性作为条件查询单条记录 | o：包含查询条件的实体对象 | JsonResponse：包含符合条件的单条记录 |
| findAll | 根据实体对象的非空属性作为条件进行分页查询 | page：当前页码（默认1）<br>size：每页记录数（默认10）<br>direction：排序方向（asc/desc）<br>properties：排序字段名称<br>o：包含查询条件的实体对象 | JsonResponse：包含分页信息和数据列表 |
| findAllNoPage | 根据实体对象的非空属性作为条件查询所有记录（不分页） | o：包含查询条件的实体对象 | JsonResponse：包含符合条件的所有记录 |
| findAllSortNoPage | 根据实体对象的非空属性作为条件查询所有记录（带排序不分页） | direction：排序方向（asc/desc）<br>properties：排序字段名称<br>o：包含查询条件的实体对象 | JsonResponse：包含排序后的所有记录 |
| create | 创建一条新记录 | o：待创建的实体对象 | JsonResponse：包含创建成功的实体对象 |
| update | 更新一条记录 | newObj：包含更新内容的新实体对象 | JsonResponse：包含更新后的实体对象 |
| deleteById | 根据主键ID删除一条记录 | id：待删除记录的主键ID | JsonResponse：删除结果 |

## 2. SystemController（系统实体控制器）

| 接口名称 | 接口类型 | 接口含义 | 继承关系 |
|---------|---------|---------|---------|
| SystemController | 系统控制器 | 提供系统实体的控制功能，包括：<br>- 继承GenericController，具备通用实体的基础操作能力<br>- 适用于系统级基础实体的管理<br>- 提供创建时间和修改时间的自动维护<br>- 作为业务实体控制器的基础类<br>- 统一的响应格式和异常处理<br>- 完整的Swagger接口文档<br>- 可被其他系统控制器继承复用 | 继承自GenericController |

### 主要方法说明
继承自GenericController的所有方法，并自动处理创建时间和修改时间字段。

## 3. LogicController（业务实体控制器）

| 接口名称 | 接口类型 | 接口含义 | 继承关系 |
|---------|---------|---------|---------|
| LogicController | 业务控制器 | 提供业务实体的控制功能，支持逻辑删除，包括：<br>- 继承GenericController，具备通用实体的基础操作能力<br>- 提供业务实体的可用性管理（启用/禁用）<br>- 支持实体的逻辑删除（软删除）<br>- 提供SSO和API双重访问支持<br>- 统一的响应格式和异常处理<br>- 完整的Swagger接口文档<br>- 可被其他业务控制器继承复用 | 继承自GenericController |

### 主要方法说明

| 方法名称 | 方法注释 | 输入参数 | 输出结果 |
|---------|---------|---------|---------|
| updateEnable | 更新实体的可用状态 | id：实体主键ID<br>enabled：是否可用 | JsonResponse：包含更新后的实体对象 |

## 继承关系

- LogicController 继承自 GenericController
- SystemController 继承自 GenericController

## 说明

1. GenericController是所有控制器的基础，提供通用的控制功能
2. SystemController在GenericController基础上增加了系统实体的特定功能
3. LogicController在GenericController基础上增加了逻辑删除和业务实体的特定功能
4. 所有控制器都支持事务操作，默认所有方法都在事务中执行
5. 所有控制器都使用泛型参数T（实体类型）和PK（主键类型）
6. 所有控制器都支持SSO和API双重访问方式
7. 所有控制器都提供完整的Swagger接口文档
8. 所有控制器都使用统一的JsonResponse响应格式
9. 所有控制器都支持全局异常处理
10. 所有控制器都支持继承复用，便于扩展 