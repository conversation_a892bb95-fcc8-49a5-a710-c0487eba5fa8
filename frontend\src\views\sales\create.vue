<template>
  <div class="page-container">
    <div class="page-header">
      <h2 class="page-title">添加销售数据</h2>
      <p class="page-description">创建新的销售数据记录</p>
    </div>

    <el-card shadow="hover">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="140px"
        label-position="right"
        class="sales-form"
      >
        <el-divider content-position="left">基本信息</el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="转账时间" prop="transfer_time">
              <el-date-picker
                v-model="form.transfer_time"
                type="date"
                placeholder="选择日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                :clearable="false"
                :editable="false"
                :shortcuts="dateShortcuts"
                :teleported="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="转账类型" prop="transfer_type">
              <el-select v-model="form.transfer_type" placeholder="请选择转账类型" style="width: 100%">
                <el-option label="对公" value="对公" />
                <el-option label="对私" value="对私" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="left">账户信息</el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="转入方账户名称" prop="recipient_account_name">
              <el-input v-model="form.recipient_account_name" placeholder="请输入账户名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="转入方账户ID" prop="recipient_account_id">
              <el-input v-model="form.recipient_account_id" placeholder="请输入账户ID" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="left">业务信息</el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="渠道/销售名称" prop="channel_sales_name">
              <el-input v-model="form.channel_sales_name" placeholder="请输入渠道或销售名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="person_in_charge">
              <el-input v-model="form.person_in_charge" placeholder="请输入负责人姓名" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="业务平台" prop="business_platform">
              <el-select v-model="form.business_platform" placeholder="请选择业务平台" style="width: 100%">
                <el-option label="巨量千川" value="巨量千川" />
                <el-option label="巨量本地推" value="巨量本地推" />
                <el-option label="巨量广告" value="巨量广告" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="业务备注" prop="business_remarks">
              <el-input v-model="form.business_remarks" placeholder="请输入业务备注" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="left">政策与金额信息</el-divider>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="户币" prop="currency">
              <el-input-number
                v-model="form.currency"
                :precision="2"
                :step="100"
                :min="0"
                controls-position="right"
                style="width: 100%"
                @change="calculateAmounts"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="端口政策(百分比)" prop="port_policy">
              <el-tooltip
                content="端口政策以百分比形式输入，如 2 表示 2%"
                placement="top"
                effect="light"
              >
                <el-input-number
                  v-model="form.port_policy"
                  :precision="3"
                  :step="0.1"
                  :min="0"
                  :max="100"
                  controls-position="right"
                  style="width: 100%"
                  @change="calculateAmounts"
                />
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户政策(百分比)" prop="customer_policy">
              <el-tooltip
                content="客户政策以百分比形式输入，如 2 表示 2%"
                placement="top"
                effect="light"
              >
                <el-input-number
                  v-model="form.customer_policy"
                  :precision="2"
                  :step="1"
                  :min="0"
                  :max="100"
                  controls-position="right"
                  style="width: 100%"
                  @change="calculateAmounts"
                />
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="端口充值款" prop="port_recharge">
              <el-tooltip
                content="自动计算：户币/(1+端口政策/100)"
                placement="top"
                effect="light"
              >
                <el-input-number
                  v-model="form.port_recharge"
                  :precision="2"
                  :step="100"
                  :min="0"
                  controls-position="right"
                  style="width: 100%"
                  :disabled="true"
                />
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="应收款" prop="receivable_amount">
              <el-tooltip
                content="自动计算：户币/(1+客户政策/100)"
                placement="top"
                effect="light"
              >
                <el-input-number
                  v-model="form.receivable_amount"
                  :precision="2"
                  :step="100"
                  :min="0"
                  controls-position="right"
                  style="width: 100%"
                  :disabled="true"
                />
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="利润" prop="profit">
              <el-tooltip
                content="自动计算：应收款-端口充值款"
                placement="top"
                effect="light"
              >
                <el-input-number
                  v-model="form.profit"
                  :precision="2"
                  :step="100"
                  :min="0"
                  controls-position="right"
                  style="width: 100%"
                  :disabled="true"
                />
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="left">资金信息</el-divider>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="备款" prop="reserve_fund">
              <el-tooltip
                content="自动计算：来款-应收款"
                placement="top"
                effect="light"
              >
                <el-input-number
                  v-model="form.reserve_fund"
                  :precision="2"
                  :step="100"
                  controls-position="right"
                  style="width: 100%"
                  :disabled="true"
                />
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="自行垫款" prop="self_advance">
              <el-tooltip
                :content="form.transfer_type === '对公' ? '自动计算：户币-端口充值款' : '自动计算：户币-户币/(1+端口政策/100-0.025)'"
                placement="top"
                effect="light"
              >
                <el-input-number
                  v-model="form.self_advance"
                  :precision="2"
                  :step="100"
                  controls-position="right"
                  style="width: 100%"
                  :disabled="true"
                />
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="资方垫款" prop="capital_advance">
              <el-tooltip
                :content="form.transfer_type === '对公' ? '对公转账时资方垫款为0' : '自动计算：户币-应收款-自行垫款'"
                placement="top"
                effect="light"
              >
                <el-input-number
                  v-model="form.capital_advance"
                  :precision="2"
                  :step="100"
                  controls-position="right"
                  style="width: 100%"
                  :disabled="true"
                />
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="left">到账信息</el-divider>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="到账时间" prop="arrival_time">
              <el-date-picker
                v-model="form.arrival_time"
                type="date"
                placeholder="选择到账时间"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                :clearable="true"
                :editable="false"
                :shortcuts="dateShortcuts"
                :teleported="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="到账金额" prop="arrival_amount">
              <el-input-number
                v-model="form.arrival_amount"
                :precision="2"
                :step="100"
                :min="0"
                controls-position="right"
                style="width: 100%"
                @change="calculateOutstanding"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="欠款金额" prop="outstanding_amount">
              <el-tooltip
                content="自动计算：应收款-到账金额"
                placement="top"
                effect="light"
              >
                <el-input-number
                  v-model="form.outstanding_amount"
                  :precision="2"
                  :step="100"
                  :min="0"
                  controls-position="right"
                  style="width: 100%"
                  :disabled="true"
                />
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="到账名称" prop="arrival_name">
              <el-input v-model="form.arrival_name" placeholder="请输入到账名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款人" prop="collector">
              <el-input v-model="form.collector" placeholder="请输入收款人" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="资金流向" prop="capital_flow">
              <el-input v-model="form.capital_flow" placeholder="请输入资金流向" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="稽核状态" prop="audit_status">
              <el-tooltip
                content="新建数据默认为未稽核状态，需要在稽核页面进行稽核操作"
                placement="top"
                effect="light"
              >
                <el-input
                  v-model="form.audit_status"
                  placeholder="未稽核"
                  :disabled="true"
                />
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="附件" prop="attachments">
          <el-upload
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            :limit="5"
            :on-exceed="handleExceed"
            :on-change="handleFileChange"
            :file-list="fileList"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
          <template #tip>
            <div class="el-upload__tip">支持jpg/png格式，单个文件不超过2MB</div>
          </template>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitting">保存</el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="goBack">返回</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import type { UploadUserFile } from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

const router = useRouter()
const formRef = ref<FormInstance>()
const submitting = ref(false)
const fileList = ref<UploadUserFile[]>([])

// 日期选择快捷方式
const dateShortcuts = [
  {
    text: '今天',
    value: new Date(),
  },
  {
    text: '昨天',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() - 3600 * 1000 * 24)
      return date
    },
  },
  {
    text: '一周前',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
      return date
    },
  },
]

// 表单数据
const form = reactive({
  transfer_time: '',
  transfer_type: '',
  recipient_account_name: '',
  recipient_account_id: '',
  channel_sales_name: '',
  person_in_charge: '',
  business_platform: '',
  business_remarks: '',
  currency: 0,
  port_policy: 0,
  customer_policy: 0,
  port_recharge: 0,
  receivable_amount: 0,
  reserve_fund: 0,
  self_advance: 0,
  capital_advance: 0,
  profit: 0,
  arrival_time: '',
  arrival_amount: 0,
  arrival_name: '',
  collector: '',
  outstanding_amount: 0,
  capital_flow: '',
  audit_status: '未稽核',
  attachments: [],
  remark: ''
})

// 表单验证规则
const rules = reactive<FormRules>({
  // 必填字段
  transfer_time: [
    { required: true, message: '请选择转账时间', trigger: 'blur' }
  ],
  transfer_type: [
    { required: true, message: '请选择转账类型', trigger: 'change' }
  ],
  recipient_account_name: [
    { required: true, message: '请输入转入方账户名称', trigger: 'blur' }
  ],
  recipient_account_id: [
    { required: true, message: '请输入转入方账户ID', trigger: 'blur' }
  ],
  channel_sales_name: [
    { required: true, message: '请输入渠道/销售名称', trigger: 'blur' }
  ],
  person_in_charge: [
    { required: true, message: '请输入负责人', trigger: 'blur' }
  ],
  business_platform: [
    { required: true, message: '请选择业务平台', trigger: 'change' }
  ],
  currency: [
    { required: true, message: '请输入户币', trigger: 'blur' }
  ],
  port_policy: [
    { required: true, message: '请输入端口政策', trigger: 'blur' }
  ],
  customer_policy: [
    { required: true, message: '请输入客户政策', trigger: 'blur' }
  ],

  // 自动计算字段
  port_recharge: [
    { required: false, message: '端口充值款将自动计算', trigger: 'blur' }
  ],
  receivable_amount: [
    { required: false, message: '应收款将自动计算', trigger: 'blur' }
  ],
  profit: [
    { required: false, message: '利润将自动计算', trigger: 'blur' }
  ],

  // 稽核状态
  audit_status: [
    { required: true, message: '稽核状态默认为未稽核', trigger: 'change' }
  ]
})

// 处理文件上传
const handleFileChange = (file: UploadUserFile) => {
  fileList.value.push(file)
}

// 处理超出文件限制
const handleExceed = () => {
  ElMessage.warning('最多只能上传5个文件')
}

// 提交表单
const submitForm = () => {
  if (!formRef.value) return

  formRef.value.validate((valid) => {
    if (valid) {
      submitting.value = true

      try {
        // 准备提交数据
        const formData = new FormData()

        // 添加表单字段
        const requiredFields = [
          'transfer_time',
          'recipient_account_name',
          'recipient_account_id',
          'channel_sales_name',
          'person_in_charge',
          'business_platform',
          'transfer_type',
          'currency',
          'port_policy',
          'customer_policy'
        ]

        // 首先添加必填字段
        requiredFields.forEach(key => {
          formData.append(key, form[key])
        })

        // 添加可选字段
        const optionalFields = [
          'arrival_time',
          'arrival_amount',
          'arrival_name',
          'collector',
          'capital_flow',
          'remark',
          'business_remarks'
        ]

        optionalFields.forEach(key => {
          if (form[key] !== null && form[key] !== undefined && form[key] !== '') {
            formData.append(key, form[key])
          }
        })

        // 添加附件
        fileList.value.forEach(file => {
          if (file.raw) {
            formData.append('files', file.raw)
          }
        })

        // 发送请求到后端API
        fetch('/api/sales', {
          method: 'POST',
          body: formData,
          headers: {
            // 不设置Content-Type，让浏览器自动设置multipart/form-data
            'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
          }
        })
        .then(response => {
          if (!response.ok) {
            return response.json().then(errorData => {
              throw new Error(errorData.detail || '添加失败')
            })
          }
          return response.json()
        })
        .then(() => {
          ElMessage.success('添加成功')
          router.push('/sales/list')
        })
        .catch(error => {
          console.error('添加销售数据失败', error)
          ElMessage.error(error.message || '添加销售数据失败')
        })
        .finally(() => {
          submitting.value = false
        })
      } catch (error) {
        console.error('添加销售数据失败', error)
        ElMessage.error('添加销售数据失败')
        submitting.value = false
      }
    } else {
      ElMessage.error('请完善表单信息')
    }
  })
}

// 重置表单
const resetForm = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
  fileList.value = []
}

// 返回列表页
const goBack = () => {
  router.push('/sales/list')
}

// 添加销售数据
const goToCreate = () => {
  router.push('/sales/create')
}

// 编辑销售数据
const handleEdit = (row: any) => {
  router.push(`/sales/edit/${row.id}`)
}

// 计算金额
const calculateAmounts = () => {
  // 端口政策从百分比转换为小数
  const portPolicyDecimal = form.port_policy / 100
  // 客户政策从百分比转换为小数
  const customerPolicyDecimal = form.customer_policy / 100

  // 端口充值款 = 户币/(1+端口政策)
  form.port_recharge = form.currency / (1 + portPolicyDecimal)

  // 应收款 = 户币/(1+客户政策)
  form.receivable_amount = form.currency / (1 + customerPolicyDecimal)

  // 计算自行垫款
  if (form.transfer_type === '对公') {
    // 对公：户币-端口充值款
    form.self_advance = form.currency - form.port_recharge
    // 对公：资方垫款为0
    form.capital_advance = 0
  } else {
    // 对私：户币-户币/(1+端口政策-0.025)
    form.self_advance = form.currency - (form.currency / (1 + portPolicyDecimal - 0.025))
    // 对私：户币-应收款-自行垫款
    form.capital_advance = form.currency - form.receivable_amount - form.self_advance
  }

  // 利润 = 应收款-端口充值款
  form.profit = form.receivable_amount - form.port_recharge

  // 计算欠款
  calculateOutstanding()
}

// 计算欠款金额
const calculateOutstanding = () => {
  // 欠款金额 = 应收款 - 到账金额
  form.outstanding_amount = form.receivable_amount - (form.arrival_amount || 0)

  // 备款 = 来款-应收款
  form.reserve_fund = (form.arrival_amount || 0) - form.receivable_amount
}

onMounted(() => {
  // 设置默认值
  const today = new Date()
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, '0')
  const day = String(today.getDate()).padStart(2, '0')
  form.transfer_time = `${year}-${month}-${day}`
  form.transfer_type = '对公'
  form.port_policy = 2 // 默认端口政策为2%
  form.customer_policy = 2 // 默认客户政策为2%
  form.business_platform = '巨量千川'
  form.audit_status = '未稽核' // 默认稽核状态为未稽核

  // 初始化计算
  calculateAmounts()
})
</script>

<style scoped>
.page-container {
  padding: 15px;
}

.sales-form {
  margin-top: 20px;
}

.el-divider {
  margin: 24px 0;
}

:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
  line-height: 100px;
}

:deep(.el-form-item__label) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-form-item) {
  margin-bottom: 22px;
}

:deep(.el-card__body) {
  padding: 20px 30px;
}

:deep(.el-tooltip__trigger) {
  width: 100%;
}

:deep(.el-input-number.is-disabled .el-input-number__decrease),
:deep(.el-input-number.is-disabled .el-input-number__increase) {
  display: none;
}

:deep(.el-input-number.is-disabled .el-input__wrapper) {
  background-color: #f5f7fa;
}

:deep(.el-form-item.is-required > .el-form-item__label::before) {
  margin-right: 6px;
}
</style>