# 4-框架统一服务层接口规范

本文档描述了SIMBEST框架中三个基础服务层接口的说明。

## 1. IGenericService（基础实体通用服务层接口）

| 接口名称 | 接口类型 | 接口含义 | 继承关系 |
|---------|---------|---------|---------|
| IGenericService | 基础服务层接口 | 提供通用实体的基础服务功能，包括：<br>- 提供通用CRUD操作接口<br>- 支持分页查询和排序<br>- 支持多种条件查询方式<br>- 支持批量操作<br>- 是系统所有服务接口的基础 | 无 |

### 主要方法说明

| 方法名称 | 方法注释 | 输入参数 | 输出结果 |
|---------|---------|---------|---------|
| getPageable() | 获取默认分页对象 | 无 | Pageable：默认配置的分页对象 |
| getPageable(int page, int size) | 获取指定页码和大小的分页对象 | page：页码（从1开始）<br>size：每页记录数 | Pageable：配置好的分页对象 |
| getPageable(int page, int size, String direction, String properties) | 获取带排序功能的分页对象 | page：页码（从1开始）<br>size：每页记录数<br>direction：排序方向（ASC或DESC）<br>properties：排序字段（多个字段用逗号分隔） | Pageable：配置好的带排序的分页对象 |
| getSpecification(T entity) | 根据实体对象创建查询规范 | entity：实体对象 | Specification<T>：查询规范 |
| getSpecification(Map<String, Object> conditions) | 根据条件Map创建查询规范 | conditions：条件Map | Specification<T>：查询规范 |
| getSpecification(Condition condition) | 根据自定义Condition对象创建查询规范 | condition：条件对象 | Specification<T>：查询规范 |
| count() | 统计实体总数 | 无 | long：实体总数 |
| count(Specification<T> specification) | 根据条件统计实体数量 | specification：查询条件 | long：符合条件的实体数量 |
| exists(PK id) | 检查指定ID的实体是否存在 | id：实体ID | boolean：是否存在 |
| findOne(PK id) | 根据ID查找实体（懒加载方式） | id：实体ID | T：实体对象 |
| findOne(Specification<T> conditions) | 根据条件查找单个实体 | conditions：查询条件 | T：实体对象，不存在返回null |
| findById(PK id) | 根据ID查找实体（即时加载方式） | id：实体ID | T：实体对象，不存在返回null |
| findAll() | 查询所有实体并分页 | 无 | Page<T>：分页后的实体列表 |
| findAll(Pageable pageable) | 根据分页参数查询所有实体 | pageable：分页参数 | Page<T>：分页后的实体列表 |
| findAll(Sort sort) | 根据排序参数查询所有实体并分页 | sort：排序参数 | Page<T>：分页后的实体列表 |
| findAllNoPage() | 查询所有实体不分页 | 无 | Iterable<T>：所有实体列表 |
| findAllNoPage(Sort sort) | 根据排序参数查询所有实体不分页 | sort：排序参数 | Iterable<T>：排序后的所有实体列表 |
| findAllByIDs(Iterable<PK> ids) | 根据ID集合查询多个实体 | ids：ID集合 | Iterable<T>：实体列表 |
| findAll(Specification<T> conditions, Pageable pageable) | 根据条件查询实体并分页 | conditions：查询条件<br>pageable：分页参数 | Page<T>：分页后的实体列表 |
| findAllNoPage(Specification<T> conditions) | 根据条件查询实体不分页 | conditions：查询条件 | Iterable<T>：符合条件的所有实体 |
| findAllNoPage(Specification<T> conditions, Sort sort) | 根据条件和排序参数查询实体不分页 | conditions：查询条件<br>sort：排序参数 | Iterable<T>：排序后的符合条件的所有实体 |
| insert(T o) | 新增实体 | o：待保存的实体对象 | T：保存后的实体对象（包含生成的ID） |
| update(T o) | 更新实体 | o：待更新的实体对象 | T：更新后的实体对象 |
| saveAndFlush(T o) | 保存实体并立即刷新 | o：待保存的实体对象 | T：保存后的实体对象 |
| saveAll(Iterable<T> entities) | 批量保存实体集合 | entities：待保存的实体集合 | List<T>：保存后的实体列表 |
| deleteById(PK id) | 根据ID删除实体 | id：实体ID | void |
| delete(T o) | 删除指定实体 | o：要删除的实体对象 | void |
| deleteAll(Iterable<? extends T> iterable) | 批量删除实体集合 | iterable：要删除的实体集合 | void |
| deleteAll() | 删除所有实体 | 无 | void |
| deleteAllByIds(Iterable<? extends PK> pks) | 根据ID集合批量删除实体 | pks：ID集合 | void |

## 2. ISystemService（系统实体通用服务层接口）

| 接口名称 | 接口类型 | 接口含义 | 继承关系 |
|---------|---------|---------|---------|
| ISystemService | 系统服务层接口 | 提供系统实体的服务功能，包括：<br>- 扩展IGenericService，继承其基础服务功能<br>- 为SystemModel类及其子类提供服务支持<br>- 自动处理创建时间和修改时间字段<br>- 作为ILogicService的基础接口 | 继承自IGenericService |

### 主要方法说明
继承自IGenericService的所有方法，并自动处理创建时间和修改时间字段。

## 3. ILogicService（业务实体通用服务层接口）

| 接口名称 | 接口类型 | 接口含义 | 继承关系 |
|---------|---------|---------|---------|
| ILogicService | 业务服务层接口 | 提供业务实体的服务功能，支持逻辑删除，包括：<br>- 扩展ISystemService，继承其基础服务功能<br>- 为LogicModel类及其子类提供服务支持<br>- 所有查询方法都只返回未被逻辑删除的数据<br>- 提供逻辑删除功能，通过设置removedTime实现<br>- 支持即时逻辑删除和计划逻辑删除 | 继承自ISystemService |

### 主要方法说明

| 方法名称 | 方法注释 | 输入参数 | 输出结果 |
|---------|---------|---------|---------|
| updateEnable(PK id, boolean enabled) | 更新实体的可用状态 | id：实体主键ID<br>enabled：是否可用 | T：更新后的实体对象 |
| scheduleLogicDelete(PK id, LocalDateTime localDateTime) | 计划在指定时间逻辑删除实体 | id：实体主键ID<br>localDateTime：计划删除时间 | void |
| scheduleLogicDelete(T entity, LocalDateTime localDateTime) | 计划在指定时间逻辑删除实体 | entity：待删除的实体对象<br>localDateTime：计划删除时间 | void |
| updateWithNull(T o) | 更新实体，允许将字段值更新为null | o：待更新的实体对象 | T：更新后的实体对象 |
| findAllNoPage() | 查询所有可用的实体不分页 | 无 | List<T>：所有可用实体的List集合 |
| findAllNoPage(Sort sort) | 根据排序条件查询所有可用的实体不分页 | sort：排序条件 | List<T>：排序后的所有可用实体的List集合 |
| findAllByIDs(Iterable<PK> ids) | 根据ID集合查询多个可用的实体 | ids：ID集合 | List<T>：匹配ID的可用实体的List集合 |
| findAllNoPage(Specification<T> conditions) | 根据条件查询可用的实体不分页 | conditions：查询条件 | List<T>：符合条件的可用实体的List集合 |
| findAllNoPage(Specification<T> conditions, Sort sort) | 根据条件和排序参数查询可用的实体不分页 | conditions：查询条件<br>sort：排序条件 | List<T>：排序后的符合条件的可用实体的List集合 |

## 继承关系

- ILogicService 继承自 ISystemService
- ISystemService 继承自 IGenericService

## 说明

1. IGenericService是所有服务层接口的基础，提供通用的服务功能
2. ISystemService在IGenericService基础上增加了系统实体的特定功能
3. ILogicService在ISystemService基础上增加了逻辑删除和业务实体的特定功能
4. 所有接口都支持事务操作，默认所有方法都在事务中执行
5. 所有接口都使用泛型参数T（实体类型）和PK（主键类型）
6. 分页查询默认使用系统配置的分页大小
7. 排序查询支持多字段排序
8. 条件查询支持动态构建查询条件
9. 逻辑删除通过设置removedTime字段实现
10. 所有查询方法都会自动过滤掉已逻辑删除的记录 