import { describe, it, expect, vi, beforeEach } from 'vitest'
import { exportToExcel } from '@/utils/excel'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'

// 模拟xlsx和file-saver
vi.mock('xlsx', () => ({
  utils: {
    json_to_sheet: vi.fn(() => ({})),
    book_new: vi.fn(() => ({})),
    book_append_sheet: vi.fn(),
    decode_range: vi.fn(() => ({ s: { r: 1 }, e: { r: 10, c: 5 } })),
    sheet_add_json: vi.fn(),
    encode_range: vi.fn(),
    encode_col: vi.fn((i) => String.fromCharCode(65 + i)) // A, B, C, ...
  },
  write: vi.fn(() => new ArrayBuffer(10))
}))

vi.mock('file-saver', () => ({
  saveAs: vi.fn()
}))

describe('Excel Export Utility', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should export data to Excel with proper headers', () => {
    // 准备测试数据
    const data = [
      { id: 1, name: '项目1', value: 100 },
      { id: 2, name: '项目2', value: 200 }
    ]
    
    const headers = [
      { header: 'ID', key: 'id', width: 10 },
      { header: '名称', key: 'name', width: 20 },
      { header: '数值', key: 'value', width: 15 }
    ]
    
    const fileName = 'test-export.xlsx'
    
    // 调用导出函数
    const result = exportToExcel(data, headers, fileName)
    
    // 验证结果
    expect(result).toBe(true)
    
    // 验证xlsx.utils.json_to_sheet被调用
    expect(XLSX.utils.json_to_sheet).toHaveBeenCalledWith(data, {
      header: ['id', 'name', 'value']
    })
    
    // 验证创建工作簿
    expect(XLSX.utils.book_new).toHaveBeenCalled()
    expect(XLSX.utils.book_append_sheet).toHaveBeenCalled()
    
    // 验证生成Excel文件
    expect(XLSX.write).toHaveBeenCalledWith(expect.anything(), { 
      bookType: 'xlsx', 
      type: 'array' 
    })
    
    // 验证保存文件
    expect(saveAs).toHaveBeenCalledWith(
      expect.any(Blob),
      'test-export.xlsx'
    )
  })
  
  it('should handle file name without .xlsx extension', () => {
    // 准备测试数据
    const data = [{ id: 1, name: 'Test' }]
    const headers = [
      { header: 'ID', key: 'id' },
      { header: '名称', key: 'name' }
    ]
    const fileName = 'test-file'
    
    // 调用导出函数
    exportToExcel(data, headers, fileName)
    
    // 验证文件名已添加.xlsx扩展名
    expect(saveAs).toHaveBeenCalledWith(
      expect.any(Blob),
      'test-file.xlsx'
    )
  })
  
  it('should handle errors during export', () => {
    // 准备测试数据
    const data = [{ id: 1 }]
    const headers = [{ header: 'ID', key: 'id' }]
    
    // 模拟错误
    vi.spyOn(console, 'error').mockImplementation(() => {})
    XLSX.utils.json_to_sheet.mockImplementationOnce(() => {
      throw new Error('Mock error')
    })
    
    // 调用导出函数
    const result = exportToExcel(data, headers, 'error-test.xlsx')
    
    // 验证结果
    expect(result).toBe(false)
    expect(console.error).toHaveBeenCalledWith(
      '导出Excel失败:',
      expect.any(Error)
    )
    
    // 验证saveAs没有被调用
    expect(saveAs).not.toHaveBeenCalled()
  })
})
