import { test, expect } from '@playwright/test';

test.describe('Login Page', () => {
  test.beforeEach(async ({ page }) => {
    // 访问登录页面
    await page.goto('/login');
  });

  test('should display login form', async ({ page }) => {
    // 验证页面标题
    await expect(page).toHaveTitle(/登录/);
    
    // 验证表单元素存在
    await expect(page.locator('form')).toBeVisible();
    await expect(page.locator('input[type="text"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
  });

  test('should show error with invalid credentials', async ({ page }) => {
    // 输入无效凭据
    await page.fill('input[type="text"]', 'invaliduser');
    await page.fill('input[type="password"]', 'invalidpassword');
    
    // 提交表单
    await page.click('button[type="submit"]');
    
    // 等待错误消息
    await page.waitForSelector('.error-message');
    
    // 验证错误消息
    const errorMessage = await page.locator('.error-message').textContent();
    expect(errorMessage).toContain('用户名或密码错误');
  });

  test('should login successfully with valid credentials', async ({ page }) => {
    // 输入有效凭据
    await page.fill('input[type="text"]', 'testuser');
    await page.fill('input[type="password"]', 'password123');
    
    // 提交表单
    await page.click('button[type="submit"]');
    
    // 等待重定向到仪表盘
    await page.waitForURL('/dashboard');
    
    // 验证仪表盘页面
    await expect(page).toHaveTitle(/仪表盘/);
    
    // 验证用户信息显示
    const userInfo = await page.locator('.user-info').textContent();
    expect(userInfo).toContain('testuser');
  });

  test('should navigate to forgot password page', async ({ page }) => {
    // 点击忘记密码链接
    await page.click('text=忘记密码?');
    
    // 等待导航到忘记密码页面
    await page.waitForURL('/forgot-password');
    
    // 验证忘记密码页面
    await expect(page).toHaveTitle(/忘记密码/);
  });

  test('should navigate to register page', async ({ page }) => {
    // 点击注册链接
    await page.click('text=注册账号');
    
    // 等待导航到注册页面
    await page.waitForURL('/register');
    
    // 验证注册页面
    await expect(page).toHaveTitle(/注册/);
  });

  test('should remember username', async ({ page }) => {
    // 输入用户名
    await page.fill('input[type="text"]', 'testuser');
    
    // 选中记住用户名复选框
    await page.check('input[type="checkbox"]');
    
    // 提交表单
    await page.fill('input[type="password"]', 'wrongpassword');
    await page.click('button[type="submit"]');
    
    // 等待错误消息
    await page.waitForSelector('.error-message');
    
    // 刷新页面
    await page.reload();
    
    // 验证用户名被记住
    const username = await page.inputValue('input[type="text"]');
    expect(username).toBe('testuser');
  });

  test('should validate form fields', async ({ page }) => {
    // 提交空表单
    await page.click('button[type="submit"]');
    
    // 验证表单验证错误
    await page.waitForSelector('.field-error');
    const errors = await page.locator('.field-error').allTextContents();
    expect(errors.some(error => error.includes('用户名'))).toBeTruthy();
    expect(errors.some(error => error.includes('密码'))).toBeTruthy();
  });

  test('should show password when toggle button is clicked', async ({ page }) => {
    // 输入密码
    await page.fill('input[type="password"]', 'password123');
    
    // 验证密码字段类型是password
    expect(await page.locator('input[name="password"]').getAttribute('type')).toBe('password');
    
    // 点击显示密码按钮
    await page.click('.show-password-button');
    
    // 验证密码字段类型变为text
    expect(await page.locator('input[name="password"]').getAttribute('type')).toBe('text');
    
    // 再次点击显示密码按钮
    await page.click('.show-password-button');
    
    // 验证密码字段类型变回password
    expect(await page.locator('input[name="password"]').getAttribute('type')).toBe('password');
  });
});
