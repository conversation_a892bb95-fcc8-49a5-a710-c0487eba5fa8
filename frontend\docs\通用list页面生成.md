# 数据列表页面生成模板

本文档提供了一个用于生成基本数据列表页面的模板，可以用于提示 AI 生成基本的 CRUD 页面。

## 变量说明

- `{MODULE_NAME}`: manage //模块名称，如 "Role"、"User" 等
- `{MODULE_PATH}`: manage //模块路径，如 "role"、"user" 等
- `{PAGE_TITLE}`: 管理员列表 //页面标题，如 "角色管理"、"用户管理" 等
- `{LIST_PAGE_TITLE}`: 管理员列表 1 //列表页面标题，如 "角色列表"、"用户列表" 等
- `{ICON_NAME}`: //菜单图标名称，如 "user"、"setting" 等
- `{API_PATH}`: manage //接口地址
- `{QUERY_PARAMS}`: //查询参数字段定义
- `{DATA_FIELDS}`: //数据字段定义
- `{CREATE_PARAMS}`: //创建参数字段定义
- `{DETAIL_FIELDS}`: //详情字段定义
- `{UPDATE_PARAMS}`: //更新参数字段定义

## 页面需求

请生成一个`{MODULE_NAME}`数据列表页面，该页面需要包含以下功能：

1. 页面标题：`{PAGE_TITLE}`
2. 搜索表单：使用`SearchForm`组件，使用方式可以参考同目录下的`组件使用说明.md` 文档
3. 数据表格：使用`DataTable`组件，使用方式可以参考同目录下的`组件使用说明.md` 文档
4. 分页功能
5. 操作按钮：新增、编辑、查看、删除等

## 路由配置要求

1. 必须在 `router/index.ts` 中添加以下路由配置：

   - 列表页面路由
   - 创建页面路由
   - 详情页面路由
   - 编辑页面路由

2. 路由配置必须包含：

   - 正确的路径
   - 组件引用
   - 路由名称
   - meta 信息（标题、图标等）
   - 子路由配置

3. 路由配置示例：

```typescript
{
  path: "/{MODULE_PATH}",
  component: Layout,
  redirect: "/{MODULE_PATH}/list",
  name: "{MODULE_NAME}",
  meta: {
    title: "{PAGE_TITLE}",
    icon: "{ICON_NAME}"
  },
  children: [
    {
      path: "list",
      name: "{MODULE_NAME}List",
      component: () => import("@/views/{MODULE_PATH}/list.vue"),
      meta: {
        title: "{LIST_PAGE_TITLE}",
        icon: "{ICON_NAME}",
      }
    },
    {
      path: "create",
      name: "Create{MODULE_NAME}",
      component: () => import("@/views/{MODULE_PATH}/create.vue"),
      meta: {
        title: "新增{MODULE_NAME}",
        hidden: true
      }
    },
    {
      path: "detail/:id",
      name: "{MODULE_NAME}Detail",
      component: () => import("@/views/{MODULE_PATH}/detail.vue"),
      meta: {
        title: "{MODULE_NAME}详情",
        hidden: true
      }
    },
    {
      path: "edit/:id",
      name: "Edit{MODULE_NAME}",
      component: () => import("@/views/{MODULE_PATH}/edit.vue"),
      meta: {
        title: "编辑{MODULE_NAME}",
        hidden: true
      }
    }
  ]
}
```

## 页面生成要求

必须生成以下所有页面：

1. 列表页面 (`list.vue`)

   - 搜索表单
   - 数据表格
   - 分页功能
   - 操作按钮（新增、编辑、查看、删除）

2. 创建页面 (`create.vue`)

   - 表单组件
   - 表单验证
   - 提交功能
   - 返回功能

3. 详情页面 (`detail.vue`)

   - 详细信息展示
   - 返回按钮
   - 数据加载状态

4. 编辑页面 (`edit.vue`)
   - 表单组件（预填充数据）
   - 表单验证
   - 提交功能
   - 取消功能

## 页面组件模板

### 列表页面模板

```vue
<template>
  <div class="{MODULE_NAME}-list-container">
    <PageHeader title="{PAGE_TITLE}">
      <template #actions>
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>新增
        </el-button>
      </template>
    </PageHeader>

    <SearchForm>...</SearchForm>
    <DataTable>...</DataTable>
  </div>
</template>
```

### 创建页面模板

```vue
<template>
  <div class="{MODULE_NAME}-create-container">
    <PageHeader title="新增{MODULE_NAME}">
      <template #actions>
        <el-button @click="handleCancel">返回</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </PageHeader>

    <el-form>...</el-form>
  </div>
</template>
```

### 详情页面模板

```vue
<template>
  <div class="{MODULE_NAME}-detail-container">
    <PageHeader title="{MODULE_NAME}详情">
      <template #actions>
        <el-button @click="handleBack">返回</el-button>
      </template>
    </PageHeader>

    <el-descriptions>...</el-descriptions>
  </div>
</template>
```

### 编辑页面模板

```vue
<template>
  <div class="{MODULE_NAME}-edit-container">
    <PageHeader title="编辑{MODULE_NAME}">
      <template #actions>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </PageHeader>

    <el-form>...</el-form>
  </div>
</template>
```

## 文件生成清单

必须生成以下文件：

1. 路由配置

   - `router/index.ts` 中的路由配置

2. API 接口

   - `api/modules/{MODULE_PATH}.ts`

3. 页面组件
   - `views/{MODULE_PATH}/list.vue`
   - `views/{MODULE_PATH}/create.vue`
   - `views/{MODULE_PATH}/detail.vue`
   - `views/{MODULE_PATH}/edit.vue`

## 生成验证清单

生成完成后，请检查以下内容：

1. 路由配置

   - [ ] 所有路由是否都已配置
   - [ ] 路由路径是否正确
   - [ ] 组件引用是否正确
   - [ ] meta 信息是否完整

2. 页面组件

   - [ ] 是否生成了所有必需的页面
   - [ ] 页面功能是否完整
   - [ ] 组件引用是否正确
   - [ ] 样式是否正确

3. API 接口
   - [ ] 是否定义了所有必需的接口
   - [ ] 接口参数是否正确
   - [ ] 类型定义是否完整

## API 接口

### 列表查询接口

接口请定义到 `/api/modules/{MODULE_PATH}.ts` 文件内，并定义以下接口：

- 接口路径：`/{API_PATH}`
- 请求方法：GET
- 请求参数：

```typescript
import { get, post, put, del } from "../core/http";
import type { PaginationParams, PaginatedResponse } from "../types";

interface {MODULE_NAME}QueryParams {
  page?: number;
  size?: number;
  {QUERY_PARAMS}
}
```

- 响应数据：

```typescript
interface {MODULE_NAME}ListResponse {
  items: {MODULE_NAME}Info[];
  total: number;
}

interface {MODULE_NAME}Info {
  id: number;
  {DATA_FIELDS}
}
```

### 创建接口

- 接口路径：`/{API_PATH}`
- 请求方法：POST
- 请求参数：

```typescript
interface Create{MODULE_NAME}Params {
  {CREATE_PARAMS}
}
```

### 详情接口

- 接口路径：`/{API_PATH}/{id}`
- 请求方法：GET
- 响应数据：

```typescript
interface {MODULE_NAME}DetailResponse {
  {DETAIL_FIELDS}
}
```

### 更新接口

- 接口路径：`/{API_PATH}/{id}`
- 请求方法：PUT
- 请求参数：

```typescript
interface Update{MODULE_NAME}Params {
  {UPDATE_PARAMS}
}
```

### 删除接口

- 接口路径：`/{API_PATH}/{id}`
- 请求方法：DELETE

## 页面生成说明 以下每个页面都需要创建

### 1. 列表页面 (list.vue)

生成列表页面，包含搜索表单、数据表格和操作按钮。

### 2. 创建页面 (create.vue)

生成创建页面，包含表单组件，用于新增数据。

### 3. 详情页面 (detail.vue)

生成详情页面，用于展示数据的详细信息。

### 4. 编辑页面 (edit.vue)

生成编辑页面，包含表单组件，用于修改数据。

## 搜索表单字段

搜索表单需要包含以下字段：

```
{SEARCH_FORM_FIELDS}
```

例如：

- 日期范围选择器：开始日期和结束日期

  ```vue
  <el-form-item label="日期范围" prop="dateRange">
    <el-date-picker
      v-model="queryParams.dateRange"
      type="daterange"
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      format="YYYY-MM-DD"
      value-format="YYYY-MM-DD"
    />
  </el-form-item>
  ```

- 输入框：关键字搜索

  ```vue
  <el-form-item label="关键字" prop="keyword">
    <el-input
      v-model="queryParams.keyword"
      placeholder="请输入关键字"
      clearable
    />
  </el-form-item>
  ```

- 下拉选择框：状态筛选
  ```vue
  <el-form-item label="状态" prop="status">
    <el-select
      v-model="queryParams.status"
      placeholder="请选择状态"
      clearable
    >
      <el-option label="状态1" value="status1" />
      <el-option label="状态2" value="status2" />
      <el-option label="状态3" value="status3" />
    </el-select>
  </el-form-item>
  ```

## 表格列配置

表格需要显示以下列：

```
{TABLE_COLUMNS}
```

例如：

- ID 列

  ```vue
  <el-table-column prop="id" label="ID" width="80" />
  ```

- 名称列

  ```vue
  <el-table-column
    prop="name"
    label="名称"
    min-width="120"
    show-overflow-tooltip
  />
  ```

- 日期列（需要格式化）

  ```vue
  <el-table-column prop="date" label="日期" min-width="100">
    <template #default="{ row }">
      {{ formatDate(row.date) }}
    </template>
  </el-table-column>
  ```

- 金额列（需要格式化）

  ```vue
  <el-table-column prop="amount" label="金额" min-width="100" align="right">
    <template #default="{ row }">
      {{ formatPrice(row.amount) }}
    </template>
  </el-table-column>
  ```

- 状态列（需要使用不同颜色的标签显示）

  ```vue
  <el-table-column prop="status" label="状态" min-width="100">
    <template #default="{ row }">
      <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
    </template>
  </el-table-column>
  ```

  - 操作（需要显示修改、查看、删除等按钮）

  ```vue
  <template #action="{ row }">
    <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
    <el-button type="primary" link @click="handleView(row)">查看</el-button>
    <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
  </template>
  ```

## 功能实现

### 页面加载

页面加载时需要调用 API 获取数据列表。

### 搜索功能

点击搜索按钮时，需要根据搜索条件重新获取数据列表。

### 重置功能

点击重置按钮时，需要清空搜索条件并重新获取数据列表。

### 分页功能

支持修改每页显示数量和页码，并重新获取数据列表。

### 新增功能

点击新增按钮时，跳转到新增页面。

### 编辑功能

点击编辑按钮时，跳转到编辑页面，并传递相应的 ID 参数。

### 查看功能

点击查看按钮时，跳转到详情页面，并传递相应的 ID 参数。

### 删除功能

点击删除按钮时，弹出确认对话框，确认后调用删除 API。

## 代码结构

请使用 Vue 3 的组合式 API（setup 语法）编写代码，并使用 TypeScript 进行类型定义。

页面结构应包含：

1. 模板部分（template）
2. 脚本部分（script setup）
3. 样式部分（style scoped）

## 组件使用

### SearchForm 组件

```vue
<SearchForm
  v-model="queryParams"
  :loading="loading"
  @search="handleSearch"
  @reset="resetQuery"
>
  <!-- 搜索表单字段 -->
</SearchForm>
```

### DataTable 组件

```vue
<DataTable
  :data="tableData"
  :loading="loading"
  :total="total"
  v-model:current-page="queryParams.page"
  v-model:page-size="queryParams.size"
  @size-change="handleSizeChange"
  @current-change="handleCurrentChange"
  @selection-change="handleSelectionChange"
>
  <!-- 表格列 -->
</DataTable>
```

## 示例代码

请参考以下示例代码，根据需求进行修改：

```vue
<template>
  <div class="{MODULE_NAME}-list-container">
    <PageHeader title="{PAGE_TITLE}">
      <template #actions>
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>新增
        </el-button>
      </template>
    </PageHeader>

    <SearchForm
      v-model="queryParams"
      :loading="loading"
      @search="handleSearch"
      @reset="resetQuery"
    >
      <!-- 搜索表单字段 -->
      <el-form-item label="{FIELD_LABEL_1}" prop="{FIELD_PROP_1}">
        <el-input
          v-model="queryParams.{FIELD_PROP_1}"
          placeholder="请输入"
          clearable
        />
      </el-form-item>

      <!-- 更多搜索字段 -->
    </SearchForm>

    <DataTable
      :data="tableData"
      :loading="loading"
      :total="total"
      v-model:current-page="queryParams.page"
      v-model:page-size="queryParams.size"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @selection-change="handleSelectionChange"
      show-selection
    >
      <!-- 表格列 -->
      <el-table-column
        prop="{COLUMN_PROP_1}"
        label="{COLUMN_LABEL_1}"
        min-width="120"
      />

      <!-- 更多表格列 -->

      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
        <el-button type="primary" link @click="handleView(row)">查看</el-button>
        <el-button type="danger" link @click="handleDelete(row)"
          >删除</el-button
        >
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import { PageHeader, SearchForm, DataTable } from '@/components';
import { get{MODULE_NAME}List, delete{MODULE_NAME} } from '@/api/modules/{MODULE_PATH}';
import type { {MODULE_NAME}QueryParams, {MODULE_NAME}Info } from '@/api/modules/{MODULE_PATH}';

const router = useRouter();

// 查询参数
const queryParams = reactive<{MODULE_NAME}QueryParams>({
  page: 1,
  size: 10,
  // 其他查询参数
});

// 表格数据
const tableData = ref<{MODULE_NAME}Info[]>([]);
// 加载状态
const loading = ref(false);
// 总记录数
const total = ref(0);
// 选中的行
const selectedRows = ref<{MODULE_NAME}Info[]>([]);

// 获取数据列表
const getList = async () => {
  loading.value = true;
  try {
    const result = await get{MODULE_NAME}List(queryParams);
    if (result && result.items) {
      tableData.value = result.items;
      total.value = result.total;
    } else if (Array.isArray(result)) {
      tableData.value = result;
      total.value = result.length;
    } else {
      tableData.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取数据失败', error);
    ElMessage.error('获取数据失败');
    tableData.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  queryParams.page = 1;
  getList();
};

// 重置
const resetQuery = () => {
  // 重置查询参数
  Object.keys(queryParams).forEach(key => {
    if (key !== 'page' && key !== 'size') {
      (queryParams as any)[key] = undefined;
    }
  });
  queryParams.page = 1;
  getList();
};

// 选择变化
const handleSelectionChange = (selection: {MODULE_NAME}Info[]) => {
  selectedRows.value = selection;
};

// 每页条数变化
const handleSizeChange = (size: number) => {
  queryParams.size = size;
  getList();
};

// 页码变化
const handleCurrentChange = (page: number) => {
  queryParams.page = page;
  getList();
};

// 新增
const handleCreate = () => {
  router.push('/{MODULE_PATH}/create');
};

// 编辑
const handleEdit = (row: {MODULE_NAME}Info) => {
  router.push(`/{MODULE_PATH}/edit/${row.id}`);
};

// 查看
const handleView = (row: {MODULE_NAME}Info) => {
  router.push(`/{MODULE_PATH}/detail/${row.id}`);
};

// 删除
const handleDelete = (row: {MODULE_NAME}Info) => {
  ElMessageBox.confirm('确认删除该记录吗？', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await delete{MODULE_NAME}(row.id);
      ElMessage.success('删除成功');
      getList();
    } catch (error) {
      console.error('删除失败', error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {
    ElMessage.info('已取消删除');
  });
};

// 页面加载时获取数据
onMounted(() => {
  getList();
});
</script>

<style scoped>
.{MODULE_NAME}-list-container {
  padding: 20px;
}
</style>
```

请根据以上模板生成一个完整的数据列表页面，替换所有占位符（如`{MODULE_NAME}`、`{PAGE_TITLE}`、`{FIELD_LABEL_1}`等）为实际的值。
